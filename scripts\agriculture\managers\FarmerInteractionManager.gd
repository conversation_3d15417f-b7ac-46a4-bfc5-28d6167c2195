# FarmerInteractionManager.gd
# 农夫交互管理器 - S+级别深度集成CoreTechnologies版本
class_name FarmerInteractionManager
extends BaseInteractionManager

# 🆕 统一状态系统支持
const UnifiedStates = preload("res://scripts/core/UnifiedCharacterStates.gd")
var current_unified_state: UnifiedStates.State = UnifiedStates.State.IDLE

## 常量定义 ##
const SEARCH_RADIUS: float = GameConstants.AgricultureConstants.FARMER_NOTIFICATION_RADIUS
const INTERACTION_RECORD_LIMIT: int = 50  # 🆕 交互记录数量限制

# 🆕 日志级别枚举
enum LogLevel { INFO, WARNING, ERROR }

## 信号定义 ##
# 🆕 管理器协调信号
signal state_sync_requested(new_state: UnifiedStates.State, source_manager: String)
signal interaction_efficiency_updated(interaction_type: String, success_rate: float)

## 属性定义 ##
# 🆕 CoreTechnologies核心技术实例 - 深度集成 (继承自基类，无需重复声明)

# 🆕 交互记录系统 - 伐木工黄金标准双重记录机制
var _交互记录系统_statistics: Dictionary = {}
var _交互记录系统_history: Array = []
var _交互记录系统_session_start_time: float

# 角色专用属性
var host: Farmer = null
var _interaction_params: Dictionary = {}
var _last_harvest_result: Dictionary = {}
var _last_plant_result: Dictionary = {}

# 🆕 交互效率缓存
var _interaction_success_rates: Dictionary = {}
var _interaction_efficiency_cache: Dictionary = {}

## 生命周期方法 ##
func initialize(farmer_ref: WorkerCharacter, task_mgr: Node, timer_mgr: Node, game_mgr: Node = null) -> bool:
    if not super.initialize(farmer_ref, task_mgr, timer_mgr, game_mgr):
        return false

    host = farmer_ref as Farmer

    # 🆕 初始化CoreTechnologies核心技术实例
    _initialize_core_technologies()

    # 🆕 初始化交互记录系统
    _初始化_交互记录系统()

    # 🆕 连接管理器协调信号
    _connect_manager_coordination_signals()

    if is_instance_valid(timer_manager) and timer_manager.has_signal("action_completed"):
        if not timer_manager.action_completed.is_connected(_on_timer_action_completed):
            timer_manager.action_completed.connect(_on_timer_action_completed)

    return true

func _initialize_core_technologies() -> void:
    """初始化CoreTechnologies核心技术实例 - 基类已初始化，这里进行农夫特化配置"""
    # 基类已经初始化了核心技术实例，这里进行农夫特化配置
    if core_interaction_recorder:
        # 农夫特化的交互记录配置
        pass

    if core_building_validator:
        # 农夫特化的建筑验证配置
        pass

    if core_state_synchronizer and is_instance_valid(host):
        # 注册农夫作为同步目标
        core_state_synchronizer.register_sync_target(host)

    print("[FarmerInteractionManager] CoreTechnologies农夫特化配置完成")

func _connect_manager_coordination_signals() -> void:
    """连接管理器协调信号"""
    # 连接计时器管理器的效率更新信号
    if is_instance_valid(timer_manager) and timer_manager.has_signal("efficiency_updated"):
        if not timer_manager.efficiency_updated.is_connected(_on_efficiency_updated):
            timer_manager.efficiency_updated.connect(_on_efficiency_updated)

    # 连接状态同步信号
    if is_instance_valid(timer_manager) and timer_manager.has_signal("state_sync_requested"):
        if not timer_manager.state_sync_requested.is_connected(_on_state_sync_requested):
            timer_manager.state_sync_requested.connect(_on_state_sync_requested)

## 核心技术实现区 ##

# 🆕 交互记录系统 - S+级别深度集成版本
func _初始化_交互记录系统() -> void:
    """初始化交互记录系统 - S+级别深度集成版本"""
    _交互记录系统_session_start_time = Time.get_unix_time_from_system()
    _交互记录系统_history.clear()
    _交互记录系统_statistics = {
        "harvest": {"total": 0, "successful": 0, "total_duration": 0.0},
        "plant": {"total": 0, "successful": 0, "total_duration": 0.0},
        "collect": {"total": 0, "successful": 0, "total_duration": 0.0},
        "store": {"total": 0, "successful": 0, "total_duration": 0.0},
        "water": {"total": 0, "successful": 0, "total_duration": 0.0},
        "fetch_water": {"total": 0, "successful": 0, "total_duration": 0.0}
    }

    # 🆕 初始化效率缓存
    _interaction_success_rates = {
        "harvest": 0.8, "plant": 0.8, "collect": 0.9,
        "store": 0.95, "water": 0.85, "fetch_water": 0.9
    }
    _interaction_efficiency_cache.clear()

func _交互记录系统_record_attempt(interaction_type: String, target: Node, params: Dictionary) -> void:
    """记录交互尝试 - S+级别深度集成版本"""
    # 🆕 优先使用CoreTechnologies的交互记录器
    if core_interaction_recorder:
        core_interaction_recorder.record_attempt(interaction_type, target, params)

    # 🆕 同时保持本地记录 - 向后兼容
    var record = {
        "id": _generate_interaction_id(),
        "type": interaction_type,
        "target_id": target.get_instance_id() if is_instance_valid(target) else 0,
        "start_time": Time.get_unix_time_from_system(),
        "end_time": 0.0,
        "success": false,
        "params": params.duplicate()
    }

    _交互记录系统_history.append(record)

    # 保持记录数量在限制内
    if _交互记录系统_history.size() > INTERACTION_RECORD_LIMIT:
        _交互记录系统_history = _交互记录系统_history.slice(-INTERACTION_RECORD_LIMIT)

func _交互记录系统_record_completion(interaction_type: String, success: bool, additional_data: Dictionary = {}) -> void:
    """记录交互完成 - S+级别深度集成版本"""
    # 🆕 优先使用CoreTechnologies的交互记录器
    if core_interaction_recorder:
        core_interaction_recorder.record_completion(interaction_type, success, additional_data)

    # 🆕 同时保持本地记录 - 向后兼容
    var record = _find_latest_incomplete_record(interaction_type)
    if record.is_empty():
        return

    var end_time = Time.get_unix_time_from_system()
    record.end_time = end_time
    record.duration = end_time - record.start_time
    record.success = success

    # 更新统计数据
    _update_interaction_statistics(interaction_type, record.duration, success)

    # 🆕 更新交互效率缓存
    _update_interaction_efficiency(interaction_type, success)

func _find_latest_incomplete_record(interaction_type: String) -> Dictionary:
    """查找最新的未完成记录"""
    for i in range(_交互记录系统_history.size() - 1, -1, -1):
        var record = _交互记录系统_history[i]
        if record.type == interaction_type and record.end_time == 0.0:
            return record
    return {}

func _update_interaction_statistics(interaction_type: String, duration: float, success: bool) -> void:
    """更新交互统计数据"""
    if not _交互记录系统_statistics.has(interaction_type):
        _交互记录系统_statistics[interaction_type] = {
            "total": 0, "successful": 0, "total_duration": 0.0
        }

    var stats = _交互记录系统_statistics[interaction_type]
    stats.total += 1
    stats.total_duration += duration

    if success:
        stats.successful += 1

func _generate_interaction_id() -> String:
    """生成唯一的交互ID"""
    return "interaction_" + str(Time.get_unix_time_from_system()) + "_" + str(randi() % 1000)

func _交互记录系统_update_record(success: bool, data: Dictionary) -> void:
    """更新最后一条交互记录"""
    if _交互记录系统_history.is_empty():
        return

    var last_record = _交互记录系统_history[-1]
    last_record.success = success
    last_record.end_time = Time.get_unix_time_from_system()
    last_record.data = data

func _交互记录系统_get_statistics() -> Dictionary:
    """获取交互统计信息"""
    return _交互记录系统_statistics.duplicate()

func get_interaction_statistics() -> Dictionary:
    """获取交互统计信息 - 标准接口"""
    return _交互记录系统_get_statistics()

## 🆕 管理器协调方法 ##

func _on_efficiency_updated(action_type: String, multiplier: float) -> void:
    """响应效率更新信号"""
    print("[FarmerInteractionManager] 收到效率更新: %s = %.2f" % [action_type, multiplier])

    # 🆕 根据效率更新调整交互成功率
    var interaction_type = action_type.replace("_action", "")
    if _interaction_success_rates.has(interaction_type):
        var base_rate = _interaction_success_rates[interaction_type]
        var adjusted_rate = min(0.98, base_rate * multiplier)  # 最高98%成功率
        _interaction_efficiency_cache[interaction_type] = adjusted_rate

        print("[FarmerInteractionManager] 交互成功率调整: %s = %.2f" % [interaction_type, adjusted_rate])

func _on_state_sync_requested(new_state: UnifiedStates.State, source_manager: String) -> void:
    """响应状态同步请求"""
    if source_manager != "FarmerInteractionManager":  # 避免自己同步自己
        update_unified_state(new_state)

func _update_interaction_efficiency(interaction_type: String, _success: bool) -> void:
    """更新交互效率"""
    if not _交互记录系统_statistics.has(interaction_type):
        return

    var stats = _交互记录系统_statistics[interaction_type]
    if stats.total > 0:
        var success_rate = float(stats.successful) / float(stats.total)
        _interaction_success_rates[interaction_type] = success_rate

        # 🆕 发送效率更新信号
        interaction_efficiency_updated.emit(interaction_type, success_rate)

func get_interaction_success_rate(interaction_type: String) -> float:
    """获取交互成功率"""
    # 🆕 优先从效率缓存获取
    if _interaction_efficiency_cache.has(interaction_type):
        return _interaction_efficiency_cache[interaction_type]

    return _interaction_success_rates.get(interaction_type, 0.8)

func apply_equipment_bonus_to_interaction(interaction_type: String, base_success: bool) -> bool:
    """应用装备加成到交互结果"""
    if base_success:
        return true  # 已经成功，无需加成

    # 🆕 根据装备效率给失败的交互第二次机会
    var success_rate = get_interaction_success_rate(interaction_type)
    var bonus_chance = (success_rate - 0.8) * 0.5  # 装备加成提供的额外机会

    if bonus_chance > 0 and randf() < bonus_chance:
        print("[FarmerInteractionManager] 装备加成救援成功: %s" % interaction_type)
        return true

    return false

# 交互位置0容差系统 - 统一实现
func start_interaction(target: Node, interaction_type: String, params: Dictionary = {}) -> bool:
    """启动交互 - 交互位置0容差：完全删除距离容差，直接调用基类精确交互"""
    if not is_instance_valid(target) or not is_instance_valid(host):
        interaction_failed.emit(interaction_type, "目标或农夫无效")
        return false
    
    # 记录交互尝试
    _交互记录系统_record_attempt(interaction_type, target, params)
    
    # 关键修复：完全删除自定义距离检查，直接调用基类方法
    # BaseInteractionManager会自动处理精确定位要求和标准距离检查
    return super.start_interaction(target, interaction_type, params)

## 业务逻辑方法 ##
func _execute_interaction(interaction_type: String, target: Node, params: Dictionary) -> bool:
    if not is_instance_valid(target):
        interaction_failed.emit(interaction_type, "目标节点无效")
        return false
    
    # 🆕 双重记录机制 - 集成到计时器管理器
    if is_instance_valid(timer_manager) and timer_manager.has_method("record_interaction_start"):
        timer_manager.record_interaction_start(interaction_type, target, params)

    # 🆕 交互记录系统 - InteractionManager记录
    _交互记录系统_record_attempt(interaction_type, target, params)
    
    match interaction_type:
        "harvest":
            return _execute_harvest_interaction(target, params)
        "plant":
            return _execute_plant_interaction(target, params)
        "collect":
            return _execute_collect_interaction(target, params)
        "store":
            return _execute_store_interaction(target, params)
        "fetch_water":
            return _execute_fetch_water_interaction(target, params)
        "water":
            return _execute_water_interaction(target, params)
        _:
            interaction_failed.emit(interaction_type, "不支持的交互类型: " + interaction_type)
            return false

func _execute_harvest_interaction(target: Node, params: Dictionary) -> bool:
    if not _validate_harvest_interaction(target, "harvest"):
        return false
    
    current_state = InteractionState.INTERACTING
    
    if is_instance_valid(worker):
        worker.change_character_state(WorkerCharacter.CharacterSpecificState.WORKING_1)
    
    _save_interaction_params(target, params, "harvest")
    return _start_action_timer("harvest_action", "harvest")

func _execute_plant_interaction(target: Node, params: Dictionary) -> bool:
    if not _validate_plant_interaction(target, "plant"):
        return false
    
    current_state = InteractionState.INTERACTING
    
    if is_instance_valid(worker):
        worker.change_character_state(WorkerCharacter.CharacterSpecificState.WORKING_2)
    
    _save_interaction_params(target, params, "plant")
    return _start_action_timer("plant_action", "plant")

func _execute_collect_interaction(target: Node, params: Dictionary) -> bool:
    if not _validate_collect_interaction(target, "collect"):
        return false
    
    current_state = InteractionState.INTERACTING
    
    if is_instance_valid(worker):
        worker.change_character_state(WorkerCharacter.CharacterSpecificState.COLLECTING)
    
    _save_interaction_params(target, params, "collect")
    return _start_action_timer("collect_action", "collect")

func _execute_store_interaction(target: Node, params: Dictionary) -> bool:
    if not _validate_store_interaction(target):
        return false
    
    current_state = InteractionState.INTERACTING
    
    if is_instance_valid(worker):
        worker.change_character_state(WorkerCharacter.CharacterSpecificState.STORING)
    
    _save_interaction_params(target, params, "store")
    return _start_action_timer("store_action", "store")

# 🆕 S+级别深度集成验证系统
func _validate_harvest_interaction(target: Node, interaction_type: String) -> bool:
    """验证收获交互 - S+级别深度集成版本"""
    # 🆕 优先使用CoreTechnologies建筑验证器
    if core_building_validator:
        if not core_building_validator.validate_building_interaction(target, interaction_type):
            interaction_failed.emit(interaction_type, "CoreTech验证失败")
            return false

    # 🆕 基础验证
    if not is_instance_valid(target):
        interaction_failed.emit(interaction_type, "农田目标无效")
        return false

    if not target.is_in_group("farmlands") and not target.is_in_group("crops"):
        interaction_failed.emit(interaction_type, "目标不是农田或作物")
        return false

    # 🆕 智能验证：使用忽略锁定状态的验证方法
    var is_harvestable = false
    if target.has_method("is_harvestable_ignoring_lock"):
        is_harvestable = target.is_harvestable_ignoring_lock()
    elif target.has_method("is_harvestable"):
        is_harvestable = target.is_harvestable()

    if not is_harvestable:
        interaction_failed.emit(interaction_type, "农田不可收获")
        return false

    return true

func _validate_plant_interaction(target: Node, interaction_type: String) -> bool:
    """验证种植交互 - S+级别深度集成版本"""
    # 🆕 优先使用CoreTechnologies建筑验证器
    if core_building_validator:
        if not core_building_validator.validate_building_interaction(target, interaction_type):
            interaction_failed.emit(interaction_type, "CoreTech验证失败")
            return false

    if not is_instance_valid(target):
        interaction_failed.emit(interaction_type, "农田目标无效")
        return false

    if not target.is_in_group("farmlands"):
        interaction_failed.emit(interaction_type, "目标不是农田")
        return false

    # 🆕 智能验证：使用忽略锁定状态的验证方法
    var can_plant = false
    if target.has_method("can_plant_ignoring_lock"):
        can_plant = target.can_plant_ignoring_lock()
    elif target.has_method("can_plant"):
        can_plant = target.can_plant()

    if not can_plant:
        interaction_failed.emit(interaction_type, "农田不可种植")
        return false

    return true

func _validate_collect_interaction(target: Node, interaction_type: String) -> bool:
    if not is_instance_valid(target):
        interaction_failed.emit(interaction_type, "收集目标无效")
        return false
    
    if not target.is_in_group("simple_dropped_items") and not target.is_in_group("crops"):
        interaction_failed.emit(interaction_type, "目标不可收集")
        return false
    
    if interaction_type == "collect" and target.has_meta("item_type"):
        var item_type = target.get_meta("item_type", "")
        if not _is_crop_type(item_type):
            interaction_failed.emit(interaction_type, "不是作物类型: " + item_type)
            return false
    
    if host and host.is_carrying_resource():
        var carrying_amount = host.get_carrying_amount()
        if carrying_amount >= 5:
            interaction_failed.emit(interaction_type, "携带数量已达上限")
            return false
    
    return true

func _validate_store_interaction(target: Node) -> bool:
    if not is_instance_valid(target):
        interaction_failed.emit("store", "存储目标无效")
        return false
    
    if not target.is_in_group("storages"):
        interaction_failed.emit("store", "目标不是存储建筑")
        return false
    
    if not host or not host.is_carrying_resource():
        interaction_failed.emit("store", "农夫未携带资源")
        return false
    
    if target.has_method("can_store_resource"):
        var carrying_type = host.get_carrying_resource_type()
        var carrying_amount = host.get_carrying_amount()
        if not target.can_store_resource(carrying_type, carrying_amount):
            interaction_failed.emit("store", "存储空间不足")
            return false
    elif target.has_method("has_storage_space") and not target.has_storage_space():
        interaction_failed.emit("store", "存储已满")
        return false
    
    return true

func _is_crop_type(item_type: String) -> bool:
    if item_type.is_empty():
        return false

    var base_item_type = item_type.replace("_item", "").to_lower()

    # 🔧 使用DataManager动态检查作物类型，而不是硬编码列表
    var game_mgr = get_node_or_null("/root/_GameManager")
    if game_mgr and game_mgr.has_method("get_data_manager"):
        var data_manager = game_mgr.get_data_manager()
        if data_manager and data_manager.has_method("has_crop_type"):
            var result = data_manager.has_crop_type(base_item_type)
            print("🔍 [农夫] 作物类型检查: %s -> %s = %s" % [item_type, base_item_type, result])
            return result

    # 如果DataManager不可用，检查农夫的可收集类型列表
    if is_instance_valid(host) and host.has_method("get_collectible_item_types"):
        var collectible_types = host.get_collectible_item_types()
        var is_collectible = item_type in collectible_types
        print("🔍 [农夫] 回退检查: %s 在可收集列表中 = %s" % [item_type, is_collectible])
        return is_collectible

    return false

func _on_timer_action_completed(_action_type: String, action_data: Dictionary) -> void:
    """计时器动作完成回调"""
    if current_state != InteractionState.INTERACTING:
        return
    
    var interaction_type = _interaction_params.get("interaction_type", "")
    var target = _interaction_params.get("target")
    
    if not is_instance_valid(target):
        _complete_interaction(false, {"reason": "目标无效"})
        return
    
    match interaction_type:
        "harvest":
            _complete_harvest_interaction(target, action_data)
        "plant":
            _complete_plant_interaction(target, action_data)
        "collect":
            _complete_collect_interaction(target, action_data)
        "store":
            _complete_store_interaction(target, action_data)
        "fetch_water":
            _complete_fetch_water_interaction(target, action_data)
        "water":
            _complete_water_interaction(target, action_data)
        _:
            _complete_interaction(false, {"reason": "未知交互类型"})

func _complete_harvest_interaction(target: Node, _action_data: Dictionary) -> void:
    """完成收获交互"""
    if not is_instance_valid(target):
        _complete_interaction(false, {"reason": "目标农田无效"})
        return
    
    var result = {"harvested_items": []}
    
    # 🆕 使用农田收获方法，只检查成功状态
    if target.has_method("harvest_crop") and is_instance_valid(host):
        var harvest_result = target.harvest_crop(host)

        if harvest_result.get("success", false):
            # 🆕 收获成功，掉落物已创建，农夫稍后会收集
            result["success"] = true
            result["message"] = "收获完成，掉落物已创建"
        else:
            var reason = harvest_result.get("reason", "未知原因")
            _complete_interaction(false, {"reason": "收获失败: " + reason})
            return
    
    else:
        _complete_interaction(false, {"reason": "农田没有收获方法"})
        return
    
    _last_harvest_result = result
    _complete_interaction(true, result)

func _complete_plant_interaction(target: Node, _action_data: Dictionary) -> void:
    """完成种植交互"""
    if not is_instance_valid(target):
        _complete_interaction(false, {"reason": "目标农田无效"})
        return
    
    var result = {"planted_crop": ""}
    
    # 执行种植逻辑
    if target.has_method("plant_crop"):
        # 🆕 优先从农田的assigned_crop_type_id获取要种植的作物类型
        var crop_type = ""
        
        # 1. 首先检查农田是否有指定的作物类型
        if target.has_method("get_assigned_crop_type_id"):
            crop_type = target.get_assigned_crop_type_id()
        
        # 2. 如果农田没有指定作物，从交互参数获取
        if crop_type.is_empty():
            crop_type = _interaction_params.get("crop_type", "")
        
        # 🆕 3. 如果仍然为空，从农夫获取默认作物类型
        if crop_type.is_empty():
            if is_instance_valid(host) and host.has_method("get_preferred_crop_type"):
                crop_type = host.get_preferred_crop_type()

            # 最终回退
            if crop_type.is_empty():
                crop_type = "wheat"

            _log_interaction("农田没有指定作物，使用默认作物: %s" % crop_type, LogLevel.WARNING)
        else:
            _log_interaction("种植指定作物: %s" % crop_type, LogLevel.INFO)
        
        var plant_result = target.plant_crop(crop_type)
        if plant_result:
            result["planted_crop"] = crop_type
            _log_interaction("成功种植作物: %s" % crop_type, LogLevel.INFO)
        else:
            _complete_interaction(false, {"reason": "种植失败: " + crop_type})
            return
    
    _last_plant_result = result
    _complete_interaction(true, result)

func _complete_collect_interaction(target: Node, _action_data: Dictionary) -> void:
    """完成收集交互"""
    if not is_instance_valid(target):
        _complete_interaction(false, {"reason": "目标物品无效"})
        return
    
    var result = {"collected_items": []}
    
    # 收集掉落物品
    if target.is_in_group("simple_dropped_items"):
        var item_data = null
        
        # 优先尝试get_item_data方法
        if target.has_method("get_item_data"):
            item_data = target.get_item_data()
        # 备用方案：从元数据获取
        elif target.has_meta("item_data"):
            item_data = target.get_meta("item_data")
        # 备用方案：从分散的元数据组装
        elif target.has_meta("item_type"):
            item_data = {
                "type": target.get_meta("item_type", ""),
                "amount": target.get_meta("amount", 1),
                "source": target.get_meta("source", "harvest")
            }
        
        if item_data and not item_data.is_empty():
            if host and host.has_method("receive_resource"):
                var item_type = item_data.get("type", "")
                var item_amount = item_data.get("amount", 1)
                
                if host.receive_resource(item_type, item_amount):
                    result["collected_items"].append(item_data)
                    target.queue_free()  # 移除掉落物品
                else:
                    _complete_interaction(false, {"reason": "无法接收物品"})
                    return
            else:
                _complete_interaction(false, {"reason": "农夫无法接收物品"})
                return
        else:
            _complete_interaction(false, {"reason": "无法获取物品数据"})
            return
    else:
        _complete_interaction(false, {"reason": "目标不是掉落物品"})
        return
    
    _complete_interaction(true, result)

func _complete_store_interaction(target: Node, _action_data: Dictionary) -> void:
    """完成存储交互"""
    if not is_instance_valid(target):
        _complete_interaction(false, {"reason": "目标存储无效"})
        return
    
    var result = {"stored_items": []}
    
    # 存储携带的资源
    if host and host.is_carrying_resource():
        var carrying_type = host.get_carrying_resource_type()
        var carrying_amount = host.get_carrying_amount()
        
        if target.has_method("store_resource"):
            if target.store_resource(carrying_type, carrying_amount):
                result["stored_items"].append({
                    "type": carrying_type,
                    "amount": carrying_amount
                })
                host.clear_carrying_resource()
            else:
                _complete_interaction(false, {"reason": "存储失败"})
                return
    
    _complete_interaction(true, result)

# 新增浇水交互完成方法
func _complete_fetch_water_interaction(target: Node, _action_data: Dictionary) -> void:
    """完成取水交互"""
    if not is_instance_valid(target):
        _complete_interaction(false, {"reason": "水井无效"})
        return
    
    var result = {"fetched_water": false}
    
    # 🆕 使用统一的基础数值+装备加成计算
    var water_amount_to_fetch = GameConstants.AgricultureConstants.WATER_BASE_FETCH_AMOUNT
    if is_instance_valid(host) and host.has_meta("water_capacity"):
        var water_capacity = host.get_meta("water_capacity", 0)
        water_amount_to_fetch = GameConstants.AgricultureConstants.calculate_effective_water_amount(
            GameConstants.AgricultureConstants.WATER_BASE_FETCH_AMOUNT, 
            water_capacity
        )
        _log_interaction("使用装备水桶容量取水: %.1f (基础: %.1f + 装备: %d)" % [
            water_amount_to_fetch, 
            GameConstants.AgricultureConstants.WATER_BASE_FETCH_AMOUNT,
            water_capacity
        ], LogLevel.INFO)
    
    # 执行取水逻辑
    if target.has_method("fetch_water"):
        var fetch_result = target.fetch_water(water_amount_to_fetch)
        if fetch_result.get("success", false):
            result["fetched_water"] = true
            
            # 农夫获得水
            if host and host.has_method("receive_resource"):
                var actual_water_amount = fetch_result.get("amount", 1)
                if host.receive_resource("water", actual_water_amount):
                    result["water_amount"] = actual_water_amount
                    _log_interaction("成功取水 %.1f 单位 (请求: %.1f)" % [actual_water_amount, water_amount_to_fetch], LogLevel.INFO)
                else:
                    _complete_interaction(false, {"reason": "无法携带水"})
                    return
        else:
            _complete_interaction(false, {"reason": "取水失败: " + fetch_result.get("reason", "未知原因")})
            return
    
    _complete_interaction(true, result)

func _complete_water_interaction(target: Node, _action_data: Dictionary) -> void:
    """完成浇水交互"""
    if not is_instance_valid(target):
        _complete_interaction(false, {"reason": "农田无效"})
        return
    
    var result = {"watered": false}
    
    # 🆕 使用统一的基础数值+装备加成计算
    var water_amount_to_use = GameConstants.AgricultureConstants.WATER_BASE_USAGE_AMOUNT
    if is_instance_valid(host) and host.has_meta("water_capacity"):
        var water_capacity = host.get_meta("water_capacity", 0)
        if water_capacity > 0:
            # 获取农田需水量
            var farmland_water_needed = 0.0
            if target.has_method("get_water_needed_amount"):
                farmland_water_needed = target.get_water_needed_amount()
            
            # 使用统一的计算方法
            water_amount_to_use = GameConstants.AgricultureConstants.get_water_usage_for_capacity(
                water_capacity, 
                farmland_water_needed
            )
            
            _log_interaction("使用装备水桶浇水: %.1f 单位 (基础: %.1f, 装备容量: %d, 农田需求: %.1f)" % [
                water_amount_to_use,
                GameConstants.AgricultureConstants.WATER_BASE_USAGE_AMOUNT,
                water_capacity,
                farmland_water_needed
            ], LogLevel.INFO)
    
    # 执行浇水逻辑
    if target.has_method("water_crop"):
        var water_result = target.water_crop(water_amount_to_use)
        if water_result.get("success", false):
            result["watered"] = true
            
            # 农夫消耗携带的水
            if host and host.is_carrying_resource() and host.get_carrying_resource_type() == "water":
                host.clear_carrying_resource()
            
            result["crop_growth_boosted"] = water_result.get("growth_boosted", false)
            result["water_used"] = water_result.get("amount_used", water_amount_to_use)
            _log_interaction("成功浇水 %.1f 单位" % water_result.get("amount_used", 0), LogLevel.INFO)
        else:
            _complete_interaction(false, {"reason": "浇水失败: " + water_result.get("reason", "未知原因")})
            return
    
    _complete_interaction(true, result)

# 修复函数签名匹配问题 - 添加默认参数
func _complete_interaction(success: bool, result: Dictionary = {}) -> void:
    """完成交互"""
    current_state = InteractionState.IDLE

    # 获取交互信息
    var interaction_type = _interaction_params.get("interaction_type", "")
    var target = _interaction_params.get("target")

    # 🆕 双重记录机制 - 记录交互完成
    _交互记录系统_record_completion(interaction_type, success, result)

    # 🆕 同时记录到计时器管理器
    if is_instance_valid(timer_manager) and timer_manager.has_method("record_interaction_completion"):
        timer_manager.record_interaction_completion(interaction_type, success, result)

    # 更新交互记录
    _交互记录系统_update_record(success, result)

    # 重置工人状态
    if is_instance_valid(worker):
        worker.change_character_state(WorkerCharacter.CharacterSpecificState.IDLE)

    # 发送信号
    if success:
        interaction_completed.emit(interaction_type, target, result)
    else:
        var reason = result.get("reason", "未知原因")
        interaction_failed.emit(interaction_type, reason)

    # 清理交互参数
    _interaction_params.clear()

func _save_interaction_params(target: Node, params: Dictionary, interaction_type: String) -> void:
    """保存交互参数"""
    _interaction_params = {
        "target": target,
        "params": params,
        "interaction_type": interaction_type,
        "start_time": Time.get_unix_time_from_system()
    }

func _start_action_timer(action_type: String, interaction_type: String) -> bool:
    """启动动作计时器 - 🔧 修复瞬间完成问题"""
    if not is_instance_valid(timer_manager):
        interaction_failed.emit(interaction_type, "计时器管理器无效")
        return false
    
    if timer_manager.has_method("start_action_timer"):
        # 🔧 关键修复：第二个参数传null，让计时器管理器使用预设的动作时间
        # 第三个参数传递交互信息
        var timer_params = {"interaction_type": interaction_type}
        
        # 🆕 添加调试日志以监控动作时间
        if timer_manager.has_method("get_action_duration"):
            var expected_duration = timer_manager.get_action_duration(action_type)
            timer_params["expected_duration"] = expected_duration
            print("[FarmerInteractionManager] 启动动作计时器: %s, 预期时长: %.1f秒" % [action_type, expected_duration])
        
        # 🔧 明确传递null作为第二个参数，确保使用预设的动作时间
        timer_manager.start_action_timer(action_type, null, timer_params)
        return true
    else:
        interaction_failed.emit(interaction_type, "无法启动动作计时器")
        return false

# 新增浇水相关交互方法
func _execute_fetch_water_interaction(target: Node, params: Dictionary) -> bool:
    """执行取水交互"""
    if not _validate_fetch_water_interaction(target, "fetch_water"):
        return false
    
    current_state = InteractionState.INTERACTING
    
    if is_instance_valid(worker):
        # 修复：取水使用COLLECTING状态，对应collecting动画（收集水资源）
        worker.change_character_state(WorkerCharacter.CharacterSpecificState.COLLECTING)
    
    _save_interaction_params(target, params, "fetch_water")
    return _start_action_timer("fetch_water_action", "fetch_water")

func _execute_water_interaction(target: Node, params: Dictionary) -> bool:
    """执行浇水交互"""
    if not _validate_water_interaction(target, "water"):
        return false
    
    current_state = InteractionState.INTERACTING
    
    if is_instance_valid(worker):
        # 修复：浇水使用WORKING_3状态，对应water动画
        worker.change_character_state(WorkerCharacter.CharacterSpecificState.WORKING_3)
    
    _save_interaction_params(target, params, "water")
    return _start_action_timer("water_action", "water")

# 新增浇水相关验证方法
func _validate_fetch_water_interaction(target: Node, interaction_type: String) -> bool:
    """验证取水交互"""
    if not is_instance_valid(target):
        interaction_failed.emit(interaction_type, "水井目标无效")
        return false
    
    if not target.is_in_group("wells"):
        interaction_failed.emit(interaction_type, "目标不是水井")
        return false
    
    # 检查水井是否可用
    if target.has_method("can_fetch_water") and not target.can_fetch_water():
        interaction_failed.emit(interaction_type, "水井不可用")
        return false
    
    # 检查农夫是否已经携带了其他资源
    if host and host.is_carrying_resource():
        var carrying_type = host.get_carrying_resource_type()
        if carrying_type != "water":
            interaction_failed.emit(interaction_type, "农夫已携带其他资源")
            return false
    
    return true

func _validate_water_interaction(target: Node, interaction_type: String) -> bool:
    """验证浇水交互"""
    if not is_instance_valid(target):
        interaction_failed.emit(interaction_type, "农田目标无效")
        return false
    
    if not target.is_in_group("farmlands"):
        interaction_failed.emit(interaction_type, "目标不是农田")
        return false
    
    # 检查农夫是否携带水
    if not host or not host.is_carrying_resource():
        interaction_failed.emit(interaction_type, "农夫未携带水")
        return false
    
    var carrying_type = host.get_carrying_resource_type()
    if carrying_type != "water":
        interaction_failed.emit(interaction_type, "农夫携带的不是水")
        return false
    
    # 检查农田是否需要浇水
    if target.has_method("needs_watering") and not target.needs_watering():
        interaction_failed.emit(interaction_type, "农田不需要浇水")
        return false
    
    return true

func _disconnect_farmer_movement_signal(callback: Callable) -> void:
    if is_instance_valid(host) and host.movement_completed.is_connected(callback):
        host.movement_completed.disconnect(callback)

# 🆕 交互日志方法
func _log_interaction(message: String, level: LogLevel = LogLevel.INFO) -> void:
    """记录交互相关的日志信息"""
    var host_name = "NoHost"
    if is_instance_valid(host):
        host_name = host.name
    var prefix = "[FarmerInteractionManager:%s] " % host_name
    match level:
        LogLevel.INFO:
            print(prefix + message)
        LogLevel.WARNING:
            push_warning(prefix + message)
        LogLevel.ERROR:
            push_error(prefix + message)

## 🆕 统一状态系统支持方法 - 旧版本已移除，使用新版本 ##

func _prepare_for_harvesting() -> void:
    """准备收获交互"""
    # 设置收获交互的特殊参数
    set_meta("interaction_mode", "harvesting")
    set_meta("priority_target_group", "harvestable_farmlands")

    # 预设收获交互参数
    _preload_harvesting_interaction_params()

    # 优化收获交互
    _optimize_harvesting_interactions()

    if OS.is_debug_build():
        print("[FarmerInteractionManager] 准备收获交互模式")

func _prepare_for_planting() -> void:
    """准备种植交互"""
    # 设置种植交互的特殊参数
    set_meta("interaction_mode", "planting")
    set_meta("priority_target_group", "empty_farmlands")

    # 预设种植交互参数
    _preload_planting_interaction_params()

    # 优化种植交互
    _optimize_planting_interactions()

    if OS.is_debug_build():
        print("[FarmerInteractionManager] 准备种植交互模式")

func _prepare_for_watering() -> void:
    """准备浇水交互"""
    # 设置浇水交互的特殊参数
    set_meta("interaction_mode", "watering")
    set_meta("priority_target_group", "farmlands_needing_water")

    # 预设浇水交互参数
    _preload_watering_interaction_params()

    # 优化浇水交互
    _optimize_watering_interactions()

    if OS.is_debug_build():
        print("[FarmerInteractionManager] 准备浇水交互模式")

func _prepare_for_collecting() -> void:
    """准备收集交互"""
    # 设置收集交互的特殊参数
    set_meta("interaction_mode", "collecting")
    set_meta("priority_target_group", "collectible_items")

    # 预设收集交互参数
    _preload_collecting_interaction_params()

    # 优化收集交互
    _optimize_collecting_interactions()

    if OS.is_debug_build():
        print("[FarmerInteractionManager] 准备收集交互模式")

func _prepare_for_storing() -> void:
    """准备存储交互"""
    # 设置存储交互的特殊参数
    set_meta("interaction_mode", "storing")
    set_meta("priority_target_group", "storage_buildings")

    # 预设存储交互参数
    _preload_storing_interaction_params()

    # 优化存储交互
    _optimize_storing_interactions()

    if OS.is_debug_build():
        print("[FarmerInteractionManager] 准备存储交互模式")

func is_interaction_valid_for_state(interaction_type: String) -> bool:
    """检查交互是否适合当前统一状态"""
    match interaction_type:
        "harvest":
            return current_unified_state == UnifiedStates.State.HARVESTING
        "plant":
            return current_unified_state == UnifiedStates.State.PLANTING
        "water":
            return current_unified_state == UnifiedStates.State.WATERING
        "collect":
            return current_unified_state == UnifiedStates.State.COLLECTING
        "store":
            return current_unified_state == UnifiedStates.State.STORING
        _:
            return true  # 其他交互类型默认允许

## 🆕 交互优化辅助方法 - S+级别实现 ##

func _preload_harvesting_interaction_params() -> void:
    """预加载收获交互参数"""
    # 预设收获交互的常用参数
    _interaction_params["harvesting_mode"] = true
    _interaction_params["auto_collect_result"] = true
    _interaction_params["prioritize_mature_crops"] = true

func _optimize_harvesting_interactions() -> void:
    """优化收获交互"""
    # 设置收获交互的特殊参数
    set_meta("interaction_mode", "harvesting")
    set_meta("priority_target_group", "harvestable_farmlands")
    set_meta("auto_collect_after_harvest", true)

    # 清理冲突的交互状态
    _clear_conflicting_interaction_states()

func _preload_planting_interaction_params() -> void:
    """预加载种植交互参数"""
    # 预设种植交互的常用参数
    _interaction_params["planting_mode"] = true
    _interaction_params["check_soil_quality"] = true
    _interaction_params["auto_water_after_plant"] = false  # 根据需要调整

func _optimize_planting_interactions() -> void:
    """优化种植交互"""
    # 设置种植交互的特殊参数
    set_meta("interaction_mode", "planting")
    set_meta("priority_target_group", "empty_farmlands")
    set_meta("prioritize_fertile_soil", true)

    # 清理冲突的交互状态
    _clear_conflicting_interaction_states()

func _preload_watering_interaction_params() -> void:
    """预加载浇水交互参数"""
    # 预设浇水交互的常用参数
    _interaction_params["watering_mode"] = true
    _interaction_params["optimize_water_usage"] = true
    _interaction_params["check_water_level"] = true

func _optimize_watering_interactions() -> void:
    """优化浇水交互"""
    # 设置浇水交互的特殊参数
    set_meta("interaction_mode", "watering")
    set_meta("priority_target_group", "farmlands_needing_water")
    set_meta("optimize_watering_sequence", true)

    # 清理冲突的交互状态
    _clear_conflicting_interaction_states()

func _preload_collecting_interaction_params() -> void:
    """预加载收集交互参数"""
    # 预设收集交互的常用参数
    _interaction_params["collecting_mode"] = true
    _interaction_params["auto_store_after_collect"] = true
    _interaction_params["prioritize_nearest_items"] = true

func _optimize_collecting_interactions() -> void:
    """优化收集交互"""
    # 设置收集交互的特殊参数
    set_meta("interaction_mode", "collecting")
    set_meta("priority_target_group", "collectible_items")
    set_meta("optimize_collection_route", true)

    # 清理冲突的交互状态
    _clear_conflicting_interaction_states()

func _preload_storing_interaction_params() -> void:
    """预设存储交互参数"""
    # 预设存储交互的常用参数
    _interaction_params["storing_mode"] = true
    _interaction_params["auto_organize"] = true
    _interaction_params["check_storage_capacity"] = true

func _optimize_storing_interactions() -> void:
    """优化存储交互"""
    # 设置存储交互的特殊参数
    set_meta("interaction_mode", "storing")
    set_meta("priority_target_group", "storage_buildings")
    set_meta("prioritize_nearest_storage", true)

    # 清理冲突的交互状态
    _clear_conflicting_interaction_states()

func _clear_conflicting_interaction_states() -> void:
    """清理冲突的交互状态"""
    # 移除可能冲突的交互模式元数据
    var current_mode = get_meta("interaction_mode", "")

    # 清理所有其他模式的元数据
    if current_mode != "harvesting":
        remove_meta("auto_collect_after_harvest")
        remove_meta("prioritize_mature_crops")

    if current_mode != "planting":
        remove_meta("prioritize_fertile_soil")
        remove_meta("auto_water_after_plant")

    if current_mode != "watering":
        remove_meta("optimize_watering_sequence")

    if current_mode != "collecting":
        remove_meta("optimize_collection_route")
        remove_meta("prioritize_nearest_items")

    if current_mode != "storing":
        remove_meta("prioritize_nearest_storage")

## 🆕 交互状态管理方法 ##

func reset_interaction_state() -> void:
    """重置交互状态"""
    # 清理所有交互模式
    remove_meta("interaction_mode")
    remove_meta("priority_target_group")

    # 清理所有优化参数
    remove_meta("auto_collect_after_harvest")
    remove_meta("prioritize_mature_crops")
    remove_meta("prioritize_fertile_soil")
    remove_meta("auto_water_after_plant")
    remove_meta("optimize_watering_sequence")
    remove_meta("optimize_collection_route")
    remove_meta("prioritize_nearest_items")
    remove_meta("prioritize_nearest_storage")

    # 清理交互参数
    _interaction_params.clear()

func get_current_interaction_mode() -> String:
    """获取当前交互模式"""
    return get_meta("interaction_mode", "")

func is_in_interaction_mode(mode: String) -> bool:
    """检查是否处于指定的交互模式"""
    return get_current_interaction_mode() == mode

## 🆕 统一状态系统支持方法 - S+级别管理器协调版本 ##

func update_unified_state(new_state: UnifiedStates.State) -> void:
    """更新统一状态 - S+级别管理器协调版本"""
    if current_unified_state == new_state:
        return

    var old_state = current_unified_state
    current_unified_state = new_state

    # 🆕 使用CoreTechnologies状态同步器
    if core_state_synchronizer:
        core_state_synchronizer.sync_state(new_state, {"source": "FarmerInteractionManager"}, self)

    # 🆕 根据状态调整交互行为
    _handle_unified_state_change(old_state, new_state)

    # 🆕 发送状态同步请求信号
    state_sync_requested.emit(new_state, "FarmerInteractionManager")

    if OS.is_debug_build():
        print("[FarmerInteractionManager] 统一状态更新: %s -> %s" % [
            UnifiedStates.get_state_name(old_state),
            UnifiedStates.get_state_name(new_state)
        ])

func get_unified_state() -> UnifiedStates.State:
    """获取当前统一状态"""
    return current_unified_state

func _handle_unified_state_change(old_state: UnifiedStates.State, new_state: UnifiedStates.State) -> void:
    """处理统一状态变化 - 智能交互优化"""
    # 🆕 根据状态变化优化交互参数
    match new_state:
        UnifiedStates.State.HARVESTING:
            _optimize_harvesting_interactions()
        UnifiedStates.State.PLANTING:
            _optimize_planting_interactions()
        UnifiedStates.State.WATERING:
            _optimize_watering_interactions()
        UnifiedStates.State.COLLECTING:
            _optimize_collecting_interactions()
        UnifiedStates.State.STORING:
            _optimize_storing_interactions()
        UnifiedStates.State.IDLE:
            # 空闲时重置交互状态
            reset_interaction_state()

    # 🆕 状态转换时的特殊处理
    _handle_state_transition_cleanup(old_state, new_state)

func _handle_state_transition_cleanup(old_state: UnifiedStates.State, new_state: UnifiedStates.State) -> void:
    """处理状态转换时的清理工作"""
    # 🆕 从工作状态转换到其他状态时，清理工作相关的交互缓存
    if UnifiedStates.is_working_state(old_state) and not UnifiedStates.is_working_state(new_state):
        print("[FarmerInteractionManager] 工作完成，清理交互缓存")
        _interaction_efficiency_cache.clear()

    # 🆕 从空闲状态转换到工作状态时，预加载交互参数
    elif old_state == UnifiedStates.State.IDLE and UnifiedStates.is_working_state(new_state):
        print("[FarmerInteractionManager] 开始工作，预加载交互参数")
        _preload_interaction_params_for_state(new_state)

func _preload_interaction_params_for_state(state: UnifiedStates.State) -> void:
    """为指定状态预加载交互参数"""
    match state:
        UnifiedStates.State.HARVESTING:
            _preload_harvesting_interaction_params()
        UnifiedStates.State.PLANTING:
            _preload_planting_interaction_params()
        UnifiedStates.State.COLLECTING:
            _preload_collecting_interaction_params()
        UnifiedStates.State.STORING:
            _preload_storing_interaction_params()

## 🆕 清理和调试方法 ##

func cleanup() -> void:
    """清理管理器资源"""
    # 🆕 清理CoreTechnologies实例
    if core_interaction_recorder:
        core_interaction_recorder.cleanup()
        core_interaction_recorder = null

    if core_building_validator:
        core_building_validator.cleanup()
        core_building_validator = null

    if core_state_synchronizer:
        core_state_synchronizer.cleanup()
        core_state_synchronizer = null

    # 清理缓存
    _interaction_efficiency_cache.clear()
    _interaction_success_rates.clear()

    # 调用基类清理
    super.cleanup()

    print("[FarmerInteractionManager] 清理完成")

func get_enhanced_debug_info() -> Dictionary:
    """获取增强的调试信息"""
    var base_info = {}
    # 基类可能没有get_debug_info方法，所以创建基础信息
    base_info["current_state"] = current_state
    base_info["current_target"] = current_target.name if is_instance_valid(current_target) else "None"
    base_info["current_interaction_type"] = current_interaction_type

    var enhanced_info = base_info.duplicate()
    enhanced_info["core_technologies"] = {
        "interaction_recorder": core_interaction_recorder != null,
        "building_validator": core_building_validator != null,
        "state_synchronizer": core_state_synchronizer != null
    }
    enhanced_info["efficiency_cache"] = _interaction_efficiency_cache.duplicate()
    enhanced_info["success_rates"] = _interaction_success_rates.duplicate()
    enhanced_info["unified_state"] = UnifiedStates.get_state_name(current_unified_state)

    return enhanced_info