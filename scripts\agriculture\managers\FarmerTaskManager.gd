# FarmerTaskManager.gd
# 农夫任务管理器 - 基于伐木工最佳实践的简化版本
class_name FarmerTaskManager
extends BaseTaskManager

# 🆕 统一状态系统支持
const UnifiedStates = preload("res://scripts/core/UnifiedCharacterStates.gd")
var current_unified_state: UnifiedStates.State = UnifiedStates.State.IDLE

## 任务类型枚举 - 阶段三简化版本 ##
enum TaskType {
    NONE, HARVEST_CROP, PLANT_SEED, COLLECT_ITEM, STORE_CROP,
    FETCH_WATER, WATER_CROP,
    RANDOM_WALK, IDLE, WAIT_FOR_STORAGE
}

# 任务类型映射已清理，不再需要向后兼容

## 常量配置 ##
const TIMEOUT_VALUES = {
    "crop_workflow": 15.0,
    "item_lock": 3.0,
    "validation_cache": 3.0
}

const TASK_PRIORITIES = {
    TaskType.STORE_CROP: TaskPriority.HIGHEST,
    TaskType.COLLECT_ITEM: TaskPriority.HIGH,  # 统一的收集任务
    TaskType.WATER_CROP: TaskPriority.HIGH,
    TaskType.FETCH_WATER: TaskPriority.NORMAL,  # 自动查找水井并取水
    TaskType.HARVEST_CROP: TaskPriority.NORMAL,
    TaskType.PLANT_SEED: TaskPriority.NORMAL,
    TaskType.WAIT_FOR_STORAGE: TaskPriority.LOW,
    TaskType.RANDOM_WALK: TaskPriority.LOWEST,
    TaskType.IDLE: TaskPriority.LOWEST
}

## 属性定义 ##
var current_task_type: int = TaskType.NONE

# 角色专用属性
var farmer: Farmer = null
var work_cycle_completed: bool = false

## 生命周期方法 ##
func _ready() -> void:
    name = "FarmerTaskManager"

func initialize(farmer_ref: WorkerCharacter, interaction_mgr: Node, timer_mgr: Node) -> bool:
    if not super.initialize(farmer_ref, interaction_mgr, timer_mgr):
        return false

    # 🔧 确保farmer和worker都指向同一个对象
    farmer = farmer_ref as Farmer
    # worker已经在基类中设置，这里确保一致性
    assert(worker == farmer, "worker和farmer必须指向同一个对象")

    _connect_interaction_signals()
    _setup_metadata_cleanup_timer()

    return true

func cleanup() -> void:
    _reset_work_cycle()
    cleanup_all_resource_locks()
    clear_building_interaction_cache()
    # 工作流清理现在由BaseTaskManager统一处理

    super.cleanup()

## 🔧 重写任务处理方法以更新current_task_type
func process_next_task() -> void:
    """处理下一个任务 - 重写以更新current_task_type"""
    if is_processing_tasks or task_queue.is_empty():
        return
    
    is_processing_tasks = true
    current_task = task_queue.pop_front()
    current_task_type = current_task.get("type", TaskType.NONE)  # 🔧 更新任务类型
    
    _start_task_timeout()
    _execute_task(current_task)

func complete_task(success: bool, result: Dictionary = {}) -> void:
    """完成当前任务 - 重写以重置current_task_type"""
    if current_task.is_empty():
        print("[FarmerTaskManager] 警告：尝试完成空任务")
        return
    
    _stop_task_timeout()
    
    var task_type = current_task.get("type", TaskType.NONE)
    current_task_type = TaskType.NONE  # 🔧 重置任务类型
    
    if not success:
        var reason = result.get("reason", "未知原因")
        print("[FarmerTaskManager] 任务失败 - 类型: %d, 原因: %s" % [task_type, reason])
        task_failed.emit(task_type, reason)
    else:
        task_completed.emit(task_type, success)
    
    current_task.clear()
    is_processing_tasks = false

    if _prevent_recursive_ai_trigger:
        return

    if not task_queue.is_empty():
        call_deferred("process_next_task")
        return
    
    if ai_enabled:
        var timer = get_tree().create_timer(0.1)
        if is_instance_valid(timer):
            timer.timeout.connect(_safe_trigger_ai_decision)
        else:
            call_deferred("_safe_trigger_ai_decision")

## 🆕 简单的AI决策触发 - 学习伐木工
func _safe_trigger_ai_decision() -> void:
    """安全触发AI决策 - 带防抖和状态检查"""
    if not _is_initialized or not ai_enabled:
        return
    
    var is_still_idle = current_task_type == TaskType.NONE or current_task_type == TaskType.IDLE
    var has_processing_tasks = is_processing_tasks or not task_queue.is_empty()  # 🔧 修复变量名冲突
    
    if is_still_idle and not has_processing_tasks:
        # 🔧 关键修复：简化农夫状态检查，移除复杂的水资源处理
        var _farmer_is_carrying = is_instance_valid(farmer) and farmer.is_carrying_resource()
        
        # 🔧 关键修复：确保_decide_next_task被正确调用
        if _has_available_work_targets():
            _decide_next_task()  # 🔧 确保这里被调用
        else:
            # 🆕 修复：使用add_random_idle_task()替代直接添加idle任务
            print("[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务")
            add_random_idle_task()
            
            # 🆕 关键修复：确保任务立即处理
            if not task_queue.is_empty() and not is_processing_tasks:
                print("[FarmerTaskManager] 立即处理新添加的空闲任务")
                call_deferred("process_next_task")

# 🆕 外部AI决策触发接口 - 响应农田等建筑的通知
func trigger_ai_decision() -> void:
    """外部触发AI决策 - 用于响应农田成熟等事件"""
    if not _is_initialized or not ai_enabled:
        return
    
    # 🆕 使用CoreTechnologies的AI频率控制器
    var farmer_state = _get_farmer_state()
    var _has_pending_tasks = not task_queue.is_empty()

    if not should_trigger_ai(farmer_state, _has_pending_tasks):
        return

    # 记录AI处理时间用于频率调整
    var start_time = Time.get_unix_time_from_system()
    
    # 检查当前是否正在执行低优先级任务（idle/随机走动）
    var current_task_data = get_current_task()
    var current_executing_task_type = current_task_data.get("type", TaskType.NONE)  # 🔧 修复变量名冲突
    var is_low_priority_task = (current_executing_task_type == TaskType.IDLE or current_executing_task_type == TaskType.RANDOM_WALK)
    
    if is_processing_tasks and is_low_priority_task:
        # 快速检查是否有重要工作
        if _has_available_work_targets():
            # 立即完成当前低优先级任务
            complete_task(true, {"reason": "被重要工作中断"})
            
            # 立即处理新任务
            call_deferred("_decide_next_task")
            return
    
    # 正常情况：延迟调用AI决策
    call_deferred("_safe_trigger_ai_decision")

    # 记录AI处理完成时间
    var end_time = Time.get_unix_time_from_system()
    record_ai_tick(end_time - start_time)

func _has_available_work_targets() -> bool:
    """快速检查是否有可用的工作目标 - 优化版本"""
    if not is_instance_valid(farmer):
        return false

    # 如果农夫携带资源，检查相应任务
    if farmer.is_carrying_resource():
        var carrying_type = farmer.get_carrying_resource_type()

        if _is_crop_type(carrying_type):
            return is_instance_valid(_find_available_storage())
        elif carrying_type == "water":
            var has_water_target = is_instance_valid(_find_farmland_needing_water())
            if not has_water_target:
                farmer.clear_carrying_resource()
            return has_water_target
        else:
            return false

    # 不携带资源时，快速检查是否有任何可用目标
    # 注意：这里只做快速检查，不进行锁定
    var collectible_items = _find_collectible_dropped_resources()
    if not collectible_items.is_empty():
        return true

    var harvestable_farmlands = _find_harvestable_farmlands()
    if not harvestable_farmlands.is_empty():
        return true

    var plantable_farmlands = _find_plantable_farmlands()
    if not plantable_farmlands.is_empty():
        return true

    var farmlands_needing_water = _find_all_farmlands_needing_water()
    if not farmlands_needing_water.is_empty():
        return true

    return false

## 前置资源锁定机制 - 核心技术1 ##
func lock_resource_target(target: Node, lock_type: String) -> bool:
    """使用BaseTaskManager的统一锁定接口"""
    return lock_target(target, lock_type, TIMEOUT_VALUES)

func is_resource_locked_by_others(target: Node, lock_type: String) -> bool:
    """使用BaseTaskManager的统一锁定检查接口"""
    return is_locked_by_others(target, lock_type, TIMEOUT_VALUES)

func cleanup_all_resource_locks() -> void:
    """使用BaseTaskManager的统一锁定清理接口"""
    cleanup_worker_locks()

func unlock_resource_target(target: Node, lock_type: String) -> bool:
    """使用BaseTaskManager的统一解锁接口"""
    return unlock_target(target, lock_type)

## 智能负载均衡算法 - 核心技术2 ##
func calculate_target_priority_score(target: Node) -> float:
    """使用BaseTaskManager的统一评分算法"""
    return calculate_target_score(target)

## 动作连贯性优化 - 核心技术3 ##
func execute_immediate_task_chain(interaction_type: String) -> bool:
    """使用BaseTaskManager的统一动作连贯性框架"""
    var chain_config = {
        "chain_map": {
            "harvest": "collect",
            "collect": "storage",
            "plant": "watering_check",
            "store": "workflow_complete",
            "water": "next_task"
        }
    }
    return immediate_task_chain(interaction_type, chain_config)

func request_collect_task_immediate() -> bool:
    """立即请求收集任务 - 已废弃，由专用动作连贯性方法处理"""
    print("[FarmerTaskManager] 旧的立即收集方法被调用，重定向到专用方法")
    return _动作连贯性_request_collect_immediate()

func request_storage_task_immediate() -> bool:
    """立即请求存储任务"""
    if not farmer.is_carrying_resource():
        return false

    # 检查是否已有存储任务
    if _has_storage_task():
        return true

    var nearest_storage = find_nearest_storage()
    if is_instance_valid(nearest_storage):
        add_task(TaskType.STORE_CROP, {"target_storage": nearest_storage}, TaskPriority.HIGHEST)
        return true

    return false

func request_watering_check_immediate() -> bool:
    """检查是否需要立即浇水"""
    # 种植完成后，检查刚种植的农田是否需要浇水
    var current_task_data = get_current_task()
    var planted_farmland = current_task_data.get("params", {}).get("target_farmland")

    if is_instance_valid(planted_farmland) and planted_farmland.has_method("needs_watering"):
        if planted_farmland.needs_watering():
            # 检查农夫是否有水
            if farmer.is_carrying_resource() and farmer.get_carrying_resource_type() == "water":
                add_task(TaskType.WATER_CROP, {"target_farmland": planted_farmland}, TaskPriority.HIGH)
                return true
            else:
                # 需要先取水
                var nearest_well = _find_nearest_well()
                if is_instance_valid(nearest_well):
                    add_task(TaskType.FETCH_WATER, {"target_well": nearest_well}, TaskPriority.HIGH)
                    _set_temporary_metadata("pending_water_target", planted_farmland, 60.0)
                    return true

    return false

func request_workflow_complete_immediate() -> bool:
    """完成工作流程循环"""
    work_cycle_completed = true
    if is_instance_valid(farmer) and farmer.has_method("complete_farm_workflow"):
        farmer.complete_farm_workflow()
    return true

func request_next_task_immediate() -> bool:
    """触发下一个任务决策"""
    call_deferred("_decide_next_task")
    return true

## 🆕 专用动作连贯性方法 - 优先级最高 ##
func _动作连贯性_request_collect_immediate() -> bool:
    """专用的动作连贯性收集方法 - 优先级最高"""
    print("[FarmerTaskManager] 动作连贯性 - 专用收集方法被调用")

    # 🔧 关键修复：立即标记和收集，不使用await
    call_deferred("_immediate_mark_and_collect")
    return true

func fallback_to_deferred_processing(_interaction_type: String) -> void:
    """动作连贯性降级机制 - 延迟到下一帧处理"""
    call_deferred("_decide_next_task")

# 🗑️ 已删除延迟收集相关方法，统一使用动作连贯性机制

func _has_storage_task() -> bool:
    """检查是否已有存储任务"""
    for task in task_queue:
        if task.get("type") == TaskType.STORE_CROP:
            return true
    return false

func _find_nearest_dropped_crop() -> Node:
    """查找最近的属于我的掉落作物 - 简化方案"""
    if not is_instance_valid(farmer):
        return null

    var dropped_items = get_tree().get_nodes_in_group("simple_dropped_items")
    var my_farmer_id = farmer.get_instance_id()
    var nearest_item = null
    var nearest_distance = INF

    for item in dropped_items:
        if not is_instance_valid(item):
            continue

        # 🔧 简化方案：只查找属于我的掉落物
        var harvester_id = item.get_meta("harvester_id", -1)
        if harvester_id != my_farmer_id:
            continue

        # 检查是否是作物类型
        var item_type = item.get_meta("item_type", "")
        if item.has_method("get_item_type"):
            item_type = item.get_item_type()

        if item_type in farmer.get_collectible_item_types():
            var distance = farmer.global_position.distance_to(item.global_position)
            if distance < nearest_distance:
                nearest_distance = distance
                nearest_item = item

    return nearest_item

func _request_storage_task_deferred() -> void:
    """延迟请求存储任务"""
    if farmer.is_carrying_resource() and _should_create_storage_task():
        var nearest_storage = find_nearest_storage()
        if is_instance_valid(nearest_storage):
            add_task(TaskType.STORE_CROP, {"target_storage": nearest_storage}, TaskPriority.HIGHEST)

## 重写基类抽象方法 ##
func _get_random_walk_task_type() -> int:
    return TaskType.RANDOM_WALK

func _get_idle_task_type() -> int:
    return TaskType.IDLE

func _get_storage_task_type() -> int:
    return TaskType.STORE_CROP

## 基础设施方法 ##
func _connect_interaction_signals() -> void:
    if interaction_manager and interaction_manager.has_signal("interaction_completed"):
        if not interaction_manager.interaction_completed.is_connected(_on_interaction_completed):
            interaction_manager.interaction_completed.connect(_on_interaction_completed)
        
        if interaction_manager.has_signal("interaction_failed"):
            if not interaction_manager.interaction_failed.is_connected(_on_interaction_failed):
                interaction_manager.interaction_failed.connect(_on_interaction_failed)

# 工作流状态追踪 - 简化实现，直接使用BaseTaskManager接口
# 注意：所有工作流方法已简化，直接使用基类的统一接口

## 建筑交互验证 - 核心技术5 ##
func clear_building_interaction_cache() -> void:
    """清理建筑交互验证缓存 - 使用BaseTaskManager接口"""
    # 清理现在由BaseTaskManager统一处理
    pass

func _reset_work_cycle() -> void:
    work_cycle_completed = false
    
    if is_instance_valid(farmer) and farmer.has_method("complete_farm_workflow"):
        farmer.complete_farm_workflow()

func validate_target(target: Node) -> bool:
    return is_instance_valid(target)

func set_worker_moving_state() -> void:
    if is_instance_valid(farmer):
        farmer.change_character_state(WorkerCharacter.CharacterSpecificState.MOVING)

func set_worker_idle_state() -> void:
    """设置工人空闲状态 - 🆕 智能状态设置，避免移动中的状态冲突"""
    if is_instance_valid(farmer):
        # 🆕 关键修复：只有在非移动状态时才设置idle，避免打断移动动画
        if not farmer.is_moving():
            farmer.change_character_state(WorkerCharacter.CharacterSpecificState.IDLE)
        # 如果正在移动，让移动完成后自然转为idle状态

## 查找方法 ##
func _find_harvestable_farmlands() -> Array:
    var harvestable_farmlands = []
    var all_farmlands = get_tree().get_nodes_in_group("farmlands")
    
    for farmland in all_farmlands:
        if not is_instance_valid(farmland):
            continue
        
        if farmland.has_method("is_harvestable") and farmland.is_harvestable():
            if not is_resource_locked_by_others(farmland, "crop"):
                harvestable_farmlands.append(farmland)
    
    return harvestable_farmlands

func _find_plantable_farmlands() -> Array:
    var plantable_farmlands = []
    var assigned_farmlands = []  # 🆕 优先级：有指定作物的农田
    var unassigned_farmlands = []  # 🆕 低优先级：没有指定作物的农田
    var all_farmlands = get_tree().get_nodes_in_group("farmlands")
    
    for farmland in all_farmlands:
        if not is_instance_valid(farmland):
            continue
        
        if farmland.has_method("can_plant") and farmland.can_plant():
            if not is_resource_locked_by_others(farmland, "crop"):
                # 🆕 分类农田：有指定作物的优先
                if farmland.has_method("get_assigned_crop_type_id"):
                    var assigned_crop = farmland.get_assigned_crop_type_id()
                    if not assigned_crop.is_empty():
                        assigned_farmlands.append(farmland)
                    else:
                        unassigned_farmlands.append(farmland)
                else:
                    unassigned_farmlands.append(farmland)
    
    # 🆕 优先返回有指定作物的农田，然后是无指定作物的农田
    plantable_farmlands.append_array(assigned_farmlands)
    plantable_farmlands.append_array(unassigned_farmlands)
    
    # 简化日志输出
    
    return plantable_farmlands

func _find_collectible_dropped_resources() -> Array:
    """查找属于我的掉落资源 - 简化方案"""
    var collectible_items = []
    var dropped_items = get_tree().get_nodes_in_group("simple_dropped_items")
    var my_farmer_id = farmer.get_instance_id()

    for item in dropped_items:
        if not is_instance_valid(item):
            continue

        # 🔧 简化方案：只收集属于我的掉落物
        var harvester_id = item.get_meta("harvester_id", -1)
        if harvester_id != my_farmer_id:
            continue

        var item_type = item.get_meta("item_type", "")
        if _is_crop_type(item_type):
            collectible_items.append(item)

    return collectible_items

func _find_best_farmland(candidates: Array) -> Node:
    if candidates.is_empty():
        return null
    
    var best_target = null
    var best_score = -1.0
    
    for target in candidates:
        if not is_instance_valid(target):
            continue
        
        var score = calculate_target_priority_score(target)
        if score > best_score:
            best_score = score
            best_target = target
    
    return best_target

func _find_available_storage() -> Node:
    var storages = get_tree().get_nodes_in_group("storages")
    
    for storage in storages:
        if not is_instance_valid(storage):
            continue
        
        if storage.has_method("can_store_resource"):
            var carrying_type = farmer.get_carrying_resource_type() if farmer.is_carrying_resource() else ""
            var carrying_amount = farmer.get_carrying_amount() if farmer.is_carrying_resource() else 0
            if storage.can_store_resource(carrying_type, carrying_amount):
                return storage
        elif storage.has_method("has_storage_space") and storage.has_storage_space():
            return storage
    
    return null

func find_nearest_dropped_item() -> Node:
    var dropped_items = _find_collectible_dropped_resources()
    return _find_best_farmland(dropped_items)

func _find_nearest_well() -> Node:
    var wells = get_tree().get_nodes_in_group("wells")
    
    if wells.is_empty():
        return null
    
    var best_well = null
    var best_distance = INF
    
    for well in wells:
        if not is_instance_valid(well):
            continue
        
        if well.has_method("can_fetch_water") and not well.can_fetch_water():
            continue
        
        var distance = farmer.global_position.distance_to(well.global_position)
        if distance < best_distance:
            best_distance = distance
            best_well = well
    
    return best_well

func _find_recently_planted_farmland() -> Node:
    """查找最近种植的农田"""
    return _find_farmland_needing_water()

func _is_crop_type(item_type: String) -> bool:
    """检查是否为作物类型 - 使用与交互管理器相同的逻辑"""
    if item_type.is_empty():
        return false

    var base_item_type = item_type.replace("_item", "").to_lower()

    # 🔧 使用DataManager动态检查作物类型
    var game_manager = get_node_or_null("/root/_GameManager")
    if game_manager and game_manager.has_method("get_data_manager"):
        var data_manager = game_manager.get_data_manager()
        if data_manager and data_manager.has_method("has_crop_type"):
            var result = data_manager.has_crop_type(base_item_type)
            return result

    # 如果DataManager不可用，检查农夫的可收集类型列表
    if is_instance_valid(farmer) and farmer.has_method("get_collectible_item_types"):
        var collectible_types = farmer.get_collectible_item_types()
        return item_type in collectible_types

    # 最终回退到硬编码列表
    return base_item_type in ["wheat", "corn", "tomato", "carrot", "potato", "turnip", "radish"]

## AI决策核心 ##
func _decide_next_task() -> void:
    if not _is_initialized:
        return
    
    # 1. 紧急情况处理
    if _handle_emergency_situations():
        return
    
    # 2. 工作任务收集
    var work_tasks = _gather_work_tasks()
    
    if not work_tasks.is_empty():
        for task in work_tasks.slice(0, 2):
            add_task(task["type"], task["params"], _get_base_task_priority(task["type"]))
        return
    
    # 3. 随机空闲任务
    add_random_idle_task()

func _handle_emergency_situations() -> bool:
    if not is_instance_valid(farmer):
        return false
    
    if farmer.is_carrying_resource():
        if _should_create_storage_task():
            return _create_storage_task_if_needed(TaskPriority.HIGHEST)
        else:
            log_non_crop_resource_skip("紧急情况检查")
    
    return false

func _gather_work_tasks() -> Array:
    """收集工作任务 - 模块化重构版本"""
    var tasks: Array = []

    # 1. 优先处理携带资源的情况
    tasks = _handle_carrying_resource_tasks()
    if not tasks.is_empty():
        return tasks

    # 2. 处理空闲状态的工作任务
    tasks = _handle_idle_state_tasks()
    return tasks

## 🆕 模块化决策方法 - 阶段二重构 ##

func _handle_carrying_resource_tasks() -> Array:
    """处理携带资源时的任务决策"""
    var tasks: Array = []

    if not farmer.is_carrying_resource():
        return tasks

    var carrying_type = farmer.get_carrying_resource_type()

    # 处理携带作物的情况
    if _is_crop_type(carrying_type):
        return _create_crop_storage_task()

    # 处理携带水的情况
    elif carrying_type == "water":
        return _create_watering_task()

    # 处理其他资源
    else:
        log_non_crop_resource_skip("任务收集")
        return tasks  # 返回空任务，让农夫空闲

func _create_crop_storage_task() -> Array:
    """创建作物存储任务"""
    var tasks: Array = []
    var storage = _find_available_storage()

    if is_instance_valid(storage):
        tasks.append({
            "type": TaskType.STORE_CROP,
            "params": {"target_storage": storage}
        })

    return tasks

func _create_watering_task() -> Array:
    """创建浇水任务"""
    var tasks: Array = []
    var farmland_to_water = _find_farmland_needing_water()

    if is_instance_valid(farmland_to_water):
        if not is_resource_locked_by_others(farmland_to_water, "crop"):
            if lock_resource_target(farmland_to_water, "crop"):
                tasks.append({
                    "type": TaskType.WATER_CROP,
                    "params": {
                        "target_farmland": farmland_to_water,
                        "pre_locked": true
                    }
                })
    else:
        # 没有需要浇水的农田，清除水资源
        farmer.clear_carrying_resource()

    return tasks
func _handle_idle_state_tasks() -> Array:
    """处理空闲状态时的工作任务决策"""
    var tasks: Array = []

    # 🔧 关键修复：任务优先级调整，收集由动作连贯性专门处理
    # 任务优先级：收获 > 种植 > 浇水 > 收集掉落物（仅作为备用）

    # 1. 收获任务
    tasks = _try_create_harvest_task()
    if not tasks.is_empty():
        return tasks

    # 2. 种植任务
    tasks = _try_create_planting_task()
    if not tasks.is_empty():
        return tasks

    # 3. 浇水任务（取水+浇水）
    tasks = _try_create_fetch_water_task()
    if not tasks.is_empty():
        return tasks

    # 4. 收集掉落物任务（仅作为备用，优先由动作连贯性处理）
    tasks = _try_create_collection_task()
    return tasks

func _try_create_collection_task() -> Array:
    """备用收集任务 - 仅在动作连贯性失效时使用"""
    var tasks: Array = []

    # 🔧 备用机制：只在没有其他任务时才收集掉落物
    print("[FarmerTaskManager] 备用收集机制被触发")

    var collectible_items = _find_collectible_dropped_resources()
    if not collectible_items.is_empty():
        var farmer_position = farmer.global_position

        # 按距离排序，收集最近的
        collectible_items.sort_custom(func(a, b):
            var dist_a = farmer_position.distance_to(a.global_position)
            var dist_b = farmer_position.distance_to(b.global_position)
            return dist_a < dist_b
        )

        var nearest_item = collectible_items[0]
        if is_instance_valid(nearest_item):
            print("[FarmerTaskManager] 备用收集：%s" % nearest_item.get_meta("item_type", "unknown"))
            tasks.append({
                "type": TaskType.COLLECT_ITEM,
                "params": {"target_item": nearest_item}
            })

    return tasks

func _try_create_harvest_task() -> Array:
    """尝试创建收获任务 - 原子化锁定"""
    var tasks: Array = []
    var harvestable_farmlands = _find_harvestable_farmlands()

    if not harvestable_farmlands.is_empty():
        # 原子化操作：直接尝试锁定最佳目标
        for farmland in harvestable_farmlands:
            if is_instance_valid(farmland) and lock_resource_target(farmland, "crop"):
                tasks.append({
                    "type": TaskType.HARVEST_CROP,
                    "params": {"target_farmland": farmland, "pre_locked": true}
                })
                break  # 成功锁定一个就够了

    return tasks

func _try_create_planting_task() -> Array:
    """尝试创建种植任务 - 原子化锁定"""
    var tasks: Array = []
    var plantable_farmlands = _find_plantable_farmlands()

    if not plantable_farmlands.is_empty():
        # 原子化操作：直接尝试锁定可种植的农田
        for farmland in plantable_farmlands:
            if is_instance_valid(farmland) and _can_afford_planting_for_farmland(farmland):
                if lock_resource_target(farmland, "crop"):
                    tasks.append({
                        "type": TaskType.PLANT_SEED,
                        "params": {"target_farmland": farmland, "pre_locked": true}
                    })
                    break  # 成功锁定一个就够了

    return tasks

func _try_create_fetch_water_task() -> Array:
    """尝试创建取水任务（用于浇水）- 原子化锁定"""
    var tasks: Array = []
    var farmlands_needing_water = _find_all_farmlands_needing_water()

    if not farmlands_needing_water.is_empty():
        var available_well = _find_nearest_well()
        if is_instance_valid(available_well):
            # 原子化操作：直接尝试锁定需要浇水的农田
            for farmland in farmlands_needing_water:
                if is_instance_valid(farmland) and lock_resource_target(farmland, "crop"):
                    tasks.append({
                        "type": TaskType.FETCH_WATER,
                        "params": {
                            "target_well": available_well,
                            "target_farmland": farmland,
                            "pre_locked": true
                        }
                    })
                    break  # 成功锁定一个就够了

    return tasks

func _get_base_task_priority(task_type: int) -> int:
    return TASK_PRIORITIES.get(task_type, TaskPriority.LOWEST)

## 任务执行方法 ##
func _execute_task(task: Dictionary) -> void:
    var task_type = task.get("type", TaskType.NONE)
    var params = task.get("params", {})
    
    match task_type:
        TaskType.HARVEST_CROP:
            _execute_harvest_crop_task(params)
        TaskType.PLANT_SEED:
            _execute_plant_seed_task(params)
        TaskType.COLLECT_ITEM:
            _execute_collect_item_task(params)
        TaskType.STORE_CROP:
            _execute_store_crop_task(params)
        TaskType.FETCH_WATER:
            _execute_fetch_water_task(params)  # 包含原FIND_WELL功能
        TaskType.WATER_CROP:
            _execute_water_crop_task(params)
        TaskType.WAIT_FOR_STORAGE:
            _execute_wait_for_storage_task(params)
        TaskType.RANDOM_WALK:
            execute_random_walk_task(params)
        TaskType.IDLE:
            execute_idle_task(params)
        _:
            complete_task(false, {"reason": "未知任务类型"})

func _execute_harvest_crop_task(params: Dictionary) -> void:
    var target_farmland = params.get("target_farmland")
    var is_pre_locked = params.get("pre_locked", false)
    
    if not validate_target(target_farmland):
        if is_pre_locked and is_instance_valid(target_farmland):
            unlock_resource_target(target_farmland, "crop")
        complete_task(false, {"reason": "没有找到可收获的农田"})
        return
    
    # 开始收获工作流程
    var workflow_id = start_workflow(target_farmland, "harvest_cycle")
    update_workflow_stage(workflow_id, "harvesting", {"farmland": target_farmland.name})

    if is_instance_valid(farmer) and farmer.has_method("start_farm_workflow"):
        farmer.start_farm_workflow(target_farmland, "harvest")

    _start_interaction(target_farmland, "harvest", params, target_farmland)

func _execute_plant_seed_task(params: Dictionary) -> void:
    var target_farmland = params.get("target_farmland")
    var is_pre_locked = params.get("pre_locked", false)
    
    if not validate_target(target_farmland):
        if is_pre_locked and is_instance_valid(target_farmland):
            unlock_resource_target(target_farmland, "crop")
        complete_task(false, {"reason": "没有找到可种植的农田"})
        return
    
    # 开始种植工作流程
    var workflow_id = start_workflow(target_farmland, "plant_cycle")
    update_workflow_stage(workflow_id, "planting", {"farmland": target_farmland.name})

    if is_instance_valid(farmer) and farmer.has_method("start_farm_workflow"):
        farmer.start_farm_workflow(target_farmland, "plant")

    _start_interaction(target_farmland, "plant", params, target_farmland)

func _execute_collect_item_task(params: Dictionary) -> void:
    """统一的收集任务 - 简化方案：只收集属于我的掉落物"""
    # 确定目标物品
    var target_item = params.get("target_item")  # 掉落物品
    if not target_item:
        target_item = params.get("target_crop")  # 作物

    # 🔧 简化方案：基础验证即可
    if not is_instance_valid(target_item):
        complete_task(false, {"reason": "目标物品无效"})
        return

    # 检查物品是否仍在场景中
    if target_item.has_method("is_inside_tree") and not target_item.is_inside_tree():
        complete_task(false, {"reason": "物品已被移除"})
        return

    # 🔧 简化方案：验证是否属于我的掉落物
    var my_farmer_id = farmer.get_instance_id()
    var harvester_id = target_item.get_meta("harvester_id", -1)
    if harvester_id != -1 and harvester_id != my_farmer_id:
        complete_task(false, {"reason": "不是我的掉落物"})
        return

    if not validate_target(target_item):
        complete_task(false, {"reason": "目标物品验证失败"})
        return

    _start_interaction(target_item, "collect", params, target_item)

func _execute_store_crop_task(params: Dictionary) -> void:
    var target_storage = params.get("target_storage")
    if not target_storage:
        target_storage = _find_available_storage()
    
    if not validate_target(target_storage):
        complete_task(false, {"reason": "目标存储无效"})
        return
    
    if not farmer.is_carrying_resource():
        complete_task(false, {"reason": "农夫未携带资源"})
        return
    
    # 🚨 关键修复：检查携带的资源是否为作物类型
    if not _should_create_storage_task():
        log_non_crop_resource_skip("存储任务执行")
        complete_task(false, {"reason": "携带的不是作物资源"})
        return
    
    _start_interaction(target_storage, "store", params, target_storage)

func _execute_fetch_water_task(params: Dictionary) -> void:
    """取水任务 - 自动查找水井并取水"""
    var target_well = params.get("target_well")
    var target_farmland = params.get("target_farmland")  # 🔧 获取目标农田
    var is_pre_locked = params.get("pre_locked", false)

    # 如果没有指定水井，自动查找最近的水井
    if not is_instance_valid(target_well):
        target_well = _find_nearest_well()
        if not is_instance_valid(target_well):
            if is_pre_locked and is_instance_valid(target_farmland):
                unlock_resource_target(target_farmland, "crop")
            complete_task(false, {"reason": "没有找到可用的水井"})
            return

    if not validate_target(target_well):
        if is_pre_locked and is_instance_valid(target_farmland):
            unlock_resource_target(target_farmland, "crop")
        complete_task(false, {"reason": "水井目标无效"})
        return
    
    if target_well.has_method("can_fetch_water") and not target_well.can_fetch_water():
        if is_pre_locked and is_instance_valid(target_farmland):
            unlock_resource_target(target_farmland, "crop")
        complete_task(false, {"reason": "水井不可用"})
        return
    
    # 🔧 保存目标农田信息，用于取水完成后的浇水任务
    if target_farmland:
        _set_temporary_metadata("pending_water_target", target_farmland, 60.0)  # 60秒超时
        _set_temporary_metadata("pending_water_pre_locked", is_pre_locked, 60.0)
    
    if is_instance_valid(farmer) and farmer.has_method("start_farm_workflow"):
        farmer.start_farm_workflow(target_well, "fetch_water")
    
    _start_interaction(target_well, "fetch_water", params, target_well)

func _execute_water_crop_task(params: Dictionary) -> void:
    var target_farmland = params.get("target_farmland")
    
    if not validate_target(target_farmland):
        complete_task(false, {"reason": "农田目标无效"})
        return
    
    if not farmer.is_carrying_resource() or farmer.get_carrying_resource_type() != "water":
        complete_task(false, {"reason": "农夫没有携带水"})
        return
    
    # 🚨 修复：检查农田状态，避免给成熟农田浇水
    if target_farmland.has_method("get_current_state_key"):
        var state_key = target_farmland.get_current_state_key()
        if state_key == "OCCUPIED_READY_FOR_HARVEST":
            complete_task(false, {"reason": "农田已成熟，无需浇水"})
            return
    
    if target_farmland.has_method("needs_watering") and not target_farmland.needs_watering():
        complete_task(false, {"reason": "农田不需要浇水"})
        return
    
    if is_instance_valid(farmer) and farmer.has_method("start_farm_workflow"):
        farmer.start_farm_workflow(target_farmland, "watering")
    
    _start_interaction(target_farmland, "water", params, target_farmland)



func _execute_wait_for_storage_task(params: Dictionary) -> void:
    if is_instance_valid(farmer) and farmer.is_carrying_resource():
        farmer.change_character_state(WorkerCharacter.CharacterSpecificState.CARRYING)
    
    var storage = _find_available_storage()
    if is_instance_valid(storage):
        add_task(TaskType.STORE_CROP, {"target_storage": storage}, TaskPriority.HIGHEST)
        complete_task(true, {"reason": "找到存储建筑"})
    else:
        var wait_time = params.get("wait_time", 2.0)
        var max_wait_cycles = params.get("max_wait_cycles", 10)
        var current_wait_cycle = params.get("current_wait_cycle", 0) + 1
        
        if current_wait_cycle >= max_wait_cycles:
            add_random_idle_task()
            complete_task(true, {"reason": "等待超时，转为空闲状态"})
        else:
            add_task(TaskType.WAIT_FOR_STORAGE, {
                "wait_time": wait_time,
                "current_wait_cycle": current_wait_cycle,
                "max_wait_cycles": max_wait_cycles
            }, TaskPriority.NORMAL)
            complete_task(true, {"reason": "继续等待存储"})

func execute_random_walk_task(params: Dictionary) -> void:
    if not is_instance_valid(farmer):
        complete_task(false, {"reason": "角色引用无效"})
        return
    
    var target_position = params.get("target_position", _calculate_random_walk_position())
    set_worker_moving_state()
    
    if farmer.has_method("move_to"):
        if not farmer.movement_completed.is_connected(_on_farmer_random_walk_completed):
            farmer.movement_completed.connect(_on_farmer_random_walk_completed)
        
        var success = farmer.move_to(target_position, {"use_navigation": true, "is_random_walk": true})
        if not success:
            var fallback_position = _calculate_random_walk_position()
            success = farmer.move_to(fallback_position, {"use_navigation": true, "is_random_walk": true})
            
            if not success:
                _disconnect_farmer_movement_signal(_on_farmer_random_walk_completed)
                set_worker_idle_state()
                complete_task(false, {"reason": "移动失败"})
                return
    else:
        complete_task(false, {"reason": "无法执行移动"})

func execute_idle_task(params: Dictionary) -> void:
    var idle_duration = params.get("duration", randf_range(2.0, 5.0))
    set_worker_idle_state()
    
    print("[FarmerTaskManager] 🕒 开始idle任务，持续时间: %.1f秒" % idle_duration)
    
    # 🚨 修复：移除await，使用基类的标准计时器方式
    if is_instance_valid(timer_manager) and timer_manager.has_method("start_action_timer"):
        # 确保信号只连接一次
        if not timer_manager.action_completed.is_connected(_on_farmer_idle_completed):
            timer_manager.action_completed.connect(_on_farmer_idle_completed, CONNECT_ONE_SHOT)
        
        timer_manager.start_action_timer("idle_task", idle_duration, {})
        print("[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器")
    else:
        # 备用方案：使用本地Timer节点
        print("[FarmerTaskManager] 🕒 使用本地Timer启动idle计时器")
        var timer = Timer.new()
        timer.wait_time = idle_duration
        timer.one_shot = true
        timer.timeout.connect(func(): 
            timer.queue_free()
            print("[FarmerTaskManager] 🕒 本地idle计时器完成")
            complete_task(true, {"reason": "idle完成"})
        , CONNECT_ONE_SHOT)
        add_child(timer)
        timer.start()

func _on_farmer_idle_completed(action_type: String, _params: Dictionary) -> void:
    """农夫idle任务完成回调"""
    print("[FarmerTaskManager] 🕒 收到idle完成信号: %s" % action_type)
    if action_type == "idle_task":
        print("[FarmerTaskManager] 🕒 农夫idle任务正常完成")
        complete_task(true, {"reason": "idle完成"})
        
        # 🆕 关键修复：idle完成后触发下一次AI决策，确保任务循环继续
        call_deferred("_safe_trigger_ai_decision")

func _on_farmer_random_walk_completed(_move_params: Dictionary = {}) -> void:
    """随机移动完成处理 - 🆕 优化状态转换"""
    _disconnect_farmer_movement_signal(_on_farmer_random_walk_completed)
    
    # 🆕 延迟设置idle状态，让移动完成处理自然进行
    call_deferred("_delayed_idle_state_setting")
    complete_task(true, {"reason": "随机走动完成"})
    
    # 🆕 关键修复：随机移动完成后触发下一次AI决策，确保任务循环继续
    call_deferred("_safe_trigger_ai_decision")

func _delayed_idle_state_setting() -> void:
    """延迟设置idle状态，避免与移动完成处理冲突"""
    if is_instance_valid(farmer) and not farmer.is_moving():
        farmer.change_character_state(WorkerCharacter.CharacterSpecificState.IDLE)

func _disconnect_farmer_movement_signal(callback: Callable) -> void:
    if is_instance_valid(farmer) and farmer.movement_completed.is_connected(callback):
        farmer.movement_completed.disconnect(callback)

func _calculate_random_walk_position() -> Vector2:
    if not is_instance_valid(farmer):
        return Vector2.ZERO
    
    if farmer.global_position == null:
        return Vector2.ZERO
    
    return super._calculate_random_walk_position()

func _start_interaction(target: Node, interaction_type: String, params: Dictionary, fallback_target: Node) -> void:
    # 🆕 记录交互尝试到BaseTaskManager
    record_interaction_attempt(interaction_type, target, params)

    if is_instance_valid(interaction_manager):
        if not interaction_manager.start_interaction(target, interaction_type, params):
            if fallback_target and is_instance_valid(fallback_target):
                var lock_type = "interaction"  # 使用通用锁定类型
                unlock_resource_target(fallback_target, lock_type)
            complete_task(false, {"reason": "交互启动失败"})
    else:
        complete_task(false, {"reason": "交互管理器无效"})

func _on_interaction_completed(interaction_type: String, _target: Node, result: Dictionary) -> void:
    """处理交互完成事件 - 使用CoreTechnologies交互记录系统"""

    # 🆕 记录交互完成到BaseTaskManager
    record_interaction_completion(interaction_type, true, result)
    
    # 🔧 取水完成后立即创建浇水任务
    if interaction_type == "fetch_water" and result.get("fetched_water", false):
        var pending_farmland = get_meta("pending_water_target")
        var is_pre_locked = get_meta("pending_water_pre_locked", false)
        
        if is_instance_valid(pending_farmland):
            # 验证农田仍然需要浇水
            if pending_farmland.has_method("needs_watering") and pending_farmland.needs_watering():
                add_task(TaskType.WATER_CROP, {
                    "target_farmland": pending_farmland,
                    "pre_locked": is_pre_locked
                }, TaskPriority.HIGHEST)
            else:
                if is_pre_locked:
                    unlock_resource_target(pending_farmland, "crop")
        
        # 清理临时数据
        remove_meta("pending_water_target")
        remove_meta("pending_water_pre_locked")
        
        # 完成任务
        complete_task(true, result)
    
    # 🔧 浇水完成后清除水资源并触发下一个任务
    elif interaction_type == "water" and result.get("watered", false):
        # 完成浇水工作流程
        var current_workflow = get_current_workflow()
        if not current_workflow.is_empty():
            var session_id = current_workflow.get("id", "")
            if not session_id.is_empty():
                complete_workflow(session_id, true)

        # 🔧 关键修复：确保清除农夫的水资源
        if is_instance_valid(farmer) and farmer.is_carrying_resource():
            var carrying_type = farmer.get_carrying_resource_type()
            if carrying_type == "water":
                farmer.clear_carrying_resource()
            else:
                print("[FarmerTaskManager] 警告：农夫携带的不是水资源: %s" % carrying_type)

        complete_task(true, result)
        call_deferred("_decide_next_task")

    # 🆕 使用伐木工黄金标准的动作连贯性机制
    else:
        # 更新工作流程阶段
        var current_workflow = get_current_workflow()
        if not current_workflow.is_empty():
            var session_id = current_workflow.get("id", "")
            if not session_id.is_empty():
                match interaction_type:
                    "harvest":
                        update_workflow_stage(session_id, "collecting", {"harvest_result": result})
                        # 🔧 统一方案：使用专用动作连贯性方法
                        call_deferred("_immediate_mark_and_collect")
                    "collect":
                        update_workflow_stage(session_id, "storing", {"collected_items": result.get("collected_items", [])})
                    "store":
                        complete_workflow(session_id, true)
                    "plant":
                        update_workflow_stage(session_id, "watering", {"planted_crop": result.get("planted_crop", "")})

        # 尝试动作连贯性处理
        var continuity_handled = execute_immediate_task_chain(interaction_type)
        if not continuity_handled:
            # 降级到延迟处理
            fallback_to_deferred_processing(interaction_type)

        complete_task(true, result)

## 🆕 立即标记和收集方法 - 无延迟版本 ##
func _immediate_mark_and_collect() -> void:
    """立即标记和收集 - 无延迟版本，避免时序问题"""
    print("[FarmerTaskManager] 立即标记和收集被调用")

    # 🔧 关键修复：如果农夫已经携带资源，优先存储
    if farmer.is_carrying_resource():
        var carrying_type = farmer.get_carrying_resource_type()
        if _is_crop_type(carrying_type):
            print("[FarmerTaskManager] 农夫携带作物，优先存储")
            var storage = _find_available_storage()
            if is_instance_valid(storage):
                add_task(TaskType.STORE_CROP, {"target_storage": storage}, TaskPriority.HIGHEST)
                return
        # 如果携带的不是作物或没有可用存储，继续收集流程

    # 🔧 关键修复：立即查找和标记，不等待帧
    var dropped_items = get_tree().get_nodes_in_group("simple_dropped_items")
    var my_farmer_id = farmer.get_instance_id()
    var farmer_position = farmer.global_position

    const OWNERSHIP_RADIUS = 50.0
    var my_drops = []

    print("[FarmerTaskManager] 查找掉落物，总数: %d" % dropped_items.size())

    for item in dropped_items:
        if not is_instance_valid(item):
            continue

        # 检查距离
        var distance = farmer_position.distance_to(item.global_position)
        if distance > OWNERSHIP_RADIUS:
            continue

        # 检查是否已有归属
        if item.has_meta("harvester_id"):
            continue

        # 检查是否是作物类型
        var item_type = item.get_meta("item_type", "")
        if not _is_crop_type(item_type):
            continue

        # 标记为我的掉落物
        item.set_meta("harvester_id", my_farmer_id)
        my_drops.append(item)
        print("[FarmerTaskManager] 标记掉落物: %s, 距离: %.1f" % [item_type, distance])

    # 立即收集我的掉落物
    if not my_drops.is_empty():
        # 选择最近的一个收集
        my_drops.sort_custom(func(a, b):
            var dist_a = farmer_position.distance_to(a.global_position)
            var dist_b = farmer_position.distance_to(b.global_position)
            return dist_a < dist_b
        )

        var nearest_drop = my_drops[0]
        print("[FarmerTaskManager] 立即收集最近的掉落物")
        add_task(TaskType.COLLECT_ITEM, {"target_item": nearest_drop}, TaskPriority.HIGHEST)
    else:
        print("[FarmerTaskManager] 没有找到可收集的掉落物，继续下一个任务")
        call_deferred("_decide_next_task")

# 🗑️ 已删除旧的 _mark_and_collect_my_drops() 方法，使用 _immediate_mark_and_collect() 替代

# 🗑️ 已删除 _check_for_dropped_items() 方法，统一使用动作连贯性机制

func _on_interaction_failed(interaction_type: String, reason: String) -> void:
    """处理交互失败事件 - 使用CoreTechnologies交互记录系统"""

    # 🆕 记录交互失败到BaseTaskManager
    record_interaction_completion(interaction_type, false, {"reason": reason})

    complete_task(false, {"reason": reason})

## 🆕 AI控制方法 - 添加缺失的AI启用控制
func enable_ai() -> void:
    """启用AI决策"""
    ai_enabled = true

func disable_ai() -> void:
    """禁用AI决策"""
    ai_enabled = false

func is_ai_enabled() -> bool:
    """检查AI是否启用"""
    return ai_enabled

func _get_farmer_state() -> String:
    """获取农夫当前状态 - 用于AI频率控制"""
    if not is_instance_valid(farmer):
        return "UNKNOWN"

    if farmer.is_carrying_resource():
        return "CARRYING"
    elif is_processing_tasks:
        var current_task_data = get_current_task()
        var task_type = current_task_data.get("type", TaskType.NONE)
        match task_type:
            TaskType.HARVEST_CROP, TaskType.PLANT_SEED, TaskType.WATER_CROP:
                return "WORKING"
            TaskType.RANDOM_WALK:
                return "MOVING"
            _:
                return "IDLE"
    else:
        return "IDLE"

## 🆕 初始任务添加 - 学习伐木工最佳实践
func _add_initial_task() -> void:
    """添加初始任务 - 在AI启用时立即触发第一次决策"""
    if not _is_initialized or not ai_enabled:
        return
    
    # 添加一个短暂的思考任务，触发AI决策
    add_task(TaskType.IDLE, {"duration": 0.1}, TaskPriority.NORMAL)
    
    # 或者直接触发AI决策
    call_deferred("_safe_trigger_ai_decision")

## 🆕 统一的存储任务检查方法 - 减少重复代码
func _should_create_storage_task() -> bool:
    """统一检查是否应该创建存储任务"""
    if not is_instance_valid(farmer) or not farmer.is_carrying_resource():
        return false
    
    var carrying_type = farmer.get_carrying_resource_type()
    return _is_crop_type(carrying_type)

func _create_storage_task_if_needed(priority: int = TaskPriority.HIGHEST) -> bool:
    """统一创建存储任务的方法"""
    if not _should_create_storage_task():
        return false
    
    var storage = _find_available_storage()
    if is_instance_valid(storage):
        add_task(TaskType.STORE_CROP, {"target_storage": storage}, priority)
        return true
    else:
        # 如果没有可用存储，添加等待任务（仅在紧急情况下）
        if priority == TaskPriority.HIGHEST:
            add_task(TaskType.WAIT_FOR_STORAGE, {}, TaskPriority.NORMAL)
            return true
    
    return false

func log_non_crop_resource_skip(_context: String) -> void:
    """简化的非作物资源跳过日志"""
    # 简化实现，只在调试模式下输出
    pass

# 🔧 新增：查找需要浇水的农田
func _find_farmland_needing_water() -> Node:
    """查找需要浇水的农田"""
    var farmlands = get_tree().get_nodes_in_group("farmlands")
    var best_farmland = null
    var best_distance = INF
    
    for farmland in farmlands:
        if not is_instance_valid(farmland):
            continue
        
        # 检查农田是否需要浇水
        if not farmland.has_method("needs_watering") or not farmland.needs_watering():
            continue
        
        # 检查农田状态，确保是生长中的作物
        if farmland.has_method("get_current_state_key"):
            var state_key = farmland.get_current_state_key()
            if state_key != "OCCUPIED_GROWING":
                continue
        
        # 选择最近的农田
        var distance = farmer.global_position.distance_to(farmland.global_position)
        if distance < best_distance:
            best_distance = distance
            best_farmland = farmland
    
    return best_farmland

func _find_all_farmlands_needing_water() -> Array:
    """查找所有需要浇水的农田 - 用于原子化锁定"""
    var farmlands = get_tree().get_nodes_in_group("farmlands")
    var needing_water = []

    for farmland in farmlands:
        if not is_instance_valid(farmland):
            continue

        # 检查农田是否需要浇水
        if not farmland.has_method("needs_watering") or not farmland.needs_watering():
            continue

        # 检查农田状态，确保是生长中的作物
        if farmland.has_method("get_current_state_key"):
            var state_key = farmland.get_current_state_key()
            if state_key != "OCCUPIED_GROWING":
                continue

        needing_water.append(farmland)

    return needing_water

# 🗑️ 已全面清理掉落物收集相关的冗余方法

## 🆕 动态超时配置重写 ##
func get_task_timeout_duration(task_type: int = -1) -> float:
    """重写：为农夫任务提供配置驱动的动态超时 - 伐木工黄金标准"""
    # 获取当前任务的动作时间
    var action_time = 0.0

    match task_type:
        TaskType.HARVEST_CROP:
            action_time = get_action_time("harvest")
        TaskType.PLANT_SEED:
            action_time = get_action_time("plant")
        TaskType.COLLECT_ITEM:
            action_time = get_action_time("collect")
        TaskType.STORE_CROP:
            action_time = get_action_time("store")
        TaskType.FETCH_WATER:
            action_time = get_action_time("fetch_water")
        TaskType.WATER_CROP:
            action_time = get_action_time("water")
        _:
            action_time = 5.0  # 默认5秒

    # 超时时间 = 动作时间 × 安全系数(2.0) + 移动时间(10秒)
    var timeout_duration = action_time * 2.0 + 10.0

    # 确保最小超时时间为15秒
    timeout_duration = max(timeout_duration, 15.0)

    # 简化调试输出
    return timeout_duration

## 🎯 统一时间计算系统 - 伐木工黄金标准 ##
func get_action_time(action_type: String) -> float:
    """统一化时间获取 - 配置驱动"""
    if is_instance_valid(timer_manager) and timer_manager.has_method("get_effective_action_time"):
        var action_key = action_type + "_action"
        var action_time = timer_manager.get_effective_action_time(action_key)
        # 简化调试输出
        return action_time

    # 兜底方案
    return 5.0

## 简化的日志系统 ##
enum LogLevel { INFO, WARNING, ERROR }

func log_task_management(message: String, level: LogLevel = LogLevel.INFO) -> void:
    """简化的任务管理日志"""
    if level == LogLevel.ERROR:
        print("[FarmerTaskManager] ERROR: " + message)

# 🆕 检查是否有足够金币种植指定农田的作物
func _can_afford_planting_for_farmland(farmland: Node) -> bool:
    if not is_instance_valid(farmland):
        return false
    
    # 获取农田指定的作物类型
    var crop_type = ""
    if farmland.has_method("get_assigned_crop_type_id"):
        crop_type = farmland.get_assigned_crop_type_id()
    
    # 🆕 如果农田没有指定作物，从农夫获取默认作物类型
    if crop_type.is_empty():
        if is_instance_valid(farmer) and farmer.has_method("get_preferred_crop_type"):
            crop_type = farmer.get_preferred_crop_type()

        # 最终回退
        if crop_type.is_empty():
            crop_type = "wheat"
    
    # 通过ResourceManager检查是否有足够金币
    var resource_manager = _get_resource_manager()
    if not is_instance_valid(resource_manager):
        log_task_management("无法获取ResourceManager，无法检查种植费用", LogLevel.ERROR)
        return false
    
    # 简化实现检查种植成本
    var cost = 10  # 默认种植成本
    var current_coins = resource_manager.get_item_amount("coins")

    var can_afford = current_coins >= cost
    if not can_afford:
        # 简化金币不足日志 - 只在调试模式下输出
        pass

    return can_afford

# 获取ResourceManager的引用 - 使用IResourceSystem统一接口
func _get_resource_manager():
    return IResourceSystem.get_resource_manager()

## 🆕 统一状态系统支持方法 ##

func update_unified_state(new_state: UnifiedStates.State) -> void:
    """更新统一状态 - 使用CoreTechnologies状态同步"""
    if current_unified_state == new_state:
        return

    var old_state = current_unified_state
    current_unified_state = new_state

    # 使用BaseTaskManager同步状态到所有目标
    var state_data = {"old_state": old_state, "new_state": new_state}
    sync_state_to_targets(new_state, state_data)

    # 根据新状态调整任务管理逻辑
    _handle_unified_state_change(old_state, new_state)

    if OS.is_debug_build():
        print("[FarmerTaskManager] 统一状态更新: %s -> %s" % [
            UnifiedStates.get_state_name(old_state),
            UnifiedStates.get_state_name(new_state)
        ])

func get_unified_state() -> UnifiedStates.State:
    """获取当前统一状态"""
    return current_unified_state

func _handle_unified_state_change(_old_state: UnifiedStates.State, new_state: UnifiedStates.State) -> void:
    """处理统一状态变化"""
    # 根据状态变化调整任务优先级和行为
    match new_state:
        UnifiedStates.State.HARVESTING:
            _on_start_harvesting()
        UnifiedStates.State.PLANTING:
            _on_start_planting()
        UnifiedStates.State.WATERING:
            _on_start_watering()
        UnifiedStates.State.COLLECTING:
            _on_start_collecting()
        UnifiedStates.State.STORING:
            _on_start_storing()
        UnifiedStates.State.IDLE:
            _on_become_idle()

func _on_start_harvesting() -> void:
    """开始收获时的处理"""
    # 收获状态下，优先处理收获相关任务
    _adjust_task_priorities_for_harvesting()

    # 清理不相关的任务
    _clear_non_harvesting_tasks()

    # 优化收获行为参数
    _optimize_harvesting_behavior()

    if OS.is_debug_build():
        print("[FarmerTaskManager] 进入收获状态 - 调整任务优先级")

func _on_start_planting() -> void:
    """开始种植时的处理"""
    # 种植状态下，优先处理种植任务
    _adjust_task_priorities_for_planting()

    # 检查种植资源准备
    _verify_planting_resources()

    # 优化种植策略
    _optimize_planting_strategy()

    if OS.is_debug_build():
        print("[FarmerTaskManager] 进入种植状态 - 调整任务优先级")

func _on_start_watering() -> void:
    """开始浇水时的处理"""
    # 浇水状态下，专注于浇水任务
    _adjust_task_priorities_for_watering()

    # 验证水资源充足
    _verify_water_resources()

    # 优化浇水路径
    _optimize_watering_path()

    if OS.is_debug_build():
        print("[FarmerTaskManager] 进入浇水状态 - 专注于浇水任务")

func _on_start_collecting() -> void:
    """开始收集时的处理"""
    # 收集状态下，优先处理收集任务
    _adjust_task_priorities_for_collecting()

    # 暂停冲突的任务
    _clear_conflicting_tasks()

    # 优化收集效率
    _optimize_collection_efficiency()

    if OS.is_debug_build():
        print("[FarmerTaskManager] 进入收集状态 - 专注于收集掉落物品")

func _on_start_storing() -> void:
    """开始存储时的处理"""
    # 存储状态下，优先处理存储任务
    _adjust_task_priorities_for_storing()

    # 暂停新任务生成
    _pause_new_task_generation()

    # 优化存储路径
    _optimize_storage_path()

    if OS.is_debug_build():
        print("[FarmerTaskManager] 进入存储状态 - 专注于存储作物")

func _on_become_idle() -> void:
    """变为空闲时的处理"""
    # 空闲状态下，可以触发新任务决策
    _reset_task_priorities()

    # 清理临时状态
    _clear_temporary_states()

    # 触发新任务决策
    if not has_pending_tasks():
        call_deferred("_decide_next_task")

    if OS.is_debug_build():
        print("[FarmerTaskManager] 进入空闲状态 - 准备新任务决策")

func is_farmer_working() -> bool:
    """检查农夫是否在工作"""
    return UnifiedStates.is_farmer_state(current_unified_state) and UnifiedStates.is_working_state(current_unified_state)

func is_in_unified_state(state: UnifiedStates.State) -> bool:
    """检查是否处于指定的统一状态"""
    return current_unified_state == state

func can_accept_new_task() -> bool:
    """检查是否可以接受新任务 - 基于统一状态"""
    return current_unified_state == UnifiedStates.State.IDLE or current_unified_state == UnifiedStates.State.CARRYING

## 🆕 状态处理配置化系统 - 低风险优化 ##

# 状态优先级配置表 - 替代18个重复方法
const STATE_TASK_PRIORITIES = {
    "harvesting": {
        TaskType.HARVEST_CROP: TaskPriority.HIGHEST,
        TaskType.COLLECT_ITEM: TaskPriority.HIGH,
        TaskType.STORE_CROP: TaskPriority.HIGH,
        TaskType.PLANT_SEED: TaskPriority.LOW,
        TaskType.WATER_CROP: TaskPriority.LOW
    },
    "planting": {
        TaskType.PLANT_SEED: TaskPriority.HIGHEST,
        TaskType.HARVEST_CROP: TaskPriority.LOW,
        TaskType.WATER_CROP: TaskPriority.LOW
    },
    "watering": {
        TaskType.WATER_CROP: TaskPriority.HIGHEST,
        TaskType.FETCH_WATER: TaskPriority.HIGH,  # 包含原FIND_WELL功能
        TaskType.PLANT_SEED: TaskPriority.LOW,
        TaskType.HARVEST_CROP: TaskPriority.LOW
    },
    "collecting": {
        TaskType.COLLECT_ITEM: TaskPriority.HIGHEST,
        TaskType.STORE_CROP: TaskPriority.HIGH,
        TaskType.PLANT_SEED: TaskPriority.LOW,
        TaskType.WATER_CROP: TaskPriority.LOW
    },
    "storing": {
        TaskType.STORE_CROP: TaskPriority.HIGHEST,
        # 所有其他任务都设为低优先级
        TaskType.HARVEST_CROP: TaskPriority.LOW,
        TaskType.PLANT_SEED: TaskPriority.LOW,
        TaskType.COLLECT_ITEM: TaskPriority.LOW,
        TaskType.WATER_CROP: TaskPriority.LOW
    }
}

# 元数据管理配置 - 优化版本
const METADATA_CONFIG = {
    # 状态相关元数据分组
    "state_groups": {
        "mode_flags": ["harvesting_mode", "planting_mode", "watering_mode", "collecting_mode", "storing_mode"],
        "optimization_flags": ["auto_collect_after_harvest", "prioritize_mature_crops", "prioritize_empty_farmlands",
                              "optimize_watering_sequence", "prioritize_nearest_items", "auto_store_after_collect",
                              "optimize_storage_route", "prioritize_nearest_storage"],
        "control_flags": ["pause_new_tasks"],
        "temporary_data": ["pending_water_target", "pending_water_pre_locked"]
    },

    # 状态行为配置 - 简化版本
    "state_behaviors": {
        "harvesting": {
            "primary_mode": "harvesting_mode",
            "optimization_flags": ["auto_collect_after_harvest", "prioritize_mature_crops"],
            "conflicting_tasks": [TaskType.PLANT_SEED, TaskType.WATER_CROP]
        },
        "planting": {
            "primary_mode": "planting_mode",
            "optimization_flags": ["prioritize_empty_farmlands"],
            "conflicting_tasks": []
        },
        "watering": {
            "primary_mode": "watering_mode",
            "optimization_flags": ["optimize_watering_sequence"],
            "conflicting_tasks": []
        },
        "collecting": {
            "primary_mode": "collecting_mode",
            "optimization_flags": ["prioritize_nearest_items", "auto_store_after_collect"],
            "conflicting_tasks": [TaskType.PLANT_SEED, TaskType.WATER_CROP]
        },
        "storing": {
            "primary_mode": "storing_mode",
            "optimization_flags": ["optimize_storage_route", "prioritize_nearest_storage"],
            "control_flags": ["pause_new_tasks"],
            "conflicting_tasks": []
        }
    }
}

# 统一的状态处理方法 - 替代18个重复方法
func _adjust_task_priorities_for_state(state_name: String) -> void:
    """统一的任务优先级调整方法"""
    var priority_config = STATE_TASK_PRIORITIES.get(state_name, {})

    # 调整任务队列中的优先级
    for task in task_queue:
        var task_type = task.get("type", TaskType.NONE)
        if priority_config.has(task_type):
            task["priority"] = priority_config[task_type]

# 优化的元数据管理系统
func _apply_state_behaviors(state_name: String) -> void:
    """应用状态相关的行为配置 - 优化版本"""
    var behavior_config = METADATA_CONFIG.state_behaviors.get(state_name, {})

    # 自动清理冲突的模式标志
    _clear_conflicting_mode_flags(state_name)

    # 设置主要模式标志
    var primary_mode = behavior_config.get("primary_mode", "")
    if not primary_mode.is_empty():
        set_meta(primary_mode, true)

    # 设置优化标志
    var optimization_flags = behavior_config.get("optimization_flags", [])
    for flag in optimization_flags:
        set_meta(flag, true)

    # 设置控制标志
    var control_flags = behavior_config.get("control_flags", [])
    for flag in control_flags:
        set_meta(flag, true)

    # 清理冲突任务
    var conflicting_tasks = behavior_config.get("conflicting_tasks", [])
    if not conflicting_tasks.is_empty():
        _clear_conflicting_tasks_by_types(conflicting_tasks)

func _clear_conflicting_mode_flags(current_state: String) -> void:
    """清理与当前状态冲突的模式标志"""
    var mode_flags = METADATA_CONFIG.state_groups.mode_flags
    var current_mode = METADATA_CONFIG.state_behaviors.get(current_state, {}).get("primary_mode", "")

    for mode_flag in mode_flags:
        if mode_flag != current_mode:
            remove_meta(mode_flag)

func _clear_all_temporary_metadata() -> void:
    """清理所有临时元数据 - 自动化版本"""
    # 清理所有分组的元数据
    for group_name in METADATA_CONFIG.state_groups:
        var meta_keys = METADATA_CONFIG.state_groups[group_name]
        for meta_key in meta_keys:
            remove_meta(meta_key)

func _set_temporary_metadata(key: String, value, timeout_seconds: float = 30.0) -> void:
    """设置临时元数据，带自动过期"""
    set_meta(key, value)

    # 设置过期时间
    var expire_time = Time.get_unix_time_from_system() + timeout_seconds
    set_meta(key + "_expire_time", expire_time)

func _cleanup_expired_metadata() -> void:
    """清理过期的临时元数据"""
    var current_time = Time.get_unix_time_from_system()
    var temp_keys = METADATA_CONFIG.state_groups.temporary_data

    for key in temp_keys:
        var expire_key = key + "_expire_time"
        if has_meta(expire_key):
            var expire_time = get_meta(expire_key, 0.0)
            if current_time > expire_time:
                remove_meta(key)
                remove_meta(expire_key)

func _setup_metadata_cleanup_timer() -> void:
    """设置元数据清理定时器"""
    var cleanup_timer = Timer.new()
    cleanup_timer.wait_time = 30.0  # 每30秒清理一次
    cleanup_timer.timeout.connect(_cleanup_expired_metadata)
    cleanup_timer.autostart = true
    add_child(cleanup_timer)

func _clear_conflicting_tasks_by_types(task_types: Array) -> void:
    """清理指定类型的冲突任务"""
    var tasks_to_remove = []
    for i in range(task_queue.size()):
        var task = task_queue[i]
        var task_type = task.get("type", TaskType.NONE)
        var priority = task.get("priority", TaskPriority.NORMAL)

        # 只移除低优先级的冲突任务
        if task_type in task_types and priority <= TaskPriority.NORMAL:
            tasks_to_remove.append(i)

    # 从后往前删除，避免索引问题
    for i in range(tasks_to_remove.size() - 1, -1, -1):
        task_queue.remove_at(tasks_to_remove[i])

## 🆕 保留的原始方法（配置化后的简化版本） ##

func _adjust_task_priorities_for_harvesting() -> void:
    """为收获状态调整任务优先级 - 配置化实现"""
    _adjust_task_priorities_for_state("harvesting")

func _clear_non_harvesting_tasks() -> void:
    """清理与收获冲突的任务 - 配置化实现"""
    _clear_conflicting_tasks_by_types([TaskType.PLANT_SEED, TaskType.WATER_CROP])

func _optimize_harvesting_behavior() -> void:
    """优化收获行为 - 配置化实现"""
    _apply_state_behaviors("harvesting")

func _adjust_task_priorities_for_planting() -> void:
    """为种植状态调整任务优先级 - 配置化实现"""
    _adjust_task_priorities_for_state("planting")

func _verify_planting_resources() -> void:
    """验证种植资源"""
    # 检查金币充足性
    var coins_sufficient = _check_planting_cost()
    if not coins_sufficient:
        _pause_planting_tasks()
        if OS.is_debug_build():
            print("[FarmerTaskManager] 金币不足，暂停种植任务")

func _optimize_planting_strategy() -> void:
    """优化种植策略 - 简化实现，避免重复调用"""
    # 根据季节调整种植策略（如果有季节系统）
    _adjust_planting_for_season()

    # 状态行为已在_adjust_task_priorities_for_planting中应用，避免重复

func _adjust_task_priorities_for_watering() -> void:
    """为浇水状态调整任务优先级 - 配置化实现"""
    _adjust_task_priorities_for_state("watering")

func _verify_water_resources() -> void:
    """验证水资源充足"""
    # 检查农夫是否携带水
    if not (is_instance_valid(farmer) and farmer.is_carrying_resource() and farmer.get_carrying_resource_type() == "water"):
        # 如果没有水，优先添加取水任务
        var nearest_well = _find_nearest_well()
        if is_instance_valid(nearest_well):
            # 清理现有的浇水任务，先取水
            _clear_watering_tasks_without_water()
            if OS.is_debug_build():
                print("[FarmerTaskManager] 缺少水资源，需要先取水")

func _optimize_watering_path() -> void:
    """优化浇水路径 - 简化实现，避免重复调用"""
    # 状态行为已在_adjust_task_priorities_for_watering中应用，避免重复
    pass

func _adjust_task_priorities_for_collecting() -> void:
    """为收集状态调整任务优先级 - 配置化实现"""
    _adjust_task_priorities_for_state("collecting")

func _clear_conflicting_tasks() -> void:
    """清理与当前状态冲突的任务 - 配置化实现"""
    _clear_conflicting_tasks_by_types([TaskType.PLANT_SEED, TaskType.WATER_CROP])

func _optimize_collection_efficiency() -> void:
    """优化收集效率 - 简化实现，避免重复调用"""
    # 状态行为已在_adjust_task_priorities_for_collecting中应用，避免重复
    pass

func _adjust_task_priorities_for_storing() -> void:
    """为存储状态调整任务优先级 - 配置化实现"""
    _adjust_task_priorities_for_state("storing")

func _pause_new_task_generation() -> void:
    """暂停新任务生成 - 简化实现，避免重复调用"""
    # 状态行为已在_adjust_task_priorities_for_storing中应用，避免重复
    pass

func _optimize_storage_path() -> void:
    """优化存储路径 - 简化实现，避免重复调用"""
    # 状态行为已在_adjust_task_priorities_for_storing中应用，避免重复
    pass

func _reset_task_priorities() -> void:
    """重置任务优先级"""
    # 恢复所有任务的默认优先级
    for task in task_queue:
        var task_type = task.get("type", TaskType.NONE)
        task["priority"] = _get_base_task_priority(task_type)

func _clear_temporary_states() -> void:
    """清理临时状态 - 自动化实现"""
    # 使用新的自动化清理方法
    _clear_all_temporary_metadata()

    # 清理过期的临时数据
    _cleanup_expired_metadata()

## 🆕 状态处理支持方法 ##

func _check_planting_cost() -> bool:
    """检查种植成本是否充足"""
    # 这里应该检查金币或其他种植成本
    # 暂时返回true，具体实现需要根据游戏经济系统
    return true

func _pause_planting_tasks() -> void:
    """暂停种植任务"""
    var tasks_to_remove = []
    for i in range(task_queue.size()):
        var task = task_queue[i]
        if task.get("type", TaskType.NONE) == TaskType.PLANT_SEED:
            tasks_to_remove.append(i)

    # 从后往前删除
    for i in range(tasks_to_remove.size() - 1, -1, -1):
        task_queue.remove_at(tasks_to_remove[i])

func _adjust_planting_for_season() -> void:
    """根据季节调整种植策略"""
    # 这里可以根据季节系统调整种植策略
    # 暂时为空实现，等待季节系统完善
    pass

func _clear_watering_tasks_without_water() -> void:
    """清理没有水资源的浇水任务"""
    var tasks_to_remove = []
    for i in range(task_queue.size()):
        var task = task_queue[i]
        if task.get("type", TaskType.NONE) == TaskType.WATER_CROP:
            # 检查是否有水资源支持
            if not (is_instance_valid(farmer) and farmer.is_carrying_resource() and farmer.get_carrying_resource_type() == "water"):
                tasks_to_remove.append(i)

    # 从后往前删除
    for i in range(tasks_to_remove.size() - 1, -1, -1):
        task_queue.remove_at(tasks_to_remove[i])
