# FarmerTimerManager.gd
# 农夫计时器管理器 - 深度集成CoreTechnologies的S+级别实现
class_name FarmerTimerManager
extends Node

# 🆕 统一状态系统支持
const UnifiedStates = preload("res://scripts/core/UnifiedCharacterStates.gd")
const CoreTech = preload("res://scripts/core/CoreTechnologies.gd")
var current_unified_state: UnifiedStates.State = UnifiedStates.State.IDLE

## 常量配置 ##
# 🆕 使用统一的基础数值配置
const ACTION_TIMES = {
    "harvest_action": GameConstants.AgricultureConstants.HARVEST_BASE_TIME,
    "plant_action": GameConstants.AgricultureConstants.PLANT_BASE_TIME,
    "collect_action": GameConstants.AgricultureConstants.COLLECT_BASE_TIME,
    "store_action": GameConstants.AgricultureConstants.STORE_BASE_TIME,
    "fetch_water_action": GameConstants.AgricultureConstants.FETCH_WATER_BASE_TIME,
    "water_action": GameConstants.AgricultureConstants.WATER_BASE_TIME,
    "thinking": 0.5,
    "resting": 5.0
}

# 性能监控常量
const MAX_AI_FREQUENCY: float = 1.0
const MIN_AI_FREQUENCY: float = 3.0
const PERFORMANCE_SAMPLE_SIZE: int = 10
const INTERACTION_RECORD_LIMIT: int = 50

## 信号定义 ##
signal action_completed(action_type: String, action_data: Dictionary)
signal ai_decision_triggered()
# 🆕 管理器协调信号
signal state_sync_requested(new_state: UnifiedStates.State, source_manager: String)
signal efficiency_updated(action_type: String, multiplier: float)

## 属性定义 ##
# 🆕 CoreTechnologies核心技术实例
var core_ai_controller: CoreTech.AIFrequencyController
var core_interaction_recorder: CoreTech.InteractionRecorder
var core_state_synchronizer: CoreTech.StateSynchronizer

# 核心技术属性 (按技术分组) - 保持向后兼容
var _动态AI频率调整_ai_tick_times: Array = []
var _动态AI频率调整_current_frequency: float = MAX_AI_FREQUENCY

# 交互记录系统 - 性能统计 - 保持向后兼容
var _交互记录系统_records: Array = []
var _交互记录系统_statistics: Dictionary = {}
var _交互记录系统_session_start_time: float

# 角色专用属性
var host_farmer: Farmer = null
var ai_is_enabled: bool = false
var is_action_in_progress: bool = false

# 内部计时器
var _ai_think_timer: Timer = null
var _action_timer: Timer = null

# 内部状态
var _is_initialized: bool = false
var _current_action_type: String = ""
var _current_action_data: Dictionary = {}

# 🆕 装备效果缓存
var _efficiency_multipliers: Dictionary = {}

## 生命周期方法 ##
func _ready() -> void:
    name = "FarmerTimerManager"

func initialize(farmer_instance, _data_manager: Node = null) -> bool:
    if not is_instance_valid(farmer_instance):
        return false

    host_farmer = farmer_instance

    # 🆕 初始化CoreTechnologies核心技术实例
    _initialize_core_technologies()

    _create_internal_timers()
    _初始化_交互记录系统()
    _is_initialized = true
    return true

func _initialize_core_technologies() -> void:
    """初始化CoreTechnologies核心技术实例"""
    # 创建AI频率控制器
    core_ai_controller = CoreTech.create_ai_frequency_controller(MAX_AI_FREQUENCY, MIN_AI_FREQUENCY)

    # 创建交互记录器
    core_interaction_recorder = CoreTech.create_interaction_recorder(INTERACTION_RECORD_LIMIT)

    # 创建状态同步器
    core_state_synchronizer = CoreTech.create_state_synchronizer()

    # 注册同步目标
    if is_instance_valid(host_farmer):
        core_state_synchronizer.register_sync_target(host_farmer)

    print("[FarmerTimerManager] CoreTechnologies核心技术初始化完成")

func cleanup() -> void:
    disable_ai()

    if is_instance_valid(_ai_think_timer):
        _ai_think_timer.queue_free()

    if is_instance_valid(_action_timer):
        _action_timer.queue_free()

    # 🆕 清理CoreTechnologies实例
    _cleanup_core_technologies()

    _清理_交互记录系统()
    _清理_动态AI频率调整()
    _is_initialized = false

func _cleanup_core_technologies() -> void:
    """清理CoreTechnologies实例"""
    if core_ai_controller:
        core_ai_controller.cleanup()
        core_ai_controller = null

    if core_interaction_recorder:
        core_interaction_recorder.cleanup()
        core_interaction_recorder = null

    if core_state_synchronizer:
        core_state_synchronizer.cleanup()
        core_state_synchronizer = null

    print("[FarmerTimerManager] CoreTechnologies清理完成")

## 核心技术实现区 ##

# 🆕 动态AI频率调整 - CoreTechnologies深度集成版本
func _动态AI频率调整_should_trigger_ai() -> bool:
    """智能判断是否应该触发AI决策 - 使用CoreTechnologies"""
    if not is_instance_valid(host_farmer):
        return false

    # 🆕 优先使用CoreTechnologies的AI控制器
    if core_ai_controller:
        return core_ai_controller.should_trigger_ai(current_unified_state, is_action_in_progress)

    # 🆕 回退到本地实现 - 保持向后兼容
    if not UnifiedStates.should_trigger_ai_for_state(current_unified_state):
        return false

    # 如果正在执行动作，不触发AI
    if is_action_in_progress:
        return false

    # 检查是否有待处理的任务
    if is_instance_valid(host_farmer.task_manager):
        var task_manager = host_farmer.task_manager
        if task_manager.has_method("has_pending_tasks") and task_manager.has_pending_tasks():
            return false

    return true

func _动态AI频率调整_adjust_frequency() -> void:
    """动态调整AI决策频率 - 使用CoreTechnologies增强版本"""
    # 🆕 优先使用CoreTechnologies的AI控制器
    if core_ai_controller:
        var new_frequency = core_ai_controller.adjust_frequency(_动态AI频率调整_ai_tick_times)
        if new_frequency > 0:
            _动态AI频率调整_current_frequency = new_frequency
            # 更新计时器频率
            if is_instance_valid(_ai_think_timer):
                _ai_think_timer.wait_time = _动态AI频率调整_current_frequency
            return

    # 🆕 回退到本地实现 - 保持向后兼容
    if _动态AI频率调整_ai_tick_times.size() < 3:
        return

    # 计算平均AI处理时间
    var total_time = 0.0
    for time in _动态AI频率调整_ai_tick_times:
        total_time += time
    var avg_time = total_time / _动态AI频率调整_ai_tick_times.size()

    # 根据处理时间调整频率
    if avg_time > 0.1:  # 处理时间过长，降低频率
        _动态AI频率调整_current_frequency = min(_动态AI频率调整_current_frequency * 1.2, MIN_AI_FREQUENCY)
    elif avg_time < 0.05:  # 处理时间很短，提高频率
        _动态AI频率调整_current_frequency = max(_动态AI频率调整_current_frequency * 0.9, MAX_AI_FREQUENCY)

    # 更新计时器频率
    if is_instance_valid(_ai_think_timer):
        _ai_think_timer.wait_time = _动态AI频率调整_current_frequency

func _动态AI频率调整_record_ai_tick(processing_time: float) -> void:
    """记录AI处理时间 - 使用CoreTechnologies增强版本"""
    # 🆕 优先使用CoreTechnologies的AI控制器
    if core_ai_controller:
        core_ai_controller.record_ai_tick(processing_time)

    # 🆕 同时保持本地记录 - 向后兼容
    _动态AI频率调整_ai_tick_times.append(processing_time)

    # 保持样本数量在合理范围内
    if _动态AI频率调整_ai_tick_times.size() > PERFORMANCE_SAMPLE_SIZE:
        _动态AI频率调整_ai_tick_times = _动态AI频率调整_ai_tick_times.slice(-PERFORMANCE_SAMPLE_SIZE)

    # 定期调整频率
    if _动态AI频率调整_ai_tick_times.size() >= 5:
        _动态AI频率调整_adjust_frequency()

func _清理_动态AI频率调整() -> void:
    """清理动态AI频率调整"""
    _动态AI频率调整_ai_tick_times.clear()
    _动态AI频率调整_current_frequency = MAX_AI_FREQUENCY

# 交互记录系统 - 统一实现
func _初始化_交互记录系统() -> void:
    """初始化交互记录系统"""
    _交互记录系统_records = []
    _交互记录系统_statistics = {
        "total_interactions": 0,
        "successful_interactions": 0,
        "failed_interactions": 0,
        "average_duration": 0.0,
        "interaction_types": {}
    }
    _交互记录系统_session_start_time = Time.get_unix_time_from_system()

func record_interaction_start(interaction_type: String, target: Node, params: Dictionary) -> void:
    """记录交互开始 - 使用CoreTechnologies增强版本"""
    # 🆕 优先使用CoreTechnologies的交互记录器
    if core_interaction_recorder:
        core_interaction_recorder.record_attempt(interaction_type, target, params)

    # 🆕 同时保持本地记录 - 向后兼容
    var record = {
        "type": interaction_type,
        "target_id": target.get_instance_id() if is_instance_valid(target) else 0,
        "start_time": Time.get_unix_time_from_system(),
        "end_time": 0.0,
        "success": false,
        "duration": 0.0
    }

    _交互记录系统_records.append(record)

    # 保持记录数量在合理范围内
    if _交互记录系统_records.size() > INTERACTION_RECORD_LIMIT:
        _交互记录系统_records = _交互记录系统_records.slice(-INTERACTION_RECORD_LIMIT / 2.0)

func record_interaction_end(interaction_type: String, success: bool) -> void:
    """记录交互结束 - 使用CoreTechnologies增强版本"""
    # 🆕 优先使用CoreTechnologies的交互记录器
    if core_interaction_recorder:
        core_interaction_recorder.record_completion(interaction_type, success)

    # 🆕 同时保持本地记录 - 向后兼容
    if _交互记录系统_records.is_empty():
        return

    # 查找最近的同类型交互记录
    for i in range(_交互记录系统_records.size() - 1, -1, -1):
        var record = _交互记录系统_records[i]
        if record.type == interaction_type and record.end_time == 0.0:
            record.end_time = Time.get_unix_time_from_system()
            record.success = success
            record.duration = record.end_time - record.start_time
            _更新_交互记录统计(record)
            break

func _更新_交互记录统计(record: Dictionary) -> void:
    """更新交互记录统计"""
    _交互记录系统_statistics.total_interactions += 1
    
    if record.success:
        _交互记录系统_statistics.successful_interactions += 1
    else:
        _交互记录系统_statistics.failed_interactions += 1
    
    # 更新平均持续时间
    var total_duration = 0.0
    var valid_records = 0
    for r in _交互记录系统_records:
        if r.duration > 0.0:
            total_duration += r.duration
            valid_records += 1
    
    if valid_records > 0:
        _交互记录系统_statistics.average_duration = total_duration / valid_records
    
    # 更新交互类型统计
    var interaction_type = record.type
    if not _交互记录系统_statistics.interaction_types.has(interaction_type):
        _交互记录系统_statistics.interaction_types[interaction_type] = {
            "count": 0,
            "success_count": 0,
            "average_duration": 0.0
        }
    
    var type_stats = _交互记录系统_statistics.interaction_types[interaction_type]
    type_stats.count += 1
    if record.success:
        type_stats.success_count += 1
    
    # 计算该类型的平均持续时间
    var type_total_duration = 0.0
    var type_valid_records = 0
    for r in _交互记录系统_records:
        if r.type == interaction_type and r.duration > 0.0:
            type_total_duration += r.duration
            type_valid_records += 1
    
    if type_valid_records > 0:
        type_stats.average_duration = type_total_duration / type_valid_records

func get_interaction_statistics() -> Dictionary:
    """获取交互统计信息"""
    return _交互记录系统_statistics.duplicate()


func record_interaction_completion(interaction_type: String, success: bool, result: Dictionary = {}) -> void:
    """记录交互完成"""
    record_interaction_end(interaction_type, success)

    # 可选：记录额外的结果数据
    if not _交互记录系统_records.is_empty():
        var last_record = _交互记录系统_records[-1]
        if last_record.type == interaction_type:
            last_record["result"] = result.duplicate()

func _清理_交互记录系统() -> void:
    """清理交互记录系统"""
    _交互记录系统_records.clear()
    _交互记录系统_statistics.clear()

## AI控制方法 ##
func enable_ai() -> void:
    """启用AI决策"""
    if not _is_initialized or ai_is_enabled:
        return
    
    ai_is_enabled = true
    
    if is_instance_valid(_ai_think_timer):
        _ai_think_timer.start()

func disable_ai() -> void:
    """禁用AI决策"""
    ai_is_enabled = false
    
    if is_instance_valid(_ai_think_timer):
        _ai_think_timer.stop()

func is_ai_enabled() -> bool:
    """检查AI是否启用"""
    return ai_is_enabled

## 动作计时器方法 ##
func start_action_timer(action_type: String, custom_duration_or_data = null, action_data: Dictionary = {}) -> void:
    """启动动作计时器 - 🔧 支持多种调用方式，修复瞬间完成问题"""
    if not _is_initialized:
        return
    
    # 🔧 强制清理之前的动作状态
    if is_action_in_progress:
        # 如果有正在进行的动作，先完成它
        _complete_current_action(false)
    
    is_action_in_progress = true
    _current_action_type = action_type
    
    # 🔧 智能参数处理 - 支持多种调用方式
    var duration: float = 0.0
    var final_action_data: Dictionary = {}
    
    if custom_duration_or_data == null:
        # 调用方式: start_action_timer("harvest_action")
        final_action_data = action_data
    elif custom_duration_or_data is float or custom_duration_or_data is int:
        # 调用方式: start_action_timer("harvest_action", 2.0, {})
        duration = float(custom_duration_or_data)
        final_action_data = action_data
    elif custom_duration_or_data is Dictionary:
        # 调用方式: start_action_timer("harvest_action", {"interaction_type": "harvest"})
        final_action_data = custom_duration_or_data
    else:
        # 未知类型，使用默认值
        final_action_data = action_data
    
    # 🔧 确保使用正确的动作时间
    if duration <= 0.0:
        duration = ACTION_TIMES.get(action_type, 1.0)
        print("[FarmerTimerManager] 使用预设动作时间: %s = %.1f秒" % [action_type, duration])
    else:
        print("[FarmerTimerManager] 使用自定义动作时间: %s = %.1f秒" % [action_type, duration])
    
    # 🆕 关键修复：强制最小时间，防止瞬间完成
    duration = max(duration, 0.8)  # 最少0.8秒
    
    _current_action_data = final_action_data
    
    if is_instance_valid(_action_timer):
        # 🆕 确保计时器完全停止 - 移除可能有问题的await
        _action_timer.stop()
        
        # 🔧 直接设置和启动，避免异步时序问题
        _action_timer.wait_time = duration
        _action_timer.start()
        
        print("[FarmerTimerManager] 🔧 计时器已启动: %s, 实际时长: %.1f秒" % [action_type, duration])
        
        # 记录交互开始
        if action_type.ends_with("_action"):
            var interaction_type = action_type.replace("_action", "")
            record_interaction_start(interaction_type, null, final_action_data)

# 🆕 新增辅助方法以获取动作时间
func get_action_duration(action_type: String) -> float:
    """获取指定动作类型的预设时长"""
    return ACTION_TIMES.get(action_type, 1.0)

## 🆕 装备效果支持方法 - S+级别深度集成版本 ##
func get_effective_action_time(action_type: String) -> float:
    """获取考虑装备效果的动作时间 - S+级别深度集成版本"""
    # 🆕 优先从效率缓存获取
    if _efficiency_multipliers.has(action_type):
        var base_time = ACTION_TIMES.get(action_type, 1.0)
        var multiplier = _efficiency_multipliers[action_type]
        return base_time / multiplier

    # 🆕 回退到元数据方式 - 向后兼容
    var adjusted_key = action_type + "_adjusted"
    if has_meta(adjusted_key):
        return get_meta(adjusted_key)

    return ACTION_TIMES.get(action_type, 1.0)

func update_efficiency_multiplier(action_type: String, multiplier: float) -> void:
    """更新效率倍数 - S+级别深度集成版本"""
    print("[FarmerTimerManager] %s 效率更新: %.3f" % [action_type, multiplier])

    # 🆕 存储到效率缓存
    _efficiency_multipliers[action_type] = multiplier

    # 🆕 发送效率更新信号，通知其他管理器
    efficiency_updated.emit(action_type, multiplier)

    # 🆕 同时保持元数据方式 - 向后兼容
    if ACTION_TIMES.has(action_type):
        var original_time = ACTION_TIMES[action_type]
        var new_time = original_time / multiplier
        print("[FarmerTimerManager] %s 时间调整: %.2f -> %.2f 秒" % [action_type, original_time, new_time])
        set_meta(action_type + "_adjusted", new_time)

func update_action_duration(action_type: String, new_duration: float) -> void:
    """更新动作持续时间 - 新的统一接口"""
    print("[FarmerTimerManager] 动作时间直接更新: %s -> %.2f 秒" % [action_type, new_duration])

    # 🆕 计算等效的效率倍数
    var base_time = ACTION_TIMES.get(action_type, 1.0)
    if base_time > 0:
        var equivalent_multiplier = base_time / new_duration
        _efficiency_multipliers[action_type] = equivalent_multiplier
        efficiency_updated.emit(action_type, equivalent_multiplier)

    # 🆕 同时保持元数据方式 - 向后兼容
    set_meta(action_type + "_adjusted", new_duration)

func update_action_times(time_updates: Dictionary) -> void:
    """批量更新动作时间 - 增强版本"""
    for action_name in time_updates:
        var new_time = time_updates[action_name]
        update_action_duration(action_name, new_time)

func get_efficiency_multiplier(action_type: String) -> float:
    """获取效率倍数"""
    return _efficiency_multipliers.get(action_type, 1.0)

func clear_efficiency_cache() -> void:
    """清理效率缓存"""
    _efficiency_multipliers.clear()
    print("[FarmerTimerManager] 效率缓存已清理")

func stop_action_timer() -> void:
    """停止动作计时器"""
    if is_instance_valid(_action_timer):
        _action_timer.stop()
    
    _complete_current_action(false)

func is_busy() -> bool:
    """检查是否忙碌"""
    return is_action_in_progress

## 内部方法 ##
func _create_internal_timers() -> void:
    """创建内部计时器"""
    # AI思考计时器
    _ai_think_timer = Timer.new()
    _ai_think_timer.name = "AIThinkTimer"
    _ai_think_timer.wait_time = _动态AI频率调整_current_frequency
    _ai_think_timer.timeout.connect(_on_ai_think_timer_timeout)
    add_child(_ai_think_timer)
    
    # 动作计时器
    _action_timer = Timer.new()
    _action_timer.name = "ActionTimer"
    _action_timer.one_shot = true
    _action_timer.timeout.connect(_on_action_timer_timeout)
    add_child(_action_timer)

func _on_ai_think_timer_timeout() -> void:
    """AI思考计时器超时处理"""
    if not ai_is_enabled or not _动态AI频率调整_should_trigger_ai():
        return
    
    var start_time = Time.get_unix_time_from_system()
    
    # 触发AI决策
    ai_decision_triggered.emit()
    
    # 记录处理时间
    var processing_time = Time.get_unix_time_from_system() - start_time
    _动态AI频率调整_record_ai_tick(processing_time)

func _on_action_timer_timeout() -> void:
    """动作计时器超时处理"""
    _complete_current_action(true)

func _complete_current_action(success: bool) -> void:
    """完成当前动作"""
    if not is_action_in_progress:
        return
    
    is_action_in_progress = false
    
    var action_type = _current_action_type
    var action_data = _current_action_data
    
    # 记录交互结束
    if action_type.ends_with("_action"):
        var interaction_type = action_type.replace("_action", "")
        record_interaction_end(interaction_type, success)
    
    # 发送完成信号
    action_completed.emit(action_type, action_data)
    
    # 清理当前动作数据
    _current_action_type = ""
    _current_action_data.clear()

## 状态更新方法 ##


## 调试和监控方法 ##
func get_debug_info() -> Dictionary:
    """获取调试信息"""
    return {
        "ai_enabled": ai_is_enabled,
        "is_busy": is_action_in_progress,
        "current_action": _current_action_type,
        "ai_frequency": _动态AI频率调整_current_frequency,
        "ai_tick_samples": _动态AI频率调整_ai_tick_times.size(),
        "interaction_stats": get_interaction_statistics(),
        "unified_state": UnifiedStates.get_state_name(current_unified_state)
    }

## 🆕 统一状态系统支持方法 - S+级别管理器协调版本 ##

func update_unified_state(new_state: UnifiedStates.State) -> void:
    """更新统一状态 - S+级别管理器协调版本"""
    if current_unified_state == new_state:
        return

    var old_state = current_unified_state
    current_unified_state = new_state

    # 🆕 使用CoreTechnologies状态同步器
    if core_state_synchronizer:
        core_state_synchronizer.sync_state(new_state, {"source": "FarmerTimerManager"}, self)

    # 根据新状态调整计时器行为
    _handle_unified_state_change(old_state, new_state)

    # 🆕 发送状态同步请求信号，通知其他管理器
    state_sync_requested.emit(new_state, "FarmerTimerManager")

    if OS.is_debug_build():
        print("[FarmerTimerManager] 统一状态更新: %s -> %s" % [
            UnifiedStates.get_state_name(old_state),
            UnifiedStates.get_state_name(new_state)
        ])

func get_unified_state() -> UnifiedStates.State:
    """获取当前统一状态"""
    return current_unified_state

func _handle_unified_state_change(old_state: UnifiedStates.State, new_state: UnifiedStates.State) -> void:
    """处理统一状态变化 - S+级别智能化版本"""
    # 🆕 使用CoreTechnologies AI控制器进行智能频率调整
    if core_ai_controller:
        var optimal_frequency = core_ai_controller.get_optimal_frequency_for_state(new_state)
        if optimal_frequency > 0:
            _动态AI频率调整_current_frequency = optimal_frequency
        else:
            # 回退到本地逻辑
            _handle_state_frequency_adjustment(new_state)
    else:
        _handle_state_frequency_adjustment(new_state)

    # 🆕 智能化动作时间调整
    _adjust_action_times_for_state(old_state, new_state)

    # 🆕 状态转换时的特殊处理
    _handle_state_transition_effects(old_state, new_state)

    # 更新AI计时器频率
    if is_instance_valid(_ai_think_timer):
        _ai_think_timer.wait_time = _动态AI频率调整_current_frequency

func _handle_state_frequency_adjustment(new_state: UnifiedStates.State) -> void:
    """处理状态频率调整 - 本地实现"""
    match new_state:
        UnifiedStates.State.IDLE:
            _动态AI频率调整_current_frequency = MAX_AI_FREQUENCY
        UnifiedStates.State.HARVESTING, UnifiedStates.State.PLANTING, UnifiedStates.State.WATERING:
            _动态AI频率调整_current_frequency = MIN_AI_FREQUENCY
        UnifiedStates.State.MOVING:
            if ai_is_enabled and is_instance_valid(_ai_think_timer):
                _ai_think_timer.paused = true
        _:
            if ai_is_enabled and is_instance_valid(_ai_think_timer):
                _ai_think_timer.paused = false

func _adjust_action_times_for_state(_old_state: UnifiedStates.State, new_state: UnifiedStates.State) -> void:
    """根据状态调整动作时间"""
    # 🆕 根据状态应用不同的效率调整
    match new_state:
        UnifiedStates.State.HARVESTING:
            # 收获状态时，如果有收获效率加成，应用它
            if _efficiency_multipliers.has("harvest_action"):
                var multiplier = _efficiency_multipliers["harvest_action"]
                print("[FarmerTimerManager] 收获状态：应用效率倍数 %.2f" % multiplier)
        UnifiedStates.State.PLANTING:
            # 种植状态时，如果有种植效率加成，应用它
            if _efficiency_multipliers.has("plant_action"):
                var multiplier = _efficiency_multipliers["plant_action"]
                print("[FarmerTimerManager] 种植状态：应用效率倍数 %.2f" % multiplier)

func _handle_state_transition_effects(old_state: UnifiedStates.State, new_state: UnifiedStates.State) -> void:
    """处理状态转换的特殊效果"""
    # 🆕 从工作状态转换到空闲状态时，记录工作完成
    if UnifiedStates.is_working_state(old_state) and new_state == UnifiedStates.State.IDLE:
        print("[FarmerTimerManager] 工作完成：%s -> IDLE" % UnifiedStates.get_state_name(old_state))

    # 🆕 从空闲状态转换到工作状态时，准备工作环境
    elif old_state == UnifiedStates.State.IDLE and UnifiedStates.is_working_state(new_state):
        print("[FarmerTimerManager] 开始工作：IDLE -> %s" % UnifiedStates.get_state_name(new_state))

func should_trigger_ai_for_state(state: UnifiedStates.State) -> bool:
    """检查指定状态是否应该触发AI决策"""
    match state:
        UnifiedStates.State.IDLE, UnifiedStates.State.CARRYING:
            return true
        UnifiedStates.State.MOVING:
            return false  # 移动时不触发AI
        _:
            return UnifiedStates.is_working_state(state) == false  # 非工作状态可以触发AI

func get_ai_frequency_for_state(state: UnifiedStates.State) -> float:
    """获取指定状态的AI决策频率"""
    match state:
        UnifiedStates.State.IDLE:
            return MAX_AI_FREQUENCY  # 空闲时最频繁
        UnifiedStates.State.CARRYING:
            return (MAX_AI_FREQUENCY + MIN_AI_FREQUENCY) / 2.0  # 携带时中等频率
        _:
            return MIN_AI_FREQUENCY  # 其他状态最低频率
