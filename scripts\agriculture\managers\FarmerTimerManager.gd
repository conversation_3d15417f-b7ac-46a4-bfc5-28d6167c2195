# FarmerTimerManager.gd
# 农夫计时器管理器 - 管理农夫的AI决策频率和动作计时
class_name <PERSON><PERSON>imerManager
extends Node

## 依赖和常量 ##
const UnifiedStates = preload("res://scripts/core/UnifiedCharacterStates.gd")

# 动作时间配置
const ACTION_TIMES = {
    "harvest_action": GameConstants.AgricultureConstants.HARVEST_BASE_TIME,
    "plant_action": GameConstants.AgricultureConstants.PLANT_BASE_TIME,
    "collect_action": GameConstants.AgricultureConstants.COLLECT_BASE_TIME,
    "store_action": GameConstants.AgricultureConstants.STORE_BASE_TIME,
    "fetch_water_action": GameConstants.AgricultureConstants.FETCH_WATER_BASE_TIME,
    "water_action": GameConstants.AgricultureConstants.WATER_BASE_TIME,
    "thinking": 0.5,
    "resting": 5.0
}

# AI频率常量
const MAX_AI_FREQUENCY: float = 1.0
const MIN_AI_FREQUENCY: float = 3.0

## 信号定义 ##
signal action_completed(action_type: String, action_data: Dictionary)
signal ai_decision_triggered()

## 核心属性 ##
# 统一状态系统
var current_unified_state: UnifiedStates.State = UnifiedStates.State.IDLE

# 角色引用
var host_farmer: Farmer = null

# AI控制
var ai_is_enabled: bool = false
var _current_ai_frequency: float = MAX_AI_FREQUENCY

# 动作控制
var is_action_in_progress: bool = false
var _current_action_type: String = ""
var _current_action_data: Dictionary = {}

# 计时器
var _ai_think_timer: Timer = null
var _action_timer: Timer = null

# 简化的统计
var _interaction_statistics: Dictionary = {}

# 初始化状态
var _is_initialized: bool = false

## 生命周期方法 ##
func _ready() -> void:
    name = "FarmerTimerManager"

func initialize(farmer_instance, _data_manager: Node = null) -> bool:
    if not is_instance_valid(farmer_instance):
        return false

    host_farmer = farmer_instance
    _create_internal_timers()
    _initialize_statistics()
    _is_initialized = true
    return true

func cleanup() -> void:
    disable_ai()

    if is_instance_valid(_ai_think_timer):
        _ai_think_timer.queue_free()

    if is_instance_valid(_action_timer):
        _action_timer.queue_free()

    _interaction_statistics.clear()
    _is_initialized = false

## AI控制系统 ##
func _should_trigger_ai() -> bool:
    """判断是否应该触发AI决策 - 简化版本"""
    if not is_instance_valid(host_farmer) or is_action_in_progress:
        return false

    # 检查状态是否适合AI决策
    match current_unified_state:
        UnifiedStates.State.IDLE, UnifiedStates.State.CARRYING:
            return true
        UnifiedStates.State.MOVING:
            return false
        _:
            return false

func _adjust_ai_frequency_for_state(state: UnifiedStates.State) -> void:
    """根据状态调整AI频率 - 简化版本"""
    match state:
        UnifiedStates.State.IDLE:
            _current_ai_frequency = MAX_AI_FREQUENCY
        UnifiedStates.State.CARRYING:
            _current_ai_frequency = (MAX_AI_FREQUENCY + MIN_AI_FREQUENCY) / 2.0
        _:
            _current_ai_frequency = MIN_AI_FREQUENCY

    # 更新计时器频率
    if is_instance_valid(_ai_think_timer):
        _ai_think_timer.wait_time = _current_ai_frequency

## 简化的统计系统 ##
func _initialize_statistics() -> void:
    """初始化统计系统"""
    _interaction_statistics = {
        "total_interactions": 0,
        "successful_interactions": 0
    }

func record_interaction_start(_interaction_type: String, _target: Node, _params: Dictionary) -> void:
    """记录交互开始 - 简化版本"""
    _interaction_statistics["total_interactions"] += 1

func record_interaction_completion(_interaction_type: String, success: bool, _result: Dictionary) -> void:
    """记录交互完成 - 简化版本"""
    if success:
        _interaction_statistics["successful_interactions"] += 1

func get_interaction_statistics() -> Dictionary:
    """获取交互统计信息"""
    return _interaction_statistics.duplicate()


# 删除复杂的记录完成方法 - 已在上面简化实现

## AI控制方法 ##
func enable_ai() -> void:
    """启用AI决策"""
    if not _is_initialized or ai_is_enabled:
        return
    
    ai_is_enabled = true
    
    if is_instance_valid(_ai_think_timer):
        _ai_think_timer.start()

func disable_ai() -> void:
    """禁用AI决策"""
    ai_is_enabled = false
    
    if is_instance_valid(_ai_think_timer):
        _ai_think_timer.stop()

func is_ai_enabled() -> bool:
    """检查AI是否启用"""
    return ai_is_enabled

## 动作计时器方法 ##
func start_action_timer(action_type: String, custom_duration_or_data = null, action_data: Dictionary = {}) -> void:
    """启动动作计时器 - 🔧 支持多种调用方式，修复瞬间完成问题"""
    if not _is_initialized:
        return
    
    # 🔧 强制清理之前的动作状态
    if is_action_in_progress:
        # 如果有正在进行的动作，先完成它
        _complete_current_action(false)
    
    is_action_in_progress = true
    _current_action_type = action_type
    
    # 🔧 智能参数处理 - 支持多种调用方式
    var duration: float = 0.0
    var final_action_data: Dictionary = {}
    
    if custom_duration_or_data == null:
        # 调用方式: start_action_timer("harvest_action")
        final_action_data = action_data
    elif custom_duration_or_data is float or custom_duration_or_data is int:
        # 调用方式: start_action_timer("harvest_action", 2.0, {})
        duration = float(custom_duration_or_data)
        final_action_data = action_data
    elif custom_duration_or_data is Dictionary:
        # 调用方式: start_action_timer("harvest_action", {"interaction_type": "harvest"})
        final_action_data = custom_duration_or_data
    else:
        # 未知类型，使用默认值
        final_action_data = action_data
    
    # 🔧 确保使用正确的动作时间
    if duration <= 0.0:
        duration = ACTION_TIMES.get(action_type, 1.0)
        print("[FarmerTimerManager] 使用预设动作时间: %s = %.1f秒" % [action_type, duration])
    else:
        print("[FarmerTimerManager] 使用自定义动作时间: %s = %.1f秒" % [action_type, duration])
    
    # 🆕 关键修复：强制最小时间，防止瞬间完成
    duration = max(duration, 0.8)  # 最少0.8秒
    
    _current_action_data = final_action_data
    
    if is_instance_valid(_action_timer):
        # 🆕 确保计时器完全停止 - 移除可能有问题的await
        _action_timer.stop()
        
        # 🔧 直接设置和启动，避免异步时序问题
        _action_timer.wait_time = duration
        _action_timer.start()
        
        print("[FarmerTimerManager] 🔧 计时器已启动: %s, 实际时长: %.1f秒" % [action_type, duration])
        
        # 记录交互开始
        if action_type.ends_with("_action"):
            var interaction_type = action_type.replace("_action", "")
            record_interaction_start(interaction_type, null, final_action_data)

# 🆕 新增辅助方法以获取动作时间
func get_action_duration(action_type: String) -> float:
    """获取指定动作类型的预设时长"""
    return ACTION_TIMES.get(action_type, 1.0)

## 🆕 装备效果支持方法 - 伐木工黄金标准 ##
func get_effective_action_time(action_type: String) -> float:
    """获取考虑装备效果的动作时间 - 伐木工黄金标准"""
    var adjusted_key = action_type + "_adjusted"
    if has_meta(adjusted_key):
        return get_meta(adjusted_key)
    return ACTION_TIMES.get(action_type, 1.0)

func update_efficiency_multiplier(action_type: String, multiplier: float) -> void:
    """更新效率倍数 - 由农夫调用"""
    print("[FarmerTimerManager] %s 效率更新: %.3f" % [action_type, multiplier])

    # 动态调整对应动作的时间
    if ACTION_TIMES.has(action_type):
        var original_time = ACTION_TIMES[action_type]
        var new_time = original_time / multiplier
        print("[FarmerTimerManager] %s 时间调整: %.2f -> %.2f 秒" % [action_type, original_time, new_time])

        # 临时存储调整后的时间
        set_meta(action_type + "_adjusted", new_time)

func update_action_times(time_updates: Dictionary) -> void:
    """批量更新动作时间 - 由农夫调用"""
    for action_name in time_updates:
        var new_time = time_updates[action_name]
        print("[FarmerTimerManager] 动作时间更新: %s -> %.2f 秒" % [action_name, new_time])
        set_meta(action_name + "_adjusted", new_time)

func stop_action_timer() -> void:
    """停止动作计时器"""
    if is_instance_valid(_action_timer):
        _action_timer.stop()
    
    _complete_current_action(false)

func is_busy() -> bool:
    """检查是否忙碌"""
    return is_action_in_progress

## 内部方法 ##
func _create_internal_timers() -> void:
    """创建内部计时器"""
    # AI思考计时器
    _ai_think_timer = Timer.new()
    _ai_think_timer.name = "AIThinkTimer"
    _ai_think_timer.wait_time = _current_ai_frequency
    _ai_think_timer.timeout.connect(_on_ai_think_timer_timeout)
    add_child(_ai_think_timer)
    
    # 动作计时器
    _action_timer = Timer.new()
    _action_timer.name = "ActionTimer"
    _action_timer.one_shot = true
    _action_timer.timeout.connect(_on_action_timer_timeout)
    add_child(_action_timer)

func _on_ai_think_timer_timeout() -> void:
    """AI思考计时器超时处理"""
    if not ai_is_enabled or not _should_trigger_ai():
        return

    # 触发AI决策
    ai_decision_triggered.emit()

func _on_action_timer_timeout() -> void:
    """动作计时器超时处理"""
    _complete_current_action(true)

func _complete_current_action(success: bool) -> void:
    """完成当前动作"""
    if not is_action_in_progress:
        return
    
    is_action_in_progress = false
    
    var action_type = _current_action_type
    var action_data = _current_action_data
    
    # 简化的交互记录
    if action_type.ends_with("_action") and success:
        _interaction_statistics["successful_interactions"] += 1
    
    # 发送完成信号
    action_completed.emit(action_type, action_data)
    
    # 清理当前动作数据
    _current_action_type = ""
    _current_action_data.clear()

## 状态更新方法 ##


## 统一状态系统支持 ##

func update_unified_state(new_state: UnifiedStates.State) -> void:
    """更新统一状态 - 新的状态同步接口"""
    if current_unified_state == new_state:
        return

    var old_state = current_unified_state
    current_unified_state = new_state

    # 根据新状态调整计时器行为
    _handle_unified_state_change(old_state, new_state)

    if OS.is_debug_build():
        print("[FarmerTimerManager] 统一状态更新: %s -> %s" % [
            UnifiedStates.get_state_name(old_state),
            UnifiedStates.get_state_name(new_state)
        ])

func get_unified_state() -> UnifiedStates.State:
    """获取当前统一状态"""
    return current_unified_state

func _handle_unified_state_change(_old_state: UnifiedStates.State, new_state: UnifiedStates.State) -> void:
    """处理统一状态变化 - 简化版本"""
    # 调整AI频率
    _adjust_ai_frequency_for_state(new_state)

    # 处理移动状态的特殊逻辑
    if new_state == UnifiedStates.State.MOVING:
        if ai_is_enabled and is_instance_valid(_ai_think_timer):
            _ai_think_timer.paused = true
    else:
        if ai_is_enabled and is_instance_valid(_ai_think_timer):
            _ai_think_timer.paused = false
