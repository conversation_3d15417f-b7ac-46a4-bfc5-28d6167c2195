# Farmer.gd
# 农夫角色 - 继承WorkerCharacter，实现农夫特有功能
class_name Farmer
extends WorkerCharacter

# 预加载依赖脚本
const FarmerTaskManagerClass = preload("res://scripts/agriculture/managers/FarmerTaskManager.gd")
const FarmerInteractionManagerClass = preload("res://scripts/agriculture/managers/FarmerInteractionManager.gd")
const FarmerTimerManagerClass = preload("res://scripts/agriculture/managers/FarmerTimerManager.gd")

# 🆕 统一状态系统
const UnifiedStates = preload("res://scripts/core/UnifiedCharacterStates.gd")

## 信号定义 ##
# 🆕 统一状态系统信号
signal unified_state_changed(old_state: UnifiedStates.State, new_state: UnifiedStates.State)

## 统一状态系统 ##
# 农夫使用 UnifiedCharacterStates.State 统一状态枚举

## 属性定义 ##
# 工作流状态属性 - 简化命名
var _work_progress: Dictionary = {}
var _current_farm_workflow: Dictionary = {}
var _workflow_completed: bool = false

# 农夫属性配置
var preferred_crop_type: String = ""  # 🆕 从配置文件读取默认作物

# 🆕 统一状态系统 - 唯一的状态管理
var _unified_state: UnifiedStates.State = UnifiedStates.State.IDLE

## 生命周期方法 ##
func _ready() -> void:
    # 添加到农夫组
    add_to_group("farmers")

    # 设置农夫特有配置 - 使用正确的枚举值
    character_type = CharacterManager.CharacterType.FARMER

    # 🆕 初始化统一状态系统
    _init_unified_state_system()

    # 🆕 从配置文件动态加载收集物品类型
    _load_collectible_item_types()

    # 🆕 从配置文件加载默认作物类型
    _load_default_crop_type()

    # 关键修复：设置动画映射 - 这是动画播放的核心配置
    _character_state_to_animation = {
        CharacterSpecificState.IDLE: "idle",
        CharacterSpecificState.MOVING: "walk",
        CharacterSpecificState.WORKING_1: "harvest",     # 收获动作
        CharacterSpecificState.WORKING_2: "plant",       # 种植动作  
        CharacterSpecificState.WORKING_3: "water",       # 浇水动作
        CharacterSpecificState.COLLECTING: "collecting",
        CharacterSpecificState.STORING: "storing",
        CharacterSpecificState.CARRYING: "walk_carry",
        CharacterSpecificState.RESTING: "idle"
    }
    
    # 设置忙碌状态
    _busy_character_states = [
        CharacterSpecificState.WORKING_1,  # 收获
        CharacterSpecificState.WORKING_2,  # 种植
        CharacterSpecificState.WORKING_3,  # 浇水
        CharacterSpecificState.COLLECTING,
        CharacterSpecificState.STORING
    ]
    
    # 调用父类初始化
    super._ready()

func _create_manager_instances() -> void:
    """创建三管理器实例"""
    if not is_instance_valid(task_manager):
        task_manager = FarmerTaskManagerClass.new()
        task_manager.name = "FarmerTaskManager"
        add_child(task_manager)

    if not is_instance_valid(interaction_manager):
        interaction_manager = FarmerInteractionManagerClass.new()
        interaction_manager.name = "FarmerInteractionManager"
        add_child(interaction_manager)

    if not is_instance_valid(timer_manager):
        timer_manager = FarmerTimerManagerClass.new()
        timer_manager.name = "FarmerTimerManager"
        add_child(timer_manager)

func _initialize_managers() -> bool:
    """初始化管理器"""
    var success = true
    
    # 初始化任务管理器
    if is_instance_valid(task_manager):
        if not task_manager.initialize(self, interaction_manager, timer_manager):
            success = false
    
    # 初始化交互管理器
    if is_instance_valid(interaction_manager):
        if not interaction_manager.initialize(self, task_manager, timer_manager):
            success = false
    
    # 初始化计时器管理器
    if is_instance_valid(timer_manager):
        if not timer_manager.initialize(self):
            success = false
    
    return success

func _connect_manager_signals() -> void:
    """连接管理器信号"""
    # 连接计时器管理器信号
    if is_instance_valid(timer_manager):
        if timer_manager.has_signal("ai_decision_triggered"):
            if not timer_manager.ai_decision_triggered.is_connected(_on_ai_decision_triggered):
                timer_manager.ai_decision_triggered.connect(_on_ai_decision_triggered)

## 核心技术实现区 ##

# 简化的携带图标显示
func _show_carrying_icon(resource_type_id: String) -> void:
    """显示携带物品图标 - 使用DataManager Atlas配置"""
    _hide_carrying_icon()

    _carrying_icon_sprite = Sprite2D.new()
    _carrying_icon_sprite.name = "CarriedItemIcon"
    _carrying_icon_sprite.offset = Vector2(0, -20)  # 在农夫头上显示
    _carrying_icon_sprite.scale = Vector2(2, 2)

    if OS.is_debug_build():
        print("[Farmer] 尝试显示携带图标，资源类型: %s" % resource_type_id)

    # 使用DataManager标准方法获取Atlas配置
    var data_manager = _get_data_manager_reference()
    if data_manager and data_manager.has_method("get_item_icon_region_data"):
        var icon_data = data_manager.get_item_icon_region_data(resource_type_id)

        if OS.is_debug_build():
            print("[Farmer] DataManager返回的图标数据: %s" % icon_data)

        if not icon_data.is_empty() and icon_data.has("texture_path"):
            var texture_path = icon_data.texture_path
            if ResourceLoader.exists(texture_path):
                var main_texture = load(texture_path) as Texture2D
                if main_texture:
                    # 创建AtlasTexture
                    var atlas = AtlasTexture.new()
                    atlas.atlas = main_texture
                    atlas.region = icon_data.get("region", Rect2(0, 0, 16, 16))
                    if icon_data.has("margin"):
                        atlas.margin = icon_data.get("margin")

                    _carrying_icon_sprite.texture = atlas
                    add_child(_carrying_icon_sprite)

                    if OS.is_debug_build():
                        print("[Farmer] 成功加载Atlas图标: %s, 区域: %s" % [texture_path, atlas.region])
                    return

    # 回退方案：尝试直接加载PNG（如果存在）
    var fallback_paths = [
        "res://assets/ui/icons/default_item.png",
        "res://assets/items/%s.png" % resource_type_id
    ]

    for path in fallback_paths:
        if ResourceLoader.exists(path):
            _carrying_icon_sprite.texture = load(path)
            add_child(_carrying_icon_sprite)
            if OS.is_debug_build():
                print("[Farmer] 使用回退图标: %s" % path)
            return

    if OS.is_debug_build():
        print("[Farmer] 警告：无法找到任何携带图标，资源类型: %s" % resource_type_id)

func _hide_carrying_icon() -> void:
    """隐藏携带物品图标"""
    if is_instance_valid(_carrying_icon_sprite):
        _carrying_icon_sprite.queue_free()
        _carrying_icon_sprite = null

func _get_data_manager_reference() -> Node:
    """获取DataManager引用 - 简化版本"""
    var game_manager = get_tree().root.get_node_or_null("_GameManager")
    if game_manager and game_manager.has_method("get_data_manager"):
        return game_manager.get_data_manager()
    return null

## 简化的配置加载方法 ##
func _load_collectible_item_types() -> void:
    """加载可收集物品类型 - 简化版本"""
    # 直接使用默认值，简化配置加载逻辑
    collectible_item_types = ["wheat_item", "corn_item", "potato_item", "carrot_item", "tomato_item"]

func get_collectible_item_types() -> Array:
    """获取农夫可以收集的物品类型列表"""
    return collectible_item_types.duplicate()

func _load_default_crop_type() -> void:
    """加载默认作物类型 - 简化版本"""
    preferred_crop_type = "wheat"  # 简化为固定默认值

## 🆕 统一状态系统方法 ##
func _init_unified_state_system() -> void:
    """初始化统一状态系统"""
    # 设置初始状态
    _unified_state = UnifiedStates.State.IDLE

    # 统一状态系统初始化完成

func change_unified_state(new_state: UnifiedStates.State) -> bool:
    """更改统一状态 - 新的状态管理入口"""
    if not UnifiedStates.is_valid_state(new_state):
        push_error("Farmer: 无效的统一状态 %d" % new_state)
        return false

    if _unified_state == new_state:
        return false

    # 验证状态转换
    if not UnifiedStates.is_valid_transition(_unified_state, new_state):
        push_warning("Farmer: 无效的状态转换 %s -> %s" % [
            UnifiedStates.get_state_name(_unified_state),
            UnifiedStates.get_state_name(new_state)
        ])
        return false

    var old_state = _unified_state
    _unified_state = new_state

    # 发送统一状态变化信号
    unified_state_changed.emit(old_state, new_state)

    # 同步到基类状态
    var base_char_state = UnifiedStates.to_base_character_state(new_state)
    if base_char_state != current_character_state:
        change_character_state(base_char_state)

    # 更新动画
    var animation = UnifiedStates.get_animation_for_state(new_state)
    _update_animation_for_unified_state(animation)

    # 同步状态到管理器
    sync_state_with_managers()

    if OS.is_debug_build():
        print("[Farmer] 统一状态变更: %s -> %s" % [
            UnifiedStates.get_state_name(old_state),
            UnifiedStates.get_state_name(new_state)
        ])

    return true

func get_unified_state() -> UnifiedStates.State:
    """获取当前统一状态"""
    return _unified_state



func _update_animation_for_unified_state(animation_name: String) -> void:
    """根据统一状态更新动画"""
    if is_instance_valid(_animated_sprite) and _animated_sprite.animation != animation_name:
        _animated_sprite.play(animation_name)
        if OS.is_debug_build():
            print("[Farmer] 统一状态动画: %s" % animation_name)

func _apply_unified_icon_effects(sprite: Sprite2D, _resource_type_id: String) -> void:
    """应用统一的图标效果"""
    if not is_instance_valid(sprite):
        return
    
    # 添加轻微的浮动动画
    var tween = create_tween()
    tween.set_loops()
    tween.tween_property(sprite, "offset:y", sprite.offset.y - 5, 1.0)
    tween.tween_property(sprite, "offset:y", sprite.offset.y, 1.0)

# 简化的状态同步
func sync_state_with_managers() -> void:
    """与管理器同步状态 - 简化版本"""
    if not _is_initialized:
        return

    # 只保留真正需要的统一状态同步
    var unified_state = get_unified_state()

    # 同步到TaskManager
    if is_instance_valid(task_manager) and task_manager.has_method("update_unified_state"):
        task_manager.update_unified_state(unified_state)

    # 同步到InteractionManager
    if is_instance_valid(interaction_manager) and interaction_manager.has_method("update_unified_state"):
        interaction_manager.update_unified_state(unified_state)

    # 同步到TimerManager
    if is_instance_valid(timer_manager) and timer_manager.has_method("update_unified_state"):
        timer_manager.update_unified_state(unified_state)

# 删除空洞的状态同步方法 - 这些方法创建数据但不使用，属于过度设计



## 简化的状态方法 ##
# 保留最常用的几个状态方法
func set_idle() -> bool:
    """设置为空闲状态"""
    return change_unified_state(UnifiedStates.State.IDLE)

func set_moving() -> bool:
    """设置为移动状态"""
    return change_unified_state(UnifiedStates.State.MOVING)

func set_carrying() -> bool:
    """设置为携带状态"""
    return change_unified_state(UnifiedStates.State.CARRYING)

func is_working_state() -> bool:
    """检查是否在工作"""
    # 检查是否为工作状态 - 使用状态分组判断
    return _unified_state in [
        UnifiedStates.State.HARVESTING,
        UnifiedStates.State.PLANTING,
        UnifiedStates.State.WATERING,
        UnifiedStates.State.COLLECTING,
        UnifiedStates.State.STORING,
        UnifiedStates.State.FETCHING_WATER
    ]

func start_farm_workflow(target: Node, workflow_type: String) -> void:
    """开始农场工作流程 - 简化版本"""
    _current_farm_workflow = {
        "target": target,
        "type": workflow_type,
        "start_time": Time.get_unix_time_from_system(),
        "status": "active"
    }

    _workflow_completed = false

    # 简化状态设置 - 直接使用change_unified_state
    match workflow_type:
        "harvest":
            change_unified_state(UnifiedStates.State.HARVESTING)
        "plant":
            change_unified_state(UnifiedStates.State.PLANTING)
        "water":
            change_unified_state(UnifiedStates.State.WATERING)
        "collect":
            change_unified_state(UnifiedStates.State.COLLECTING)
        "store":
            change_unified_state(UnifiedStates.State.STORING)
        "fetch_water":
            change_unified_state(UnifiedStates.State.FETCHING_WATER)
        _:
            set_idle()

func complete_farm_workflow() -> void:
    """完成农场工作流程 - 使用统一状态系统"""
    _workflow_completed = true
    _current_farm_workflow.clear()

    # 🆕 重置状态到合适的状态
    if is_carrying_resource():
        set_carrying()
    else:
        set_idle()

func is_in_workflow() -> bool:
    """检查是否在工作流程中"""
    return not _current_farm_workflow.is_empty()

func get_workflow_progress() -> Dictionary:
    """获取工作流程进度"""
    return _work_progress.duplicate()

func set_preferred_crop_type(crop_type: String) -> void:
    """设置偏好的作物类型"""
    preferred_crop_type = crop_type

func get_preferred_crop_type() -> String:
    """获取偏好的作物类型"""
    return preferred_crop_type

## 资源管理方法 ##
func receive_resource(resource_type: String, amount: int) -> bool:
    """接收资源"""
    if not can_carry_more():
        return false
    
    # 修复累加逻辑
    if is_carrying_resource():
        var current_type = get_carrying_resource_type()
        var current_amount = get_carrying_amount()
        
        if current_type == resource_type:
            # 同类型资源，累加
            var new_amount = current_amount + amount
            if new_amount <= GameConstants.AgricultureConstants.FARMER_MAX_CARRYING_CAPACITY:
                set_carrying_resource(resource_type, new_amount)
            else:
                return false
        else:
            # 不同类型资源，无法接收
            return false
    else:
        # 没有携带资源，直接设置
        set_carrying_resource(resource_type, amount)
    
    # 🆕 更新携带状态 - 使用统一状态
    if is_carrying_resource():
        set_carrying()
        _show_carrying_icon(resource_type)

    return true

func can_carry_more() -> bool:
    """检查是否可以携带更多资源"""
    if not is_carrying_resource():
        return true
    
    var current_amount = get_carrying_amount()
    return current_amount < GameConstants.AgricultureConstants.FARMER_MAX_CARRYING_CAPACITY

func clear_carrying_resource() -> void:
    """清空携带资源"""
    # 修复：使用基类正确的方法
    super.clear_carrying_resource()
    _hide_carrying_icon()
    
    # 🆕 更新状态 - 使用统一状态
    if _unified_state == UnifiedStates.State.CARRYING:
        set_idle()

## 移动相关方法重写 ##
func move_to(target_position: Vector2, move_params: Dictionary = {}) -> bool:
    """移动到目标位置 - 🆕 使用统一状态"""
    set_moving()
    return super.move_to(target_position, move_params)

func _on_movement_completed(_move_params: Dictionary = {}) -> void:
    """移动完成回调 - 🆕 使用统一状态"""
    # 修复：移除不存在的super调用

    # 🆕 根据是否携带资源更新状态
    if is_carrying_resource():
        set_carrying()
    else:
        set_idle()

## AI决策相关 ##
func _on_ai_decision_triggered() -> void:
    """AI决策触发回调"""
    if not _is_initialized:
        return
    
    # 修复：调用任务管理器的公共触发方法，而不是私有的_decide_next_task
    if is_instance_valid(task_manager) and task_manager.has_method("trigger_ai_decision"):
        task_manager.trigger_ai_decision()
    elif is_instance_valid(task_manager) and task_manager.has_method("_safe_trigger_ai_decision"):
        task_manager._safe_trigger_ai_decision()

func enable_ai() -> void:
    """启用AI"""
    # 修复：移除不存在的super调用
    ai_enabled = true
    
    if is_instance_valid(timer_manager) and timer_manager.has_method("enable_ai"):
        timer_manager.enable_ai()

func disable_ai() -> void:
    """禁用AI"""
    # 修复：移除不存在的super调用
    ai_enabled = false
    
    if is_instance_valid(timer_manager) and timer_manager.has_method("disable_ai"):
        timer_manager.disable_ai()

## 交互相关方法 ##
func can_interact_with_farmland() -> bool:
    """检查是否可以与农田交互 - 🆕 使用统一状态"""
    return not _is_moving and _unified_state in [UnifiedStates.State.IDLE, UnifiedStates.State.CARRYING]

func can_harvest() -> bool:
    """检查是否可以收获"""
    return not is_carrying_resource() and can_interact_with_farmland()

func can_plant() -> bool:
    """检查是否可以种植"""
    return not is_carrying_resource() and can_interact_with_farmland()

func can_store() -> bool:
    """检查是否可以存储"""
    return is_carrying_resource() and can_interact_with_farmland()

## 简化的调试方法 ##
func get_debug_info() -> Dictionary:
    """获取基本调试信息 - 简化版本"""
    return {
        "unified_state": UnifiedStates.get_state_name(_unified_state),
        "is_working": UnifiedStates.is_working_state(_unified_state),
        "is_carrying": is_carrying_resource(),
        "carrying_type": get_carrying_resource_type() if is_carrying_resource() else "",
        "preferred_crop": preferred_crop_type
    }

## 性能优化相关 ##
func _physics_process(_delta: float) -> void:
    """物理更新，执行状态同步"""
    # 修复：移除不存在的super调用
    
    # 定期同步状态（每隔一定帧数）
    if Engine.get_process_frames() % 60 == 0:  # 每秒同步一次
        sync_state_with_managers()

# 删除复杂的性能指标方法 - 对简单游戏来说过度复杂

## 装备效果应用系统 - 农夫特化实现 ##
func _on_attributes_updated(attributes: Dictionary) -> void:
    """属性更新后的回调 - 农夫特化处理"""
    super._on_attributes_updated(attributes)
    
    # 农夫特有的装备效果处理
    _update_farming_capabilities(attributes)
    _update_work_timers(attributes)
    _update_crop_preferences(attributes)

func _update_farming_capabilities(attributes: Dictionary) -> void:
    """更新农业能力"""
    # 更新收获加成
    if attributes.has("harvest_bonus_range"):
        var harvest_bonus = attributes["harvest_bonus_range"]
        print("Farmer: 收获加成更新为 %s" % harvest_bonus)
        
        # 通知相关管理器收获能力变化
        if is_instance_valid(interaction_manager) and interaction_manager.has_method("update_harvest_bonus"):
            interaction_manager.update_harvest_bonus(harvest_bonus)
    
    # 更新水桶容量
    if attributes.has("water_capacity_bonus"):
        var water_capacity = int(attributes["water_capacity_bonus"])
        print("Farmer: 水桶容量更新为 %d" % water_capacity)
        
        # 设置农夫的单次浇水能力
        set_meta("max_water_uses", max(1, water_capacity / 10.0))  # 每10容量可以浇水1次
    
    # 更新种植效率
    if attributes.has("work_efficiency_multiplier"):
        var work_efficiency = float(attributes["work_efficiency_multiplier"])
        print("Farmer: 工作效率更新为 %f" % work_efficiency)
        
        # 影响种植和收获的成功率
        set_meta("planting_success_rate", min(1.0, 0.8 + (work_efficiency - 1.0) * 0.5))
        set_meta("harvesting_efficiency", work_efficiency)

func _update_work_timers(attributes: Dictionary) -> void:
    """更新工作时间"""
    # 更新收获时间
    if attributes.has("harvest_speed_multiplier"):
        var harvest_speed = float(attributes["harvest_speed_multiplier"])
        var base_harvest_time = GameConstants.AgricultureConstants.FARMER_BASE_HARVEST_TIME
        var final_harvest_time = base_harvest_time / harvest_speed
        
        # 通知计时器管理器更新时间
        if is_instance_valid(timer_manager) and timer_manager.has_method("update_action_duration"):
            timer_manager.update_action_duration("harvest", final_harvest_time)
        
        print("Farmer: 收获时间更新为 %f 秒 (加速倍数: %f)" % [final_harvest_time, harvest_speed])
    
    # 更新种植时间
    if attributes.has("work_efficiency_multiplier"):
        var work_efficiency = float(attributes["work_efficiency_multiplier"])
        var base_plant_time = GameConstants.AgricultureConstants.FARMER_BASE_PLANT_TIME
        var final_plant_time = base_plant_time / work_efficiency
        
        if is_instance_valid(timer_manager) and timer_manager.has_method("update_action_duration"):
            timer_manager.update_action_duration("plant", final_plant_time)
        
        print("Farmer: 种植时间更新为 %f 秒 (效率倍数: %f)" % [final_plant_time, work_efficiency])

func _update_crop_preferences(attributes: Dictionary) -> void:
    """根据装备更新作物偏好"""
    # 根据装备特性调整作物偏好
    if attributes.has("crop_quality_multiplier"):
        var quality_bonus = float(attributes["crop_quality_multiplier"])
        var new_preferred_crop = _get_optimal_crop_for_quality_bonus(quality_bonus)

        if not new_preferred_crop.is_empty():
            set_preferred_crop_type(new_preferred_crop)
            print("Farmer: 根据品质加成 %f 调整作物偏好为 %s" % [quality_bonus, preferred_crop_type])

func _get_optimal_crop_for_quality_bonus(quality_bonus: float) -> String:
    """根据品质加成获取最优作物类型 - 简化版本"""
    # 简化为硬编码逻辑，避免复杂计算
    if quality_bonus > 1.2:
        return "tomato"
    elif quality_bonus > 1.0:
        return "corn"
    else:
        return "wheat"

## 装备效果查询方法 ##
func get_effective_harvest_amount(base_amount: int) -> int:
    """计算有效收获数量（应用装备加成）"""
    var harvest_bonus = get_effective_harvest_bonus()
    
    if harvest_bonus == "0-0":
        return base_amount
    
    # 解析范围格式 "1-2" -> 在1到2之间随机
    var parts = harvest_bonus.split("-")
    if parts.size() == 2:
        var min_bonus = int(parts[0])
        var max_bonus = int(parts[1])
        var bonus = randi_range(min_bonus, max_bonus)
        return base_amount + bonus
    
    return base_amount

func get_effective_water_uses() -> int:
    """获取有效的浇水次数"""
    return get_meta("max_water_uses", 1)

func get_effective_planting_success_rate() -> float:
    """获取有效的种植成功率"""
    return get_meta("planting_success_rate", 0.8)

func get_effective_harvesting_efficiency() -> float:
    """获取有效的收获效率"""
    return get_meta("harvesting_efficiency", 1.0)

## 装备效果在实际工作中的应用 ##
func perform_harvest_with_equipment_bonus(farmland: Node) -> Dictionary:
    """执行装备加成的收获操作"""
    var result = {
        "success": false,
        "items": [],
        "bonus_applied": false
    }
    
    if not is_instance_valid(farmland):
        return result
    
    # 检查农田是否可收获
    if not farmland.has_method("can_harvest") or not farmland.can_harvest():
        return result
    
    # 应用收获效率影响成功率
    var efficiency = get_effective_harvesting_efficiency()
    var success_rate = min(
        GameConstants.AgricultureConstants.FARMER_MAX_SUCCESS_RATE,
        GameConstants.AgricultureConstants.FARMER_BASE_SUCCESS_RATE * efficiency
    )
    
    if randf() > success_rate:
        return result
    
    # 获取基础收获量
    var base_harvest = farmland.get_meta("harvest_amount", 1)
    
    # 应用装备收获加成
    var final_harvest_amount = get_effective_harvest_amount(base_harvest)
    
    if farmland.has_method("harvest"):
        var harvest_items = farmland.harvest()
        
        # 根据加成调整收获量
        if final_harvest_amount > base_harvest:
            result["bonus_applied"] = true
            
            # 增加额外的收获物品
            var bonus_amount = final_harvest_amount - base_harvest
            for i in range(bonus_amount):
                if harvest_items.size() > 0:
                    var bonus_item = harvest_items[0].duplicate()  # 复制第一个物品作为奖励
                    harvest_items.append(bonus_item)
        
        result["success"] = true
        result["items"] = harvest_items
    
    print("Farmer: 装备加成收获 - 基础:%d, 最终:%d, 加成生效:%s" % [
        base_harvest, final_harvest_amount, result["bonus_applied"]
    ])
    
    return result

func perform_planting_with_equipment_bonus(farmland: Node, crop_type: String) -> bool:
    """执行装备加成的种植操作"""
    if not is_instance_valid(farmland) or crop_type.is_empty():
        return false
    
    # 应用装备种植成功率
    var success_rate = get_effective_planting_success_rate()
    
    if randf() > success_rate:
        print("Farmer: 种植失败，成功率: %f" % success_rate)
        return false
    
    # 执行实际种植
    if farmland.has_method("plant"):
        var plant_success = farmland.plant(crop_type)
        if plant_success:
            print("Farmer: 装备加成种植成功，作物类型: %s，成功率: %f" % [crop_type, success_rate])
        return plant_success
    
    return false
