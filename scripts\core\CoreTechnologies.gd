# CoreTechnologies.gd
# 11项核心技术统一模块 - 所有角色系统共用的核心技术实现
# 基于农夫系统S+级别黄金标准提取
class_name CoreTechnologies

## 常量定义 ##
const DEFAULT_TIMEOUT_VALUES = {
    "crop_workflow": 15.0,
    "ore_workflow": 15.0,
    "tree_workflow": 15.0,
    "kitchen_workflow": 15.0,
    "fishing_workflow": 15.0,
    "item_lock": 3.0,
    "validation_cache": 3.0
}

const DEFAULT_SCORING_WEIGHTS = {
    "distance": 0.35,
    "maturity": 0.35,
    "availability": 0.20,
    "load": 0.10
}

const DEFAULT_SCORING_VALUES = {
    "distance_base": 1000.0,
    "maturity_base": 300.0,
    "availability_base": 200.0,
    "load_base": 200.0,
    "locked_penalty": 0.1
}

## 全局静态锁定字典 - 跨所有角色实例共享 ##
static var _global_locked_resources: Dictionary = {}
static var _global_locked_items: Dictionary = {}

## 🆕 1. 前置资源锁定机制 - 统一实现 ##

static func lock_target(target: Node, worker: Node, lock_type: String, timeout_values: Dictionary = DEFAULT_TIMEOUT_VALUES) -> bool:
    """统一的资源锁定机制 - 解决并发控制问题"""
    if not is_instance_valid(target) or not is_instance_valid(worker):
        return false
    
    var target_id = target.get_instance_id()
    var worker_id = worker.get_instance_id()
    var current_time = Time.get_unix_time_from_system()
    
    var lock_dict = _get_lock_dictionary(lock_type)
    var timeout = timeout_values.get(lock_type + "_workflow", timeout_values.get("item_lock", 15.0))
    
    # 检查是否已被其他工人锁定
    if lock_dict.has(target_id):
        var lock_info = lock_dict[target_id]
        if lock_info.worker_id == worker_id:
            lock_info.lock_time = current_time  # 刷新锁定时间
            return true
        elif current_time - lock_info.lock_time <= timeout:
            return false  # 被其他工人锁定且未超时
    
    # 创建锁定信息
    lock_dict[target_id] = {
        "worker_id": worker_id,
        "worker_type": _get_worker_type(worker),
        "lock_time": current_time,
        "timeout": timeout,
        "stage": "active"
    }
    
    # 🚨 关键：同步设置目标对象的本地锁定状态
    _sync_local_lock_state(target, lock_type, true)
    
    return true

static func is_locked_by_others(target: Node, worker: Node, lock_type: String, timeout_values: Dictionary = DEFAULT_TIMEOUT_VALUES) -> bool:
    """检查资源是否被其他工人锁定"""
    if not is_instance_valid(target) or not is_instance_valid(worker):
        return false
    
    var target_id = target.get_instance_id()
    var worker_id = worker.get_instance_id()
    var lock_dict = _get_lock_dictionary(lock_type)
    
    if lock_dict.has(target_id):
        var lock_info = lock_dict[target_id]
        
        # 检查锁定是否超时
        var current_time = Time.get_unix_time_from_system()
        var timeout = timeout_values.get(lock_type + "_workflow", timeout_values.get("item_lock", 15.0))
        
        if current_time - lock_info.lock_time > timeout:
            # 锁定已超时，清理锁定状态
            lock_dict.erase(target_id)
            _sync_local_lock_state(target, lock_type, false)
            return false
        
        # 锁定仍然有效，检查是否被其他工人锁定
        return lock_info.worker_id != worker_id
    
    return false

static func unlock_target(target: Node, lock_type: String) -> bool:
    """解锁资源 - 同步清理两套锁定状态"""
    if not is_instance_valid(target):
        return false
    
    var target_id = target.get_instance_id()
    var lock_dict = _get_lock_dictionary(lock_type)
    
    if lock_dict.has(target_id):
        lock_dict.erase(target_id)
        # 同步清理目标对象的本地锁定状态
        _sync_local_lock_state(target, lock_type, false)
        return true
    
    return false

static func cleanup_worker_locks(worker: Node) -> void:
    """清理指定工人的所有锁定"""
    if not is_instance_valid(worker):
        return
    
    var worker_id = worker.get_instance_id()
    
    # 清理资源锁定
    for target_id in _global_locked_resources.keys():
        var lock_info = _global_locked_resources[target_id]
        if lock_info.worker_id == worker_id:
            _global_locked_resources.erase(target_id)
    
    # 清理物品锁定
    for target_id in _global_locked_items.keys():
        var lock_info = _global_locked_items[target_id]
        if lock_info.worker_id == worker_id:
            _global_locked_items.erase(target_id)

## 🆕 2. 智能负载均衡算法 - 统一实现 ##

static func calculate_target_score(target: Node, worker: Node, scoring_config: Dictionary = {}) -> float:
    """四因素评分算法 - 统一的目标评分计算"""
    if not is_instance_valid(target) or not is_instance_valid(worker):
        return 0.0
    
    var weights = scoring_config.get("weights", DEFAULT_SCORING_WEIGHTS)
    var values = scoring_config.get("values", DEFAULT_SCORING_VALUES)
    var score = 0.0
    
    # 1. 距离因子
    var distance = worker.global_position.distance_to(target.global_position)
    var distance_score = values.distance_base / max(distance, 1.0)
    score += distance_score * weights.distance
    
    # 2. 成熟度/可用性因子
    var maturity_score = _calculate_maturity_score(target, values)
    score += maturity_score * weights.maturity
    
    # 3. 可用性因子
    var availability_score = _calculate_availability_score(target, values)
    score += availability_score * weights.availability
    
    # 4. 负载因子
    var load_score = values.load_base
    var lock_type = _determine_lock_type(target)
    if is_locked_by_others(target, worker, lock_type):
        load_score *= values.locked_penalty
    score += load_score * weights.load
    
    return score

static func find_best_target(candidates: Array, worker: Node, scoring_config: Dictionary = {}) -> Node:
    """统一的最佳目标选择算法"""
    if candidates.is_empty() or not is_instance_valid(worker):
        return null
    
    var best_target = null
    var best_score = -1.0
    
    for target in candidates:
        if not is_instance_valid(target):
            continue
        
        var score = calculate_target_score(target, worker, scoring_config)
        if score > best_score:
            best_score = score
            best_target = target
    
    return best_target

## 🆕 3. 动作连贯性优化 - 统一框架 ##

static func immediate_task_chain(interaction_type: String, task_manager: Node, chain_config: Dictionary) -> bool:
    """零延迟任务衔接 - 统一的动作连贯性框架"""
    if not is_instance_valid(task_manager):
        return false

    var chain_map = chain_config.get("chain_map", {})
    var next_action = chain_map.get(interaction_type, "")

    if next_action.is_empty():
        return false

    # 优先调用任务管理器的具体实现方法
    var method_name = "_动作连贯性_request_" + next_action + "_immediate"
    if task_manager.has_method(method_name):
        return task_manager.call(method_name)

    # 如果没有具体实现，使用通用处理
    match next_action:
        "collect":
            return _handle_collect_immediate(task_manager)
        "storage":
            return _handle_storage_immediate(task_manager)
        "watering_check":
            return _handle_watering_check(task_manager)
        "workflow_complete":
            _handle_workflow_complete(task_manager)
            return true
        "next_task":
            task_manager.call_deferred("_decide_next_task")
            return true

    return false

static func _handle_collect_immediate(task_manager: Node) -> bool:
    """处理立即收集请求"""
    if not is_instance_valid(task_manager):
        return false

    var worker = task_manager.get("farmer") if task_manager.has_method("get") else null
    if not is_instance_valid(worker):
        worker = task_manager.get("worker") if task_manager.has_method("get") else null

    if not is_instance_valid(worker):
        return false

    print("CoreTech: 动作连贯性 - 立即请求收集任务")

    # 🔧 统一方案：CoreTechnologies不再直接处理收集
    # 所有收集任务由FarmerTaskManager的专用动作连贯性方法处理
    return false

static func _handle_storage_immediate(task_manager: Node) -> bool:
    """处理立即存储请求"""
    if not is_instance_valid(task_manager):
        return false

    var worker = task_manager.get("farmer") if task_manager.has_method("get") else null
    if not is_instance_valid(worker):
        worker = task_manager.get("worker") if task_manager.has_method("get") else null

    if not is_instance_valid(worker):
        return false

    print("CoreTech: 动作连贯性 - 立即请求存储任务")

    if not worker.is_carrying_resource():
        print("CoreTech: 未携带资源，无法存储")
        return false

    # 查找最近的存储建筑
    var nearest_storage = _find_nearest_storage(worker)
    if is_instance_valid(nearest_storage):
        if task_manager.has_method("add_task"):
            var TaskType = task_manager.get("TaskType") if task_manager.has_method("get") else null
            var TaskPriority = task_manager.get("TaskPriority") if task_manager.has_method("get") else null
            if TaskType and TaskPriority:
                task_manager.add_task(TaskType.STORE_CROP, {"target_storage": nearest_storage}, TaskPriority.HIGHEST)
                return true

    return false

static func _handle_watering_check(task_manager: Node) -> bool:
    """处理浇水检查"""
    # 种植完成后，检查刚种植的农田是否需要浇水
    var current_task_data = task_manager.call("get_current_task") if task_manager.has_method("get_current_task") else {}
    var planted_farmland = current_task_data.get("params", {}).get("target_farmland")

    if is_instance_valid(planted_farmland):
        if planted_farmland.has_method("needs_water") and planted_farmland.needs_water():
            var worker = task_manager.get("farmer") if task_manager.has_method("get") else null
            if is_instance_valid(worker) and worker.is_carrying_resource() and worker.get_carrying_resource_type() == "water":
                if task_manager.has_method("add_task") and task_manager.has_method("set_meta"):
                    var TaskType = task_manager.get("TaskType") if task_manager.has_method("get") else null
                    var TaskPriority = task_manager.get("TaskPriority") if task_manager.has_method("get") else null
                    if TaskType and TaskPriority:
                        task_manager.add_task(TaskType.WATER_CROP, {"target_farmland": planted_farmland}, TaskPriority.HIGH)
                        task_manager.set_meta("pending_water_target", planted_farmland)
                        return true

    return false

static func _handle_workflow_complete(task_manager: Node) -> void:
    """处理工作流程完成"""
    print("CoreTech: 动作连贯性 - 完成工作流程循环")
    if task_manager.has_method("set"):
        task_manager.set("work_cycle_completed", true)

    var worker = task_manager.get("farmer") if task_manager.has_method("get") else null
    if not is_instance_valid(worker):
        worker = task_manager.get("worker") if task_manager.has_method("get") else null

    if is_instance_valid(worker) and worker.has_method("complete_farm_workflow"):
        worker.complete_farm_workflow()
    elif is_instance_valid(worker) and worker.has_method("complete_workflow"):
        worker.complete_workflow()

static func _find_nearby_dropped_items(worker: Node) -> Array:
    """查找附近的掉落物品"""
    var items = []
    if is_instance_valid(worker) and worker.has_method("get_tree"):
        var scene_tree = worker.get_tree()
        if is_instance_valid(scene_tree):
            var dropped_items = scene_tree.get_nodes_in_group("simple_dropped_items")
            for item in dropped_items:
                if is_instance_valid(item):
                    var distance = worker.global_position.distance_to(item.global_position)
                    if distance <= 200.0:  # 200像素范围内
                        items.append(item)
    return items

static func _find_nearest_storage(worker: Node) -> Node:
    """查找最近的存储建筑"""
    if not is_instance_valid(worker) or not worker.has_method("get_tree"):
        return null

    var scene_tree = worker.get_tree()
    if not is_instance_valid(scene_tree):
        return null

    var storages = scene_tree.get_nodes_in_group("storages")
    var nearest_storage = null
    var min_distance = INF

    for storage in storages:
        if is_instance_valid(storage):
            var distance = worker.global_position.distance_to(storage.global_position)
            if distance < min_distance:
                min_distance = distance
                nearest_storage = storage

    return nearest_storage

static func complete_workflow_cycle(worker: Node) -> void:
    """完成工作流程循环 - 统一实现"""
    if not is_instance_valid(worker):
        return
    
    # 设置工作循环完成标志
    if worker.has_method("set_work_cycle_completed"):
        worker.set_work_cycle_completed(true)
    
    # 调用角色特定的完成方法
    if worker.has_method("complete_workflow"):
        worker.complete_workflow()

## 🆕 辅助方法 ##

static func _get_lock_dictionary(lock_type: String) -> Dictionary:
    """获取对应类型的锁定字典"""
    if lock_type == "item":
        return _global_locked_items
    else:
        return _global_locked_resources

static func _sync_local_lock_state(target: Node, lock_type: String, locked: bool) -> void:
    """同步目标对象的本地锁定状态"""
    if not is_instance_valid(target):
        return
    
    # 根据锁定类型设置对应的本地状态
    match lock_type:
        "crop", "ore", "tree":
            if target.has_method("set") and "is_locked" in target:
                target.is_locked = locked
        "kitchen":
            if target.has_method("set") and "is_locked" in target:
                target.is_locked = locked
        "fishing":
            if target.has_method("set") and "locked_for_fishing" in target:
                target.locked_for_fishing = locked

static func _get_worker_type(worker: Node) -> String:
    """获取工人类型"""
    if worker.is_in_group("farmers"):
        return "farmer"
    elif worker.is_in_group("woodcutters"):
        return "woodcutter"
    elif worker.is_in_group("miners"):
        return "miner"
    elif worker.is_in_group("chefs"):
        return "chef"
    elif worker.is_in_group("fishermen"):
        return "fisherman"
    else:
        return "unknown"

static func _determine_lock_type(target: Node) -> String:
    """自动确定目标的锁定类型"""
    if target.is_in_group("farmlands") or target.is_in_group("crops"):
        return "crop"
    elif target.is_in_group("trees"):
        return "tree"
    elif target.is_in_group("ores") or target.is_in_group("mining_spots"):
        return "ore"
    elif target.is_in_group("kitchens"):
        return "kitchen"
    elif target.is_in_group("fishing_spots"):
        return "fishing"
    elif target.is_in_group("simple_dropped_items"):
        return "item"
    else:
        return "unknown"

static func _calculate_maturity_score(target: Node, values: Dictionary) -> float:
    """计算成熟度评分"""
    if target.has_method("get_growth_progress"):
        var progress = target.get_growth_progress()
        return progress * values.maturity_base
    elif target.has_method("get_resource_amount"):
        var amount = target.get_resource_amount()
        return min(amount * 10.0, values.maturity_base)
    else:
        return values.maturity_base * 0.5  # 默认中等评分

static func _calculate_availability_score(target: Node, values: Dictionary) -> float:
    """计算可用性评分"""
    var score = values.availability_base

    # 检查目标是否可用
    if target.has_method("is_available") and not target.is_available():
        score *= 0.1
    elif target.has_method("can_interact") and not target.can_interact():
        score *= 0.1

    return score

## 🆕 4. 动态AI频率调整 - 统一实现 ##

class AIFrequencyController:
    """动态AI频率调整控制器"""
    var _ai_tick_times: Array = []
    var _current_frequency: float = 1.0
    var _max_frequency: float = 1.0
    var _min_frequency: float = 3.0
    var _sample_size: int = 10

    func _init(max_freq: float = 1.0, min_freq: float = 3.0, sample_size: int = 10):
        _max_frequency = max_freq
        _min_frequency = min_freq
        _sample_size = sample_size
        _current_frequency = max_freq

    func should_trigger_ai(worker_state: String, has_pending_tasks: bool) -> bool:
        """智能判断是否应该触发AI决策"""
        # 根据状态判断是否应该触发AI
        match worker_state:
            "IDLE", "CARRYING":
                return not has_pending_tasks
            "MOVING":
                return false  # 移动时暂停AI
            _:
                return false  # 工作时降低AI频率

    func record_ai_tick(processing_time: float) -> void:
        """记录AI处理时间"""
        _ai_tick_times.append(processing_time)

        # 保持样本数量在合理范围内
        if _ai_tick_times.size() > _sample_size:
            _ai_tick_times = _ai_tick_times.slice(-_sample_size)

        # 定期调整频率
        if _ai_tick_times.size() >= 5:
            _adjust_frequency()

    func _adjust_frequency() -> void:
        """动态调整AI决策频率"""
        if _ai_tick_times.size() < 3:
            return

        # 计算平均AI处理时间
        var total_time = 0.0
        for time in _ai_tick_times:
            total_time += time
        var avg_time = total_time / _ai_tick_times.size()

        # 根据处理时间调整频率
        if avg_time > 0.1:  # 处理时间过长，降低频率
            _current_frequency = min(_current_frequency * 1.2, _min_frequency)
        elif avg_time < 0.05:  # 处理时间很短，提高频率
            _current_frequency = max(_current_frequency * 0.9, _max_frequency)

    func get_current_frequency() -> float:
        return _current_frequency

    func adjust_for_state(state: String) -> void:
        """根据状态调整频率"""
        match state:
            "IDLE":
                _current_frequency = _max_frequency
            "WORKING", "HARVESTING", "PLANTING", "CHOPPING":
                _current_frequency = _min_frequency
            "MOVING":
                pass  # 移动时保持当前频率

    func cleanup() -> void:
        """清理频率控制器"""
        _ai_tick_times.clear()
        _current_frequency = _max_frequency

## 🆕 5. 交互记录系统 - 统一实现 ##

class InteractionRecorder:
    """交互记录系统 - 双重记录机制"""
    var _statistics: Dictionary = {}
    var _history: Array = []
    var _session_start_time: float
    var _record_limit: int = 50

    func _init(record_limit: int = 50):
        _record_limit = record_limit
        _session_start_time = Time.get_unix_time_from_system()
        _history.clear()
        _statistics.clear()

    func record_attempt(interaction_type: String, target: Node, params: Dictionary) -> void:
        """记录交互尝试"""
        var record = {
            "id": _generate_interaction_id(),
            "type": interaction_type,
            "target_id": target.get_instance_id() if is_instance_valid(target) else 0,
            "start_time": Time.get_unix_time_from_system(),
            "end_time": 0.0,
            "success": false,
            "params": params.duplicate()
        }

        _history.append(record)

        # 保持记录数量在限制内
        if _history.size() > _record_limit:
            _history = _history.slice(-_record_limit)

    func record_completion(interaction_type: String, success: bool, additional_data: Dictionary = {}) -> void:
        """记录交互完成"""
        var record = _find_latest_incomplete_record(interaction_type)
        if record.is_empty():
            return

        var end_time = Time.get_unix_time_from_system()
        record.end_time = end_time
        record.duration = end_time - record.start_time
        record.success = success
        record.data = additional_data

        # 更新统计数据
        _update_statistics(interaction_type, record.duration, success)

    func get_statistics() -> Dictionary:
        """获取交互统计信息"""
        return _statistics.duplicate()

    func get_history() -> Array:
        """获取交互历史记录"""
        return _history.duplicate()

    func _find_latest_incomplete_record(interaction_type: String) -> Dictionary:
        """查找最新的未完成记录"""
        for i in range(_history.size() - 1, -1, -1):
            var record = _history[i]
            if record.type == interaction_type and record.end_time == 0.0:
                return record
        return {}

    func _update_statistics(interaction_type: String, duration: float, success: bool) -> void:
        """更新交互统计数据"""
        if not _statistics.has(interaction_type):
            _statistics[interaction_type] = {
                "total": 0, "successful": 0, "total_duration": 0.0
            }

        var stats = _statistics[interaction_type]
        stats.total += 1
        stats.total_duration += duration

        if success:
            stats.successful += 1

    func _generate_interaction_id() -> String:
        """生成唯一的交互ID"""
        return "interaction_" + str(Time.get_unix_time_from_system()) + "_" + str(randi() % 1000)

    func cleanup() -> void:
        """清理记录系统"""
        _history.clear()
        _statistics.clear()

## 🆕 6. 建筑交互验证 - 统一实现 ##

class BuildingValidator:
    """建筑交互验证系统"""
    var _validation_cache: Dictionary = {}
    var _last_validation_time: Dictionary = {}
    var _cache_timeout: float = 3.0

    func _init(cache_timeout: float = 3.0):
        _cache_timeout = cache_timeout

    func validate_building_interaction(target: Node, interaction_type: String) -> bool:
        """验证建筑交互 - 带缓存机制"""
        if not is_instance_valid(target):
            return false

        var target_id = target.get_instance_id()
        var cache_key = str(target_id) + "_" + interaction_type
        var current_time = Time.get_unix_time_from_system()

        # 检查缓存
        if _validation_cache.has(cache_key):
            var last_time = _last_validation_time.get(cache_key, 0.0)
            if current_time - last_time < _cache_timeout:
                return _validation_cache[cache_key]

        # 执行验证
        var is_valid = _perform_validation(target, interaction_type)

        # 更新缓存
        _validation_cache[cache_key] = is_valid
        _last_validation_time[cache_key] = current_time

        return is_valid

    func _perform_validation(target: Node, interaction_type: String) -> bool:
        """执行实际的验证逻辑"""
        # 基础验证
        if not target.is_inside_tree():
            return false

        # 根据交互类型进行特定验证
        match interaction_type:
            "store":
                return _validate_storage(target)
            "harvest":
                return _validate_harvest(target)
            "plant":
                return _validate_plant(target)
            "chop":
                return _validate_chop(target)
            "mine":
                return _validate_mine(target)
            "cook":
                return _validate_cook(target)
            "fish":
                return _validate_fish(target)
            _:
                return true

    func _validate_storage(target: Node) -> bool:
        """验证存储建筑"""
        return target.is_in_group("storages") and target.has_method("can_store_resource")

    func _validate_harvest(target: Node) -> bool:
        """验证收获目标"""
        return (target.is_in_group("farmlands") or target.is_in_group("crops")) and target.has_method("is_harvestable")

    func _validate_plant(target: Node) -> bool:
        """验证种植目标"""
        return target.is_in_group("farmlands") and target.has_method("can_plant")

    func _validate_chop(target: Node) -> bool:
        """验证砍伐目标"""
        return target.is_in_group("trees") and target.has_method("can_chop")

    func _validate_mine(target: Node) -> bool:
        """验证挖矿目标"""
        return (target.is_in_group("ores") or target.is_in_group("mining_spots")) and target.has_method("can_mine")

    func _validate_cook(target: Node) -> bool:
        """验证烹饪目标"""
        return target.is_in_group("kitchens") and target.has_method("can_cook")

    func _validate_fish(target: Node) -> bool:
        """验证钓鱼目标"""
        return target.is_in_group("fishing_spots") and target.has_method("can_fish")

    func clear_cache() -> void:
        """清理验证缓存"""
        _validation_cache.clear()
        _last_validation_time.clear()

    func cleanup() -> void:
        """清理验证器"""
        clear_cache()

## 🆕 7. 复杂工作流状态追踪 - 统一实现 ##

class WorkflowTracker:
    """复杂工作流状态追踪系统"""
    var _workflow_sessions: Dictionary = {}
    var _current_session_id: String = ""

    func start_workflow(target: Node, workflow_type: String, worker: Node) -> String:
        """开始工作流程追踪"""
        var session_id = _generate_session_id()
        _current_session_id = session_id

        _workflow_sessions[session_id] = {
            "target": target,
            "type": workflow_type,
            "worker": worker,
            "start_time": Time.get_unix_time_from_system(),
            "current_stage": "started",
            "stages": [],
            "status": "active"
        }

        return session_id

    func update_stage(session_id: String, stage: String, data: Dictionary = {}) -> void:
        """更新工作流程阶段"""
        if not _workflow_sessions.has(session_id):
            return

        var session = _workflow_sessions[session_id]
        session.current_stage = stage
        session.stages.append({
            "stage": stage,
            "time": Time.get_unix_time_from_system(),
            "data": data
        })

    func complete_workflow(session_id: String, success: bool = true) -> void:
        """完成工作流程"""
        if not _workflow_sessions.has(session_id):
            return

        var session = _workflow_sessions[session_id]
        session.status = "completed" if success else "failed"
        session.end_time = Time.get_unix_time_from_system()
        session.duration = session.end_time - session.start_time

        # 清理会话（可选择保留用于统计）
        _workflow_sessions.erase(session_id)
        if _current_session_id == session_id:
            _current_session_id = ""

    func get_current_workflow() -> Dictionary:
        """获取当前工作流程信息"""
        if _current_session_id.is_empty() or not _workflow_sessions.has(_current_session_id):
            return {}
        return _workflow_sessions[_current_session_id]

    func is_workflow_active(session_id: String) -> bool:
        """检查工作流程是否活跃"""
        return _workflow_sessions.has(session_id) and _workflow_sessions[session_id].status == "active"

    func _generate_session_id() -> String:
        """生成会话ID"""
        return "workflow_" + str(Time.get_unix_time_from_system()) + "_" + str(randi() % 1000)

    func cleanup() -> void:
        """清理工作流追踪器"""
        _workflow_sessions.clear()
        _current_session_id = ""

## 🆕 8. 异步处理规范 - 统一实现 ##

class AsyncProcessor:
    """异步处理规范系统"""
    var _pending_tasks: Array = []
    var _processing_queue: Array = []
    var _max_queue_size: int = 50

    func _init(max_queue_size: int = 50):
        _max_queue_size = max_queue_size

    func queue_async_task(task_data: Dictionary, delay: float = 0.0) -> void:
        """队列异步任务"""
        var task = {
            "data": task_data,
            "delay": delay,
            "queue_time": Time.get_unix_time_from_system(),
            "execute_time": Time.get_unix_time_from_system() + delay,
            "status": "queued"
        }

        _pending_tasks.append(task)

        # 保持队列大小在限制内
        if _pending_tasks.size() > _max_queue_size:
            _pending_tasks = _pending_tasks.slice(-_max_queue_size)

    func process_async_tasks(processor_node: Node) -> void:
        """处理异步任务"""
        var current_time = Time.get_unix_time_from_system()
        var tasks_to_remove = []

        for i in range(_pending_tasks.size()):
            var task = _pending_tasks[i]
            if current_time >= task.execute_time:
                # 执行任务
                _execute_async_task(task, processor_node)
                tasks_to_remove.append(i)

        # 从后往前删除已执行的任务
        for i in range(tasks_to_remove.size() - 1, -1, -1):
            _pending_tasks.remove_at(tasks_to_remove[i])

    func _execute_async_task(task: Dictionary, processor_node: Node) -> void:
        """执行异步任务"""
        var task_data = task.data
        var method_name = task_data.get("method", "")
        var params = task_data.get("params", [])

        if not method_name.is_empty() and processor_node.has_method(method_name):
            if params.is_empty():
                processor_node.call(method_name)
            else:
                processor_node.callv(method_name, params)

    func get_queue_status() -> Dictionary:
        """获取队列状态"""
        return {
            "pending_count": _pending_tasks.size(),
            "processing_count": _processing_queue.size(),
            "max_queue_size": _max_queue_size
        }

    func cleanup() -> void:
        """清理异步处理器"""
        _pending_tasks.clear()
        _processing_queue.clear()

## 🆕 9. Y轴排序方案 - 统一实现 ##

static func apply_y_sorting(worker: Node, carrying_item: Node = null) -> void:
    """应用Y轴排序方案 - 统一配置"""
    if not is_instance_valid(worker):
        return

    # 设置工人的Y轴排序
    worker.z_index = 0
    if worker.has_method("set_y_sort_enabled"):
        worker.set_y_sort_enabled(true)

    # 设置携带物品的Y轴排序
    if is_instance_valid(carrying_item):
        carrying_item.z_index = 0
        carrying_item.position.y = -20  # 统一偏移量
        if carrying_item.has_method("set_y_sort_enabled"):
            carrying_item.set_y_sort_enabled(true)

## 🆕 10. 交互位置0容差系统 - 统一实现 ##

static func start_precise_interaction(target: Node, interaction_type: String, params: Dictionary, interaction_manager: Node) -> bool:
    """启动精确交互 - 0容差系统"""
    if not is_instance_valid(target) or not is_instance_valid(interaction_manager):
        return false

    # 完全删除自定义距离检查，直接调用基类精确交互
    # BaseInteractionManager会自动处理精确定位要求和标准距离检查
    if interaction_manager.has_method("start_interaction"):
        return interaction_manager.start_interaction(target, interaction_type, params)

    return false

## 🆕 11. 状态同步机制 - 统一实现 ##

class StateSynchronizer:
    """状态同步机制 - 五层同步架构"""
    var _sync_targets: Array = []

    func register_sync_target(target: Node) -> void:
        """注册同步目标"""
        if is_instance_valid(target) and target not in _sync_targets:
            _sync_targets.append(target)

    func unregister_sync_target(target: Node) -> void:
        """注销同步目标"""
        if target in _sync_targets:
            _sync_targets.erase(target)

    func sync_state(new_state_enum, state_data: Dictionary = {}, caller: Node = null) -> void:
        """同步状态到所有目标（除了调用者）"""
        for target in _sync_targets:
            if not is_instance_valid(target):
                continue

            # 避免同步到调用者自己
            if is_instance_valid(caller) and target == caller:
                continue

            # 调用目标的状态更新方法
            if target.has_method("update_unified_state"):
                target.update_unified_state(new_state_enum)
            elif target.has_method("sync_state"):
                target.sync_state(new_state_enum, state_data)

    func cleanup() -> void:
        """清理同步器"""
        _sync_targets.clear()

## 🆕 工厂方法 - 创建技术实例 ##

static func create_ai_frequency_controller(max_freq: float = 1.0, min_freq: float = 3.0) -> AIFrequencyController:
    """创建AI频率控制器"""
    return AIFrequencyController.new(max_freq, min_freq)

static func create_interaction_recorder(record_limit: int = 50) -> InteractionRecorder:
    """创建交互记录器"""
    return InteractionRecorder.new(record_limit)

static func create_building_validator(cache_timeout: float = 3.0) -> BuildingValidator:
    """创建建筑验证器"""
    return BuildingValidator.new(cache_timeout)

static func create_workflow_tracker() -> WorkflowTracker:
    """创建工作流追踪器"""
    return WorkflowTracker.new()

static func create_async_processor(max_queue_size: int = 50) -> AsyncProcessor:
    """创建异步处理器"""
    return AsyncProcessor.new(max_queue_size)

static func create_state_synchronizer() -> StateSynchronizer:
    """创建状态同步器"""
    return StateSynchronizer.new()
