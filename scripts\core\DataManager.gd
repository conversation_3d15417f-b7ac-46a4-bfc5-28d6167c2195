# DataManager.gd
# 数据管理器 - 负责加载和管理游戏配置数据
class_name DataManager
extends Node

## 信号定义区 ##
signal crop_unlocked_state_changed(crop_type_id: String, is_unlocked: bool)

## 常量定义区 ##
const CONFIG_PATHS = {
    "crops": "res://config/crops.json",
    "forestry": "res://config/forestry.json",
    "fishing": "res://config/fishing.json",
    "buildings": "res://config/buildings.json",
    "equipment": "res://config/equipment.json",
    "cooking": "res://config/cooking.json",
    "animals": "res://config/animals.json"
}

## 缓存与状态变量 ##
var is_initialized: bool = false
var _configs: Dictionary = {
    "crops": {},
    "forestry": {},
    "fishing": {},
    "buildings": {},
    "equipment": {},
    "cooking": {},
    "animals": {}
}
var _unlocked_crops_cache: Dictionary = {}

## 管理器引用 ##
var _resource_manager = null
var _ore_data_manager = null

## 生命周期函数区 ##
func _ready() -> void:
    initialize()

func initialize() -> void:
    if is_initialized:
        return
        
    # 加载所有配置数据
    _load_all_configs()
    
    # 获取资源管理器引用
    _get_resource_manager_reference()
    
    # 标记初始化完成
    is_initialized = true
    
    # 进行初始化后处理
    call_deferred("_post_initialization")

## 配置加载方法 ##
func _load_all_configs() -> void:
    var success_count = 0
    var total_count = CONFIG_PATHS.size()
    
    for config_type in CONFIG_PATHS:
        if _load_config_file(config_type, CONFIG_PATHS[config_type]):
            success_count += 1
    
    print("[DataManager] 配置加载完成: %d/%d 成功" % [success_count, total_count])

func _load_config_file(config_type: String, file_path: String) -> bool:
    if not FileAccess.file_exists(file_path):
        push_error("[DataManager] %s配置文件不存在: %s" % [config_type, file_path])
        return false
    
    var file = FileAccess.open(file_path, FileAccess.READ)
    if not file:
        push_error("[DataManager] 无法打开%s配置文件" % config_type)
        return false
    
    var json_text = file.get_as_text()
    file.close()
    
    var json = JSON.new()
    var error = json.parse(json_text)
    if error != OK:
        push_error("[DataManager] 解析%s配置失败: %s" % [config_type, json.get_error_message()])
        return false
    
    var data = json.get_data()
    if not data is Dictionary:
        push_error("[DataManager] %s配置格式错误" % config_type)
        return false
    
    # 特殊处理作物配置验证
    if config_type == "crops" and not data.has("crop_types"):
        push_error("[DataManager] 作物配置缺少 'crop_types' 字段")
        return false
    
    _configs[config_type] = data.duplicate(true)
    return true

func _get_resource_manager_reference() -> void:
    var game_manager = get_node_or_null("/root/_GameManager")
    if is_instance_valid(game_manager) and game_manager.has_method("get_resource_manager"):
        _resource_manager = game_manager.get_resource_manager()
        if is_instance_valid(_resource_manager):
            call_deferred("_connect_resource_manager_signals")
    
    # 🆕 同时获取OreDataManager引用
    if is_instance_valid(game_manager) and game_manager.has_method("get_ore_data_manager"):
        _ore_data_manager = game_manager.get_ore_data_manager()

## 作物检查与管理方法 ##
func has_crop_type(crop_type_id: String) -> bool:
    var crop_types = _configs.crops.get("crop_types", {})
    return crop_types.has(crop_type_id)

func get_all_crop_type_ids() -> Array[String]:
    var crop_types = _configs.crops.get("crop_types", {})
    var result: Array[String] = []
    for key in crop_types.keys():
        result.append(str(key))
    return result

func get_all_crop_type_ids_ordered() -> Array[String]:
    var crop_types = _configs.crops.get("crop_types", {})
    var crop_list = []
    
    for crop_id in crop_types.keys():
        var crop_config = crop_types[crop_id]
        var level = crop_config.get("level", 999)
        crop_list.append({"id": str(crop_id), "level": level})
    
    crop_list.sort_custom(func(a, b): return a.level < b.level)
    
    var ordered_ids: Array[String] = []
    for item in crop_list:
        ordered_ids.append(item.id)
    
    return ordered_ids

func is_crop_unlocked(crop_type_id: String) -> bool:
    if not has_crop_type(crop_type_id):
        return false

    var config = get_crop_config(crop_type_id)
    if config.is_empty():
        return false

    # 总是重新计算解锁状态，确保实时性
    var actual_unlocked = _check_crop_unlock_condition(crop_type_id, config)
    
    # 更新缓存
    _unlocked_crops_cache[crop_type_id] = actual_unlocked
    
    return actual_unlocked

func _check_crop_unlock_condition(crop_type_id: String, config: Dictionary) -> bool:
    if config.is_empty() or not config.has_all(["is_unlocked_by_default", "unlock_condition"]):
        return false

    if config.get("is_unlocked_by_default", false):
        return true

    var unlock_condition = config.get("unlock_condition", {})
    if not unlock_condition is Dictionary or not unlock_condition.has_all(["requires_crop", "amount"]):
        return false
    
    var required_crop_id = unlock_condition.get("requires_crop")
    var required_amount = unlock_condition.get("amount")

    # 确保ResourceManager引用有效
    if not is_instance_valid(_resource_manager):
        _get_resource_manager_reference()
        if not is_instance_valid(_resource_manager):
            return false

    if required_amount <= 0:
        return false

    # ✅ 使用IResourceSystem统一接口
    var current_amount = IResourceSystem.get_item_amount(required_crop_id)
    
    # 🆕 移除枚举系统回退检查
    
    return current_amount >= required_amount

func get_plantable_crop_ids() -> Array[String]:
    var all_ids = get_all_crop_type_ids()
    var plantable_ids: Array[String] = []
    
    for crop_id in all_ids:
        if is_crop_unlocked(crop_id):
            plantable_ids.append(crop_id)
    
    return plantable_ids

func is_crop_plantable(crop_type_id: String) -> bool:
    return is_crop_unlocked(crop_type_id)

func get_crop_config(crop_type_id: String) -> Dictionary:
    if not has_crop_type(crop_type_id):
        return {}
    var crop_types = _configs.crops.get("crop_types", {})
    return crop_types.get(crop_type_id, {}).duplicate(true)

func get_all_crop_types() -> Dictionary:
    """获取所有作物类型配置"""
    return _configs.crops.get("crop_types", {}).duplicate(true)

## 作物视觉资源方法 ##
func get_crop_icon_region(crop_type_id: String) -> Rect2:
    var config = get_crop_config(crop_type_id)
    if config.is_empty():
        return Rect2()

    var visuals = config.get("visuals", {})
    if not visuals is Dictionary:
        return Rect2()
        
    var icon_source = visuals.get("icon_source", {})
    if not icon_source is Dictionary or icon_source.get("type") != "spritesheet":
        return Rect2()

    var region_key = icon_source.get("path_or_region_key", "")
    if region_key.is_empty():
        return Rect2()
    
    var regions = visuals.get("regions", {})
    if not regions.has(region_key):
        return Rect2()
        
    var region_data = regions[region_key]
    if not region_data is Dictionary or not region_data.has_all(["x", "y", "w", "h"]):
        return Rect2()
    
    return Rect2(region_data["x"], region_data["y"], region_data["w"], region_data["h"])

## 初始化后的处理 ##
func _post_initialization() -> void:
    ensure_crop_textures_loaded()
    _initialize_all_unlock_caches_and_emit_initial_signals()

func _initialize_all_unlock_caches_and_emit_initial_signals() -> void:
    var all_crop_ids = get_all_crop_type_ids()
    
    for crop_id in all_crop_ids:
        var config = get_crop_config(crop_id)
        if config.is_empty(): 
            continue

        var is_now_unlocked = _check_crop_unlock_condition(crop_id, config)
        _unlocked_crops_cache[crop_id] = is_now_unlocked
        crop_unlocked_state_changed.emit(crop_id, is_now_unlocked)

func ensure_crop_textures_loaded() -> bool:
    var all_loaded = true
    var crop_types = get_all_crop_type_ids()
    
    for crop_id in crop_types:
        var config = get_crop_config(crop_id)
        if config.is_empty():
            all_loaded = false
            continue
            
        var spritesheet_path = config.get("visuals", {}).get("spritesheet_path", "")
        if spritesheet_path.is_empty() or not ResourceLoader.exists(spritesheet_path):
            all_loaded = false
            continue
            
        var texture = load(spritesheet_path)
        if not is_instance_valid(texture) or not texture is Texture2D:
            all_loaded = false
    
    return all_loaded

func _connect_resource_manager_signals():
    # 🎯 统一化重构：连接统一的资源变化信号
    if not is_instance_valid(_resource_manager) or not _resource_manager.has_signal("item_changed"):
        return

    var callable = Callable(self, "_on_item_changed_for_unlock_check")
    if not _resource_manager.item_changed.is_connected(callable):
        _resource_manager.item_changed.connect(callable)

func _on_item_changed_for_unlock_check(item_id: String, amount: int, total: int):
    call_deferred("_trigger_crop_unlock_check")

func _trigger_crop_unlock_check():
    var all_crop_ids = get_all_crop_type_ids()
    
    for crop_id in all_crop_ids:
        var config = get_crop_config(crop_id)
        if config.is_empty():
            continue
            
        var actual_status = _check_crop_unlock_condition(crop_id, config)
        var current_status = _unlocked_crops_cache.get(crop_id, false)
        
        if actual_status != current_status:
            _unlocked_crops_cache[crop_id] = actual_status
            crop_unlocked_state_changed.emit(crop_id, actual_status)

## 辅助方法 ##
# 🆕 移除冗余的枚举获取方法

## 图标数据获取方法 ##
func _extract_icon_data_from_config(config: Dictionary) -> Dictionary:
    var visuals = config.get("visuals", {})
    if visuals.is_empty():
        return {}
    
    var spritesheet_path = visuals.get("spritesheet_path", "")
    var icon_source = visuals.get("icon_source", {})
    var regions = visuals.get("regions", {})
    
    if icon_source.get("type") == "spritesheet":
        var region_key = icon_source.get("path_or_region_key", "")
        if regions.has(region_key):
            var region_data = regions[region_key]
            if region_data.has_all(["x", "y", "w", "h"]):
                var result = {
                    "texture_path": spritesheet_path,
                    "region": Rect2(region_data["x"], region_data["y"], region_data["w"], region_data["h"])
                }
                if region_data.has("margin"):
                    result["margin"] = region_data["margin"]
                return result
    
    return {}

func get_crop_icon_region_data(crop_id: String) -> Dictionary:
    var config = get_crop_config(crop_id)
    return _extract_icon_data_from_config(config) if not config.is_empty() else {}

## 林业配置方法 ##
func _get_forestry_config_section(section_name: String) -> Dictionary:
    if _configs.forestry.is_empty():
        return {}
    return _configs.forestry.get(section_name, {}).duplicate(true)

func get_wood_source_config() -> Dictionary:
    return _get_forestry_config_section("wood_source_settings")

func get_woodcutter_settings() -> Dictionary:
    return _get_forestry_config_section("woodcutter_settings")

func get_general_forestry_settings() -> Dictionary:
    return _get_forestry_config_section("general_forestry_settings")

func get_tree_growth_config() -> Dictionary:
    return _get_forestry_config_section("tree_growth_settings")

func get_tree_type_config(tree_type_id: String) -> Dictionary:
    var tree_types = _configs.forestry.get("tree_types", [])
    for tree_type in tree_types:
        if tree_type is Dictionary and tree_type.get("id") == tree_type_id:
            return tree_type.duplicate(true)
    return {}

func get_all_tree_type_configs() -> Dictionary:
    var tree_types = _configs.forestry.get("tree_types", [])
    var result = {}
    
    for tree_type in tree_types:
        if tree_type is Dictionary and tree_type.has("id"):
            result[tree_type.get("id")] = tree_type.duplicate(true)
    
    return result

## 渔业配置方法 ##
func get_fishing_spot_config() -> Dictionary:
    if not _configs.fishing.is_empty() and _configs.fishing.has("fishing_spot"):
        return _configs.fishing.get("fishing_spot", {}).duplicate(true)
    
    return {
        "max_fishers": 2,
        "fish_respawn_time": 5.0,
        "lock_timeout": 15.0
    }

func get_fisherman_config() -> Dictionary:
    if not _configs.fishing.is_empty() and _configs.fishing.has("fisherman"):
        return _configs.fishing.get("fisherman", {}).duplicate(true)
    
    return {
        "move_speed": 100.0,
        "interaction_distance": 30.0,
        "search_radius": 50.0,
        "fishing_durations": {
            "casting": 1.0, "fishing": 8.0, "reeling": 1.5,
            "catch": 1.0, "collecting": 1.0, "storing": 1.0
        }
    }

func get_fishing_config() -> Dictionary:
    return _configs.fishing.duplicate(true)

func get_fishing_item_icon_data(item_id: String) -> Dictionary:
    if _configs.fishing.is_empty():
        return {}
    
    # 检查各类渔业物品
    var sections = ["fishing_rods", "baits"]
    for section in sections:
        var items = _configs.fishing.get(section, {})
        if items.has(item_id):
            return _extract_icon_data_from_config(items[item_id])
    
    # 检查捕获物品
    var catch_items = _configs.fishing.get("catch_items", {})
    var catch_sections = ["junk", "freshwater_fish", "saltwater_fish"]
    for section in catch_sections:
        var items = catch_items.get(section, {})
        if items.has(item_id):
            return _extract_icon_data_from_config(items[item_id])
    
    return {}

func get_fishing_item_display_name(item_id: String) -> String:
    if _configs.fishing.is_empty():
        return ""
        
    # 检查各类渔业物品
    var sections = ["fishing_rods", "baits"]
    for section in sections:
        var items = _configs.fishing.get(section, {})
        if items.has(item_id):
            return items[item_id].get("display_name", "")
            
    # 检查捕获物品
    var catch_items = _configs.fishing.get("catch_items", {})
    var catch_sections = ["junk", "freshwater_fish", "saltwater_fish"]
    for section in catch_sections:
        var items = catch_items.get(section, {})
        if items.has(item_id):
            return items[item_id].get("display_name", "")
            
    return ""

func get_resource_display_name(resource_id: String) -> String:
    if not _configs.crops.is_empty() and _configs.crops.has("resource_types"):
        var resource_types = _configs.crops.get("resource_types", {})
        if resource_types.has(resource_id):
            return resource_types[resource_id].get("display_name", "")
    return ""

func get_building_display_name(building_id: String) -> String:
    if not _configs.buildings.is_empty() and _configs.buildings.has("building_types"):
        var building_types = _configs.buildings.get("building_types", {})
        if building_types.has(building_id):
            # 🎯 修复：建筑配置使用"name"字段，不是"display_name"
            return building_types[building_id].get("name", "")
    return ""

func get_equipment_item_icon_data(item_id: String) -> Dictionary:
    if not _configs.equipment.is_empty() and _configs.equipment.has("equipment_items"):
        var equipment_items = _configs.equipment.get("equipment_items", {})
        if equipment_items.has(item_id):
            var item_config = equipment_items[item_id]
            # 优化：直接从equipment.json的icon字段获取图标数据
            if item_config.has("icon"):
                var icon_config = item_config["icon"]
                if icon_config.has("texture") and icon_config.has("region"):
                    var region_data = icon_config["region"]
                    if region_data.has_all(["x", "y", "w", "h"]):
                        var result = {
                            "texture_path": icon_config["texture"],
                            "region": Rect2(region_data["x"], region_data["y"], region_data["w"], region_data["h"])
                        }
                        if region_data.has("margin"):
                            result["margin"] = region_data["margin"]
                        return result
            # 回退到通用方法
            return _extract_icon_data_from_config(item_config)
    return {}

func get_equipment_item_display_name(item_id: String) -> String:
    """获取装备物品的显示名称"""
    if not _configs.equipment.is_empty() and _configs.equipment.has("equipment_items"):
        var equipment_items = _configs.equipment.get("equipment_items", {})
        if equipment_items.has(item_id):
            return equipment_items[item_id].get("display_name", "")
    return ""

func get_equipment_config() -> Dictionary:
    """获取装备配置 - 配置驱动架构支持"""
    return _configs.equipment.duplicate(true)

func get_animal_product_display_name(item_id: String) -> String:
    """获取动物产品的显示名称"""
    # 动物产品应该从专门的配置文件获取，这里暂时保留硬编码
    # TODO: 将来添加动物产品配置文件时移除硬编码
    var animal_product_names = {
        "egg": "鸡蛋",
        "duck_egg": "鸭蛋",
        "milk": "牛奶",
        "wool": "羊毛",
        "meat": "肉类"
    }
    return animal_product_names.get(item_id, "")

func get_mining_item_display_name(item_id: String) -> String:
    """获取矿产物品的显示名称"""
    if not is_instance_valid(_ore_data_manager) or not _ore_data_manager.is_loaded:
        return ""

    # 检查是否是矿物原材料
    var ore_data = _ore_data_manager.get_ore_type_data(item_id)
    if not ore_data.is_empty():
        return ore_data.get("display_name", "")

    # 检查是否是成品（直接从products配置获取正确的显示名称）
    var product_data = _ore_data_manager.get_product_data(item_id)
    if not product_data.is_empty():
        return product_data.get("display_name", "")

    # 如果都找不到，返回空字符串
    return ""

func get_animal_product_icon_data(item_id: String) -> Dictionary:
    """获取动物产品的图标数据"""
    if not _configs.animals.is_empty():
        var animal_products = _configs.animals.get("animal_products", {})
        if animal_products.has(item_id):
            return _extract_icon_data_from_config(animal_products[item_id])
    return {}

func get_mining_item_icon_data(item_id: String) -> Dictionary:
    """获取矿产物品的图标数据"""
    if not is_instance_valid(_ore_data_manager) or not _ore_data_manager.is_loaded:
        return {}
    
    # 🎯 精确识别：只处理真正的矿产物品
    # 检查是否是矿物原材料 - 使用IResourceSystem统一接口
    var resource_manager = IResourceSystem.get_resource_manager()
    if is_instance_valid(resource_manager):
        var item_type = resource_manager.get_item_type(item_id)
        if item_type != ResourceManager.ItemType.MINING:
            # 如果不是MINING类型，不处理（避免与基础资源gold冲突）
            return {}
    
    # 检查是否是矿物原材料
    var ore_data = _ore_data_manager.get_ore_type_data(item_id)
    if not ore_data.is_empty():
        return _extract_new_ore_icon_data(ore_data, false)

    # 检查是否是成品（直接从products配置中查找）
    var product_data = _ore_data_manager.get_product_data(item_id)
    if not product_data.is_empty():
        return _extract_new_ore_icon_data(product_data, true)
    
    # 如果OreDataManager无法提供数据，返回空字典
    return {}

func get_cooking_item_icon_data(item_id: String) -> Dictionary:
    """获取烹饪物品的图标数据"""
    if _configs.cooking.is_empty():
        return {}

    var recipes = _configs.cooking.get("recipes", {})
    if recipes.has(item_id):
        var recipe_data = recipes[item_id]
        var visuals = recipe_data.get("visuals", {})

        if visuals.has("spritesheet_path") and visuals.has("regions"):
            var texture_path = visuals.spritesheet_path
            var regions = visuals.regions
            var icon_source = visuals.get("icon_source", {})
            var region_key = icon_source.get("path_or_region_key", item_id + "_icon")

            if regions.has(region_key):
                var region_data = regions[region_key]
                return {
                    "texture_path": texture_path,
                    "region": Rect2(
                        region_data.get("x", 0),
                        region_data.get("y", 0),
                        region_data.get("w", 16),
                        region_data.get("h", 16)
                    )
                }

    return {}

func get_cooking_item_display_name(item_id: String) -> String:
    """获取烹饪物品的显示名称"""
    if _configs.cooking.is_empty():
        return ""
    
    var recipes = _configs.cooking.get("recipes", {})
    if recipes.has(item_id):
        return recipes[item_id].get("name_cn", "")
    
    return ""

func get_item_display_name(item_id: String) -> String:
    """通用物品显示名称获取方法"""
    # 优化：按优先级顺序检查不同类型的物品
    var name_methods = [
        "get_cooking_item_display_name",    # 🎯 新增：优先检查烹饪物品
        "get_equipment_item_display_name",  # 优先检查装备物品
        "get_mining_item_display_name",     # 🎯 新增：矿产物品
        "get_animal_product_display_name",  # 新增：动物产品
        "get_building_display_name", 
        "get_fishing_item_display_name",
        "get_resource_display_name"
    ]
    
    # 检查作物配置
    var crop_config = get_crop_config(item_id)
    if not crop_config.is_empty():
        return crop_config.get("display_name", "")
    
    # 检查其他类型的物品
    for method_name in name_methods:
        if has_method(method_name):
            var result = call(method_name, item_id)
            if result is String and not result.is_empty():
                return result
                
    return ""

func get_item_config(item_id: String) -> Dictionary:
    """通用物品配置获取方法"""
    if not is_initialized:
        return {}
    
    # 优化：按优先级顺序检查不同的配置源
    var config_sources = [
        _configs.equipment.get("equipment_items", {}),  # 优先检查装备
        _configs.buildings.get("building_types", {}), 
        _configs.fishing.get("fishing_rods", {}), 
        _configs.fishing.get("baits", {})
    ]

    for source in config_sources:
        if source.has(item_id):
            return source[item_id].duplicate(true)
            
    # 检查更深层次的配置
    var fishing_catch_items = _configs.fishing.get("catch_items", {})
    for category in fishing_catch_items.values():
        if category.has(item_id):
            return category[item_id].duplicate(true)
            
    var crop_types = _configs.crops.get("crop_types", {})
    if crop_types.has(item_id):
        return crop_types[item_id].duplicate(true)
        
    # 检查crops中的crop_resource_types（新结构）
    var crop_resource_types = _configs.crops.get("crop_resource_types", {})
    if crop_resource_types.has(item_id):
        return crop_resource_types[item_id].duplicate(true)

    # 检查animals中的animal_products（新结构）
    var animal_products = _configs.animals.get("animal_products", {})
    if animal_products.has(item_id):
        return animal_products[item_id].duplicate(true)

    # 检查林业资源
    if _configs.has("forestry") and _configs.forestry != null and not _configs.forestry.is_empty():
        var forestry_resources = _configs.forestry.get("forestry_resources", {})
        if forestry_resources.has(item_id):
            return forestry_resources[item_id].duplicate(true)

    return {}

func get_all_craftable_items() -> Dictionary:
    """获取所有可制作物品的统一接口"""
    var all_items = {
        "building": {},
        "tool": {},
        "consumable": {}
    }
    
    # 获取建筑
    if _configs.buildings.has("building_types"):
        all_items.building = _configs.buildings.get("building_types", {})
        
    # 🆕 从equipment.json获取渔具（鱼竿和鱼饵）
    var equipment_items = _configs.equipment.get("equipment_items", {})
    for item_id in equipment_items:
        var item = equipment_items[item_id]
        var compatible_chars = item.get("compatible_characters", [])

        if compatible_chars.has("fisherman"):
            if item.get("category") == "main_hand" and item.has("crafting_cost"):
                # 鱼竿
                var item_data = item.duplicate()
                item_data["cost"] = item_data.get("crafting_cost")
                item_data.erase("crafting_cost")
                all_items.tool[item_id] = item_data
            elif item.get("category") == "off_hand" and item.has("crafting_cost"):
                # 鱼饵
                var item_data = item.duplicate()
                item_data["cost"] = item_data.get("crafting_cost")
                item_data.erase("crafting_cost")
                all_items.consumable[item_id] = item_data
                
    return all_items

## 建筑配置方法 ##
func get_building_config(building_type: String) -> Dictionary:
    if not _configs.buildings.is_empty() and _configs.buildings.has("building_types"):
        var building_types = _configs.buildings.get("building_types", {})
        if building_types.has(building_type):
            return building_types[building_type].duplicate(true)
    
    var defaults = {
        "fishingspot": {"name": "钓鱼点", "max_fishers": 2},
        "house": {"name": "住宅", "cost": {"wood": 20, "stone": 10}, "build_time": 15},
        "farm": {"name": "农场", "cost": {"wood": 15, "stone": 5}, "build_time": 10},
        "storage": {"name": "仓库", "cost": {"wood": 30, "stone": 20}, "build_time": 20},
        "farmland": {"name": "农田", "cost": {"wood": 5, "stone": 3}, "build_time": 8},
        "well": {"name": "水井", "cost": {"stone": 15, "wood": 5}, "build_time": 12},
        "kitchen": {"name": "炉灶", "cost": {"wood": 15, "stone": 10}, "build_time": 15}
    }
    
    return defaults.get(building_type, {})

func get_building_icon_region_data(building_id: String) -> Dictionary:
    if not _configs.buildings.is_empty() and _configs.buildings.has("building_types"):
        var building_types = _configs.buildings.get("building_types", {})
        if building_types.has(building_id):
            return _extract_icon_data_from_config(building_types[building_id])
    return {}

func get_resource_icon_region_data(resource_id: String) -> Dictionary:
    # 🆕 优先检查作物物品（_item后缀）- 从作物类型的regions中获取
    if resource_id.ends_with("_item"):
        var crop_id = resource_id.replace("_item", "")
        var crop_types = _configs.crops.get("crop_types", {})
        if crop_types.has(crop_id):
            var crop_config = crop_types[crop_id]
            var visuals = crop_config.get("visuals", {})
            var regions = visuals.get("regions", {})
            var icon_key = "icon_" + resource_id  # icon_wheat_item
            if regions.has(icon_key):
                var region_data = regions[icon_key]
                var icon_data = {
                    "texture_path": visuals.get("spritesheet_path", ""),
                    "region": Rect2(
                        region_data.get("x", 0),
                        region_data.get("y", 0),
                        region_data.get("w", 16),
                        region_data.get("h", 16)
                    )
                }

                return icon_data

    # 检查crop_resource_types（独立的作物物品配置）
    var crop_resource_types = _configs.crops.get("crop_resource_types", {})
    if crop_resource_types.has(resource_id):
        return _extract_icon_data_from_config(crop_resource_types[resource_id])

    # 检查动物产品
    var animal_products = _configs.animals.get("animal_products", {})
    if animal_products.has(resource_id):
        return _extract_icon_data_from_config(animal_products[resource_id])

    # 最后检查作物类型（无后缀）- 用于农业系统
    var crop_data = get_crop_icon_region_data(resource_id)
    if not crop_data.is_empty():
        return crop_data

    # 检查矿石配置
    if _configs.has("Ores") and _configs.Ores != null and not _configs.Ores.is_empty():
        var ore_types = _configs.Ores.get("ore_types", {})
        if ore_types.has(resource_id):
            return _extract_new_ore_icon_data(ore_types[resource_id], false)

    # 检查林业配置（如果存在）
    if _configs.has("forestry") and _configs.forestry != null and not _configs.forestry.is_empty():
        var forestry_resources = _configs.forestry.get("forestry_resources", {})
        if forestry_resources.has(resource_id):
            return _extract_icon_data_from_config(forestry_resources[resource_id])

    return {}

func get_item_icon_region_data(item_id: String) -> Dictionary:
    """通用物品图标数据获取方法"""
    # 优化：按优先级顺序尝试不同的配置源
    var icon_methods = [
        "get_cooking_item_icon_data",    # 🎯 优先检查烹饪物品
        "get_equipment_item_icon_data",  # 优先检查装备物品
        "get_resource_icon_region_data", # 🎯 基础资源优先于矿产，避免gold冲突
        "get_mining_item_icon_data",     # 🎯 矿产物品（调整到基础资源之后）
        "get_animal_product_icon_data",  # 动物产品
        "get_crop_icon_region_data",
        "get_building_icon_region_data", 
        "get_fishing_item_icon_data"
    ]
    
    for method in icon_methods:
        if has_method(method):
            var icon_data = call(method, item_id)
            if not icon_data.is_empty():
                return icon_data
    
    return {}

func get_resource_config(resource_id: String) -> Dictionary:
    """获取资源配置"""
    # 检查crops中的crop_resource_types
    if not _configs.crops.is_empty():
        var crop_resource_types = _configs.crops.get("crop_resource_types", {})
        if crop_resource_types.has(resource_id):
            return crop_resource_types[resource_id].duplicate(true)

    # 检查animals中的animal_products
    if not _configs.animals.is_empty():
        var animal_products = _configs.animals.get("animal_products", {})
        if animal_products.has(resource_id):
            return animal_products[resource_id].duplicate(true)

    # 检查Ores配置
    if _configs.has("Ores") and _configs.Ores != null and not _configs.Ores.is_empty():
        var ore_types = _configs.Ores.get("ore_types", {})
        if ore_types.has(resource_id):
            return ore_types[resource_id].duplicate(true)

    # 检查林业配置
    if _configs.has("forestry") and _configs.forestry != null and not _configs.forestry.is_empty():
        var forestry_resources = _configs.forestry.get("forestry_resources", {})
        if forestry_resources.has(resource_id):
            return forestry_resources[resource_id].duplicate(true)

    return {}

func _get_placeholder_texture() -> Texture2D:
    """获取占位符纹理，用于判断是否为占位符"""
    # 返回null作为占位符标识，表示没有有效纹理
    return null

func load_all_configs() -> void:
    if not is_initialized:
        initialize()

func get_cooking_config() -> Dictionary:
    return _configs.cooking.duplicate(true)

## 动物配置方法 ##
func get_animals_config() -> Dictionary:
    """获取动物配置"""
    return _configs.animals.duplicate(true)

func get_animal_config(animal_id: String) -> Dictionary:
    """获取指定动物的配置"""
    var animals = _configs.animals.get("animals", {})
    return animals.get(animal_id, {}).duplicate(true)

func _extract_new_ore_icon_data(config: Dictionary, is_product: bool) -> Dictionary:
    """从新的矿物/成品配置中提取图标数据"""
    var visuals = config.get("visuals", {})
    if visuals.is_empty():
        return {}

    # 根据是否为成品选择图标字段
    var icon_field = "product_icon" if is_product else "ore_icon"
    var region_data = visuals.get(icon_field, {})

    if region_data.is_empty() or not region_data.has_all(["x", "y", "w", "h"]):
        return {}

    # 获取精灵表路径
    var spritesheet_path = ""
    if is_instance_valid(_ore_data_manager):
        spritesheet_path = _ore_data_manager.get_spritesheet_path()

    if spritesheet_path.is_empty():
        return {}

    var result = {
        "texture_path": spritesheet_path,
        "region": Rect2(region_data["x"], region_data["y"], region_data["w"], region_data["h"])
    }

    # 可选的边距信息
    if region_data.has("margin"):
        result["margin"] = region_data["margin"]

    return result

# 🧹 旧的兼容方法已清理 - 现在完全使用新的配置格式

func get_ore_config(ore_id: String) -> Dictionary:
    """获取矿物配置数据 - 兼容性方法"""
    if not is_instance_valid(_ore_data_manager) or not _ore_data_manager.is_loaded:
        return {}
    
    return _ore_data_manager.get_ore_type_data(ore_id)
