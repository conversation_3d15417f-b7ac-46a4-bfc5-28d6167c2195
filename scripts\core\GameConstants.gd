# GameConstants.gd
# 游戏常量定义 - 统一管理所有系统的配置参数
class_name GameConstants
extends RefCounted

## 核心系统常量
const GRID_SIZE_PIXELS: int = 32                        # 基础网格大小（像素）
const DOUBLE_GRID_SIZE_PIXELS: int = 32                 # 双倍网格大小
const INTERACTION_DISTANCE_TOLERANCE: float = 5.0       # 交互距离容差

## 世界配置
const WORLD_WIDTH_GRIDS: int = 60                       # 世界宽度（网格）
const WORLD_HEIGHT_GRIDS: int = 8                       # 世界高度（网格）
const GAME_WIDTH_PIXELS: int = 1920                     # 游戏分辨率宽度
const GAME_HEIGHT_PIXELS: int = 256                     # 游戏分辨率高度

## 计算常量
const WORLD_BOUNDS_GRIDS = Rect2i(0, 0, WORLD_WIDTH_GRIDS, WORLD_HEIGHT_GRIDS)
const WORLD_BOUNDS_PIXELS = Rect2(0, 0, WORLD_WIDTH_GRIDS * GRID_SIZE_PIXELS, WORLD_HEIGHT_GRIDS * GRID_SIZE_PIXELS)

## 世界边界工具方法
static func is_grid_in_world_bounds(grid_pos: Vector2i) -> bool:
    """检查网格坐标是否在世界范围内"""
    return WORLD_BOUNDS_GRIDS.has_point(grid_pos)

static func is_world_pos_in_bounds(world_pos: Vector2) -> bool:
    """检查世界坐标是否在世界范围内"""
    return WORLD_BOUNDS_PIXELS.has_point(world_pos)

static func clamp_grid_to_world_bounds(grid_pos: Vector2i) -> Vector2i:
    """限制网格坐标在世界范围内"""
    return Vector2i(
        clamp(grid_pos.x, 0, WORLD_WIDTH_GRIDS - 1),
        clamp(grid_pos.y, 0, WORLD_HEIGHT_GRIDS - 1)
    )

static func clamp_world_pos_to_bounds(world_pos: Vector2) -> Vector2:
    """限制世界坐标在世界范围内"""
    return Vector2(
        clamp(world_pos.x, 0, WORLD_BOUNDS_PIXELS.size.x),
        clamp(world_pos.y, 0, WORLD_BOUNDS_PIXELS.size.y)
    )

## 农业系统配置
class AgricultureConstants:
    ## 水分管理
    const WATER_BASE_FETCH_AMOUNT: float = 5.0          # 基础取水量
    const WATER_BASE_USAGE_AMOUNT: float = 5.0          # 基础浇水量
    const WATER_FARMLAND_CAPACITY: float = 20.0         # 农田水容量
    const WATER_FARMLAND_NEEDED_THRESHOLD: float = 5.0  # 农田需水阈值
    const WATER_FARMLAND_DRY_THRESHOLD: float = 2.0     # 农田干燥阈值
    const WATER_FARMLAND_CONSUMPTION_RATE: float = 0.1  # 农田耗水率

    ## 收获系统
    const HARVEST_BASE_YIELD_MIN: int = 1               # 基础最小收获量
    const HARVEST_BASE_YIELD_MAX: int = 3               # 基础最大收获量

    ## 作业时间
    const HARVEST_BASE_TIME: float = 1.8                # 基础收获时间
    const PLANT_BASE_TIME: float = 1.5                  # 基础种植时间
    const WATER_BASE_TIME: float = 1.2                  # 基础浇水时间
    const FETCH_WATER_BASE_TIME: float = 2.0            # 基础取水时间
    const COLLECT_BASE_TIME: float = 1.5                # 基础收集时间
    const STORE_BASE_TIME: float = 1.8                  # 基础存储时间
    
    ## 计算方法
    static func calculate_effective_amount(base_amount: float, bonus_range: String) -> float:
        """计算有效数量（基础值 + 装备加成）"""
        if bonus_range.is_empty() or bonus_range == "0-0":
            return base_amount

        var parts = bonus_range.split("-")
        if parts.size() != 2:
            return base_amount

        var min_bonus = parts[0].to_int()
        var max_bonus = parts[1].to_int()
        var actual_bonus = randi_range(min_bonus, max_bonus)
        return base_amount + actual_bonus

    static func calculate_effective_water_amount(base_amount: float, water_capacity_bonus: int) -> float:
        """计算有效水量（考虑水桶容量加成）"""
        if water_capacity_bonus <= 0:
            return base_amount
        return max(base_amount, float(water_capacity_bonus))

    static func get_water_usage_for_capacity(water_capacity_bonus: int, farmland_needed: float = 0.0) -> float:
        """根据水桶容量计算浇水量"""
        var base_usage = WATER_BASE_USAGE_AMOUNT
        if water_capacity_bonus <= 0:
            return base_usage

        if farmland_needed > 0:
            return min(float(water_capacity_bonus), farmland_needed)
        return min(float(water_capacity_bonus), 30.0)  # 最大浇水量限制

    ## 农夫角色配置
    const FARMER_MAX_CARRYING_CAPACITY: int = 5            # 最大携带数量
    const FARMER_BASE_HARVEST_TIME: float = 2.0            # 基础收获时间
    const FARMER_BASE_PLANT_TIME: float = 1.5              # 基础种植时间
    const FARMER_BASE_SUCCESS_RATE: float = 0.7            # 基础成功率
    const FARMER_MAX_SUCCESS_RATE: float = 0.95            # 最大成功率
    const FARMER_NOTIFICATION_RADIUS: float = 300.0        # 农田通知半径
    const FARMER_URGENT_NOTIFICATION_RADIUS: float = 400.0 # 紧急通知半径

    ## AI决策冷却时间
    const FARMER_AI_COOLDOWN_BASE: float = 1.0              # 基础冷却时间
    const FARMER_AI_COOLDOWN_WATER: float = 0.5             # 携带水时
    const FARMER_AI_COOLDOWN_CROP: float = 2.0              # 携带作物时
    const FARMER_AI_COOLDOWN_OTHER: float = 1.5             # 携带其他物品时

## 渔业系统配置
class FishingConstants:
    ## 配置文件路径
    const FISHING_CONFIG_PATH: String = "res://config/fishing.json"

    ## 装备耐久度
    const DEFAULT_EQUIPMENT_DURABILITY: int = 100
    const DEFAULT_DURABILITY_COST_PER_CAST: int = 1

    ## 钓鱼时间配置
    const DEFAULT_CASTING_TIME: float = 1.0
    const DEFAULT_FISHING_TIME: float = 8.0
    const DEFAULT_REELING_TIME: float = 1.5
    const DEFAULT_CATCH_TIME: float = 1.0
    const DEFAULT_COLLECTING_TIME: float = 1.0
    const DEFAULT_STORING_TIME: float = 1.0

    ## 位置偏移
    const DEFAULT_INTERACTION_POINT_OFFSET: Vector2 = Vector2(0, 30)
    const STORAGE_INTERACTION_OFFSET: Vector2 = Vector2(32, 0)
    const FISHING_INTERACTION_OFFSET: Vector2 = Vector2(16, 8)

    ## 概率配置
    const FISH_TIER_COUNT: int = 3
    const DEFAULT_FISH_PROBABILITY: float = 0.4
    const DEFAULT_JUNK_PROBABILITY: float = 0.2
    const DEFAULT_NOTHING_PROBABILITY: float = 0.4

    ## 任务优先级
    const TASK_PRIORITY_STORE_FISH: int = 10
    const TASK_PRIORITY_TRANSPORT_FISH: int = 9
    const TASK_PRIORITY_COLLECT_FISH: int = 8
    const TASK_PRIORITY_START_FISHING: int = 7
    const TASK_PRIORITY_MOVE_TO_FISHING: int = 6
    const TASK_PRIORITY_FIND_FISHING_SPOT: int = 5
    const TASK_PRIORITY_FISHING: int = 4
    const TASK_PRIORITY_REST: int = 2
    const TASK_PRIORITY_RANDOM_WALK: int = 1
    const TASK_PRIORITY_IDLE: int = 0

    ## 关键状态
    const CRITICAL_STATES: Array[String] = [
        "CASTING", "FISHING", "REELING", "CATCH", "COLLECTING", "STORING"
    ]

    ## 渔夫基础配置
    const DEFAULT_MOVE_SPEED: float = 100.0
    const DEFAULT_INTERACTION_DISTANCE: float = 30.0
    const DEFAULT_SEARCH_RADIUS: float = 50.0
    const DEFAULT_POSITION_TOLERANCE: float = 2.0
    const DEFAULT_AI_DECISION_INTERVAL: float = 2.0
    const DEFAULT_STATE_CHECK_FREQUENCY: float = 0.5
    const DEFAULT_MOVEMENT_THRESHOLD: float = 5.0

    ## 钓鱼点配置
    const DEFAULT_MAX_FISHERS_PER_SPOT: int = 2
    const DEFAULT_FISH_RESPAWN_TIME: float = 3.0
    const DEFAULT_LOCK_TIMEOUT: float = 15.0

    ## 工具方法
    static func get_default_duration(phase: String) -> float:
        """获取默认持续时间"""
        match phase:
            "casting": return DEFAULT_CASTING_TIME
            "fishing": return DEFAULT_FISHING_TIME
            "reeling": return DEFAULT_REELING_TIME
            "catch": return DEFAULT_CATCH_TIME
            "collecting": return DEFAULT_COLLECTING_TIME
            "storing": return DEFAULT_STORING_TIME
            _: return 1.0

    static func get_task_priority(task_type: String) -> int:
        """获取任务优先级"""
        match task_type:
            "STORE_FISH": return TASK_PRIORITY_STORE_FISH
            "TRANSPORT_FISH": return TASK_PRIORITY_TRANSPORT_FISH
            "COLLECT_FISH": return TASK_PRIORITY_COLLECT_FISH
            "START_FISHING": return TASK_PRIORITY_START_FISHING
            "MOVE_TO_FISHING": return TASK_PRIORITY_MOVE_TO_FISHING
            "FIND_FISHING_SPOT": return TASK_PRIORITY_FIND_FISHING_SPOT
            "FISHING": return TASK_PRIORITY_FISHING
            "REST": return TASK_PRIORITY_REST
            "RANDOM_WALK": return TASK_PRIORITY_RANDOM_WALK
            "IDLE": return TASK_PRIORITY_IDLE
            _: return TASK_PRIORITY_IDLE

    static func is_critical_state(state_name: String) -> bool:
        """检查是否为关键状态"""
        return state_name in CRITICAL_STATES



## 林业系统配置
class ForestryConstants:
    ## 砍伐系统
    const CHOPPING_BASE_TIME: float = 2.0               # 基础砍伐时间
    const WOOD_BASE_YIELD_MIN: int = 1                  # 基础最小木材产量
    const WOOD_BASE_YIELD_MAX: int = 2                  # 基础最大木材产量
    const CHOPPING_BASE_DAMAGE: int = 25                # 基础砍伐伤害

    ## 作业时间
    const COLLECT_WOOD_BASE_TIME: float = 1.5           # 基础木材收集时间
    const STORE_WOOD_BASE_TIME: float = 1.8             # 基础木材存储时间

    ## 🆕 伐木工角色基础数值
    const WOODCUTTER_MAX_CARRYING_CAPACITY: int = 5     # 伐木工最大携带数量
    const WOODCUTTER_SEARCH_RADIUS: float = 200.0       # 搜索半径
    const WOODCUTTER_INTERACTION_DISTANCE: float = 30.0 # 交互距离

    ## 🆕 超时时间配置
    const TREE_WORKFLOW_TIMEOUT: float = 15.0           # 树木工作流程超时
    const ITEM_LOCK_TIMEOUT: float = 3.0                # 物品锁定超时
    const VALIDATION_CACHE_TIMEOUT: float = 3.0         # 验证缓存超时

    ## 🆕 树木基础配置
    const TREE_DEFAULT_MAX_HEALTH: int = 100            # 树木默认最大健康值
    const TREE_DEFAULT_WOOD_DROP_MIN: int = 1           # 默认最小木材掉落
    const TREE_DEFAULT_WOOD_DROP_MAX: int = 2           # 默认最大木材掉落

    ## 🆕 UI和视觉效果配置
    const CARRYING_ICON_OFFSET: Vector2 = Vector2(0, -20)        # 携带图标偏移
    const CARRYING_ICON_SCALE: Vector2 = Vector2(2, 2)           # 携带图标缩放
    const CARRYING_ICON_FLOAT_DISTANCE: float = 3.0              # 浮动距离
    const CARRYING_ICON_FLOAT_DURATION: float = 1.5              # 浮动周期
    const TREE_INTERACTION_OFFSET_X: float = 20.0                # 树木交互点X偏移
    const TREE_INTERACTION_OFFSET_Y: float = 16.0                # 树木交互点Y偏移
    const DROP_ITEM_SCALE: Vector2 = Vector2(2, 2)               # 掉落物品缩放
    const DROP_ITEM_RANDOM_OFFSET: float = 8.0                   # 掉落物品随机偏移

    ## 🆕 纹理和图像配置
    const FALLBACK_TEXTURE_SIZE: int = 16                        # 备用纹理尺寸
    const DEFAULT_ATLAS_REGION: Rect2 = Rect2(0, 0, 16, 16)     # 默认图集区域
    const WOOD_COLOR: Color = Color(0.6, 0.4, 0.2, 1.0)         # 木材颜色
    const WOOD_HIGHLIGHT_COLOR: Color = Color(0.8, 0.6, 0.4, 1.0) # 木材高光颜色
    const WOOD_SHADOW_COLOR: Color = Color(0.4, 0.2, 0.1, 1.0)   # 木材阴影颜色

    ## 🆕 默认树木类型
    const DEFAULT_TREE_TYPES: Array[String] = ["oak_tree", "pine_tree", "birch_tree"]
    
    ## 计算辅助方法
    static func calculate_effective_amount(base_amount: float, bonus_range: String) -> float:
        """计算最终有效数量：基础数值 + 装备加成 - 复用农业系统逻辑"""
        return AgricultureConstants.calculate_effective_amount(base_amount, bonus_range)
    
    static func calculate_effective_wood_yield(base_min: int, base_max: int, bonus_range: String) -> int:
        """计算考虑装备加成的木材产量"""
        var base_yield = randi_range(base_min, base_max)
        return int(calculate_effective_amount(float(base_yield), bonus_range))
    
    static func calculate_effective_chopping_time(base_time: float, efficiency_multiplier: float) -> float:
        """计算考虑装备效率的砍伐时间"""
        if efficiency_multiplier <= 0.0:
            return base_time
        return base_time / efficiency_multiplier
    
    static func calculate_effective_chopping_damage(base_damage: int, damage_multiplier: float) -> int:
        """计算考虑装备的砍伐伤害"""
        if damage_multiplier <= 0.0:
            return base_damage
        return max(1, int(float(base_damage) * damage_multiplier))

## 烹饪系统配置
class CookingConstants:
    ## 基础时间
    const CUTTING_BASE_TIME: float = 3.0
    const COOKING_BASE_TIME: float = 5.0
    const COLLECT_FOOD_BASE_TIME: float = 2.0
    const STORE_FOOD_BASE_TIME: float = 1.5
    const GET_INGREDIENTS_BASE_TIME: float = 2.5
    const THINKING_TIME: float = 0.5
    const RESTING_TIME: float = 5.0

    ## 基础数值
    const FOOD_BASE_QUALITY: float = 1.0
    const MAX_CARRY_AMOUNT: int = 5
    const DEFAULT_AI_INTERVAL: float = 2.0

    ## 超时配置
    const KITCHEN_LOCK_TIMEOUT: float = 15.0
    const ITEM_LOCK_TIMEOUT: float = 2.0
    const VALIDATION_CACHE_TIMEOUT: float = 3.0
    const TASK_EXECUTION_TIMEOUT: float = 10.0

    ## 负载均衡配置
    const SCORING_WEIGHT_DISTANCE: float = 0.4
    const SCORING_WEIGHT_AVAILABILITY: float = 0.3
    const SCORING_WEIGHT_LOAD: float = 0.2
    const SCORING_WEIGHT_PRIORITY: float = 0.1
    const SCORING_VALUE_DISTANCE_BASE: float = 1000.0
    const SCORING_VALUE_AVAILABILITY_PENALTY: float = 0.1
    const SCORING_VALUE_LOAD_BASE: float = 200.0
    const SCORING_VALUE_PRIORITY_BASE: float = 100.0

    ## 🆕 厨房交互位置偏移
    const KITCHEN_CUTTING_AREA_OFFSET: Vector2 = Vector2(-48, 16)
    const KITCHEN_COOKING_AREA_OFFSET: Vector2 = Vector2(48, 16)

    ## 🆕 携带图标配置
    const CARRYING_ICON_OFFSET: Vector2 = Vector2(0, -20)
    const CARRYING_ICON_SCALE: Vector2 = Vector2(2, 2)
    const DEFAULT_ATLAS_REGION: Rect2 = Rect2(0, 0, 16, 16)

    ## 🆕 性能监控配置
    const MAX_AI_FREQUENCY: float = 1.0
    const MIN_AI_FREQUENCY: float = 3.0
    const PERFORMANCE_SAMPLE_SIZE: int = 10
    const AI_SLOW_THRESHOLD: float = 0.1
    const AI_FAST_THRESHOLD: float = 0.05
    const INTERACTION_HISTORY_LIMIT: int = 50

    ## 🆕 默认收集物品类型
    const DEFAULT_COLLECTIBLE_TYPES: Array[String] = ["wheat", "fish", "food", "ingredients"]

    ## 🆕 中文名称映射（可以考虑移到配置文件）
    static func get_chinese_name_map() -> Dictionary:
        """获取物品中文名称映射"""
        return {
            # 作物类
            "wheat_item": "小麦", "radish_item": "萝卜", "potato_item": "土豆",
            "turnip_item": "芜菁", "carrot_item": "胡萝卜", "cabbage_item": "卷心菜",
            "onion_item": "洋葱", "caisim_item": "菜心", "corn_item": "玉米",
            "spinach_item": "菠菜", "bell_pepper_green_item": "青椒", "leek_item": "韭菜",
            "soybean_item": "大豆",

            # 鱼类
            "bluegill": "蓝鳃太阳鱼", "smelt": "胡瓜鱼", "anchovy": "鳀鱼",
            "sardine": "沙丁鱼", "carp": "鲤鱼", "bass": "鲈鱼", "mackerel": "鲭鱼",
            "flounder": "比目鱼", "clam": "蛤蜊", "squid": "鱿鱼", "grouper": "石斑鱼",
            "red_snapper": "红鲷鱼", "seabass": "海鲈鱼", "tuna": "金枪鱼",

            # 动物产品
            "egg": "鸡蛋", "duck_egg": "鸭蛋", "milk": "牛奶", "pork": "猪肉",
            "beef": "牛肉", "chicken": "鸡肉", "mutton": "羊肉", "wool": "羊毛"
        }

    ## 🆕 UI配置常量
    const PROGRESS_BAR_SIZE: Vector2 = Vector2(60, 20)
    const PROGRESS_BAR_HEIGHT: float = 2.0
    const PROGRESS_BAR_Y_OFFSET: float = 16.0
    const PROGRESS_BAR_BACKGROUND_COLOR: Color = Color(0, 0, 0, 0.3)
    const PROGRESS_BAR_BG_COLOR: Color = Color(0.2, 0.2, 0.2, 0.8)
    const PROGRESS_BAR_CORNER_RADIUS: int = 1

    # 🆕 简化的计算辅助方法
    static func calculate_effective_amount(base_amount: float, bonus_range: String) -> float:
        """计算考虑范围加成的有效数量"""
        var parts = bonus_range.split("-")
        if parts.size() != 2:
            return base_amount

        var min_bonus = int(parts[0])
        var max_bonus = int(parts[1])

        if min_bonus == 0 and max_bonus == 0:
            return base_amount

        var random_bonus = randi() % (max_bonus - min_bonus + 1) + min_bonus
        return base_amount + float(random_bonus)

    static func get_default_action_times() -> Dictionary:
        """获取默认动作时间配置"""
        return {
            "cutting": CUTTING_BASE_TIME,
            "cooking": COOKING_BASE_TIME,
            "collecting": COLLECT_FOOD_BASE_TIME,
            "storing": STORE_FOOD_BASE_TIME,
            "thinking": THINKING_TIME,
            "resting": RESTING_TIME
        }

    static func get_scoring_weights() -> Dictionary:
        """获取智能负载均衡评分权重"""
        return {
            "distance": SCORING_WEIGHT_DISTANCE,
            "availability": SCORING_WEIGHT_AVAILABILITY,
            "load": SCORING_WEIGHT_LOAD,
            "priority": SCORING_WEIGHT_PRIORITY
        }

    static func get_scoring_values() -> Dictionary:
        """获取智能负载均衡评分基础值"""
        return {
            "distance_base": SCORING_VALUE_DISTANCE_BASE,
            "availability_locked_penalty": SCORING_VALUE_AVAILABILITY_PENALTY,
            "load_base": SCORING_VALUE_LOAD_BASE,
            "priority_base": SCORING_VALUE_PRIORITY_BASE
        }

## 矿业系统配置
class MiningConstants:
    ## 采矿系统基础数值
    const MINING_BASE_TIME: float = 3.0                 # 基础采矿时间
    const MINING_BASE_DAMAGE: int = 25                  # 基础采矿伤害（与Ore.gd中的默认值一致）
    const ORE_BASE_YIELD_MIN: int = 1                   # 基础最小矿物产量
    const ORE_BASE_YIELD_MAX: int = 2                   # 基础最大矿物产量

    ## 作业时间基础数值
    const COLLECT_ORE_BASE_TIME: float = 1.5            # 基础矿物收集时间
    const STORE_ORE_BASE_TIME: float = 2.0              # 基础矿物存储时间
    const IDLE_ACTION_TIME: float = 2.0                 # 空闲动作时间

    ## 🆕 超时时间配置
    const MINE_ORE_TIMEOUT: float = 30.0                # 采矿任务超时
    const COLLECT_ORE_TIMEOUT: float = 15.0             # 收集任务超时
    const STORE_ORE_TIMEOUT: float = 15.0               # 存储任务超时
    const ENTER_UNDERGROUND_TIMEOUT: float = 10.0       # 进入地下超时
    const EXIT_UNDERGROUND_TIMEOUT: float = 10.0        # 离开地下超时
    const RANDOM_WALK_TIMEOUT: float = 20.0             # 随机走动超时
    const INTERACTION_TIMEOUT: float = 30.0             # 交互超时
    const LOCK_TIMEOUT_DURATION: float = 15.0           # 锁定超时
    const VALIDATION_CACHE_TIMEOUT: float = 3.0         # 验证缓存超时

    ## 🆕 交互距离配置
    const MAX_INTERACTION_DISTANCE: float = 2.0         # 最大交互距离

    ## 🆕 AI频率配置
    const MAX_AI_FREQUENCY: float = 1.0                 # 最大AI频率
    const MIN_AI_FREQUENCY: float = 3.0                 # 最小AI频率
    const DEFAULT_AI_FREQUENCY: float = 2.0             # 默认AI频率
    const PERFORMANCE_SAMPLE_SIZE: int = 10             # 性能采样大小

    ## 🆕 矿物生成配置
    const RESPAWN_DELAY: float = 30.0                   # 重生延迟
    const GENERATION_DENSITY: float = 0.3               # 生成密度
    const MAX_TOTAL_ORES: int = 100                     # 最大矿物总数
    const RESPAWN_CHANCE: float = 0.8                   # 重生概率
    const EDGE_MARGIN: int = 1                          # 边缘边距
    const DEFAULT_BLOCKING_LAYERS: Array[int] = [2, 3, 4, 5, 6, 7]  # 默认阻挡层

    ## 🆕 UI和视觉效果配置
    const FALLBACK_TEXTURE_SIZE: int = 16              # 备用纹理尺寸
    const FADE_OUT_DURATION: float = 0.2               # 淡出动画时长
    const DEFAULT_ORE_COLOR: Color = Color.GRAY         # 默认矿物颜色
    
    ## 计算辅助方法
    static func calculate_effective_amount(base_amount: float, bonus_range: String) -> float:
        """计算最终有效数量：基础数值 + 装备加成 - 复用农业系统逻辑"""
        return AgricultureConstants.calculate_effective_amount(base_amount, bonus_range)
    
    static func calculate_effective_ore_yield(base_min: int, base_max: int, bonus_range: String) -> int:
        """计算考虑装备加成的矿物产量"""
        var base_yield = randi_range(base_min, base_max)
        return int(calculate_effective_amount(float(base_yield), bonus_range))
    
    static func calculate_effective_mining_time(base_time: float, efficiency_multiplier: float) -> float:
        """计算考虑装备效率的采矿时间"""
        if efficiency_multiplier <= 0.0:
            return base_time
        return base_time / efficiency_multiplier
    
    static func calculate_effective_mining_damage(base_damage: int, damage_multiplier: float) -> int:
        """计算考虑装备的采矿伤害"""
        if damage_multiplier <= 0.0:
            return base_damage
        return max(1, int(float(base_damage) * damage_multiplier))

    static func get_default_action_times() -> Dictionary:
        """获取默认动作时间配置"""
        return {
            "mine_action": MINING_BASE_TIME,
            "collect_action": COLLECT_ORE_BASE_TIME,
            "store_action": STORE_ORE_BASE_TIME,
            "idle_action": IDLE_ACTION_TIME
        }

    static func get_timeout_values() -> Dictionary:
        """获取超时时间配置"""
        return {
            "MINE_ORE": MINE_ORE_TIMEOUT,
            "COLLECT_ORE": COLLECT_ORE_TIMEOUT,
            "STORE_ORE": STORE_ORE_TIMEOUT,
            "ENTER_UNDERGROUND": ENTER_UNDERGROUND_TIMEOUT,
            "EXIT_UNDERGROUND": EXIT_UNDERGROUND_TIMEOUT,
            "RANDOM_WALK": RANDOM_WALK_TIMEOUT
        }

## 动物系统配置
class AnimalConstants:
    ## 生成配置
    const SPAWN_INTERVAL: float = 3000.0           # 生成间隔（秒）
    const MAX_WILD_ANIMALS: int = 15               # 最大野生动物数量
    const SPAWN_RADIUS: float = 400.0              # 生成半径
    const MIN_DISTANCE_FROM_BUILDINGS: float = 100.0  # 距离建筑最小距离

    ## 行为权重配置
    const DEFAULT_BEHAVIOR_WEIGHTS = {
        "idle": 40,
        "walk": 35,
        "eating": 15,
        "sleeping": 10
    }

    ## 状态持续时间配置
    const DEFAULT_STATE_DURATIONS = {
        "idle": {"min": 3.0, "max": 8.0},
        "walk": {"min": 2.0, "max": 5.0},
        "eating": {"min": 1.0, "max": 3.0},
        "sleeping": {"min": 5.0, "max": 10.0}
    }

    ## 生成权重配置
    const DEFAULT_SPAWN_WEIGHTS = {
        "chicken": 30,
        "duck": 25,
        "sheep": 20,
        "pig": 15,
        "cow": 8,
        "horse": 2
    }

    ## 基础属性
    const DEFAULT_MAX_HEALTH: int = 100
    const DEFAULT_MOVE_SPEED: float = 50.0
    const DEFAULT_ACTIVITY_RADIUS: float = 100.0
    const OFFSPRING_POSITION_OFFSET: float = 20.0

    ## 状态值常量
    const ANIMAL_STATE_WILD: int = 0
    const ANIMAL_STATE_TAMED: int = 1
    const AGE_GROUP_YOUNG: int = 0
    const AGE_GROUP_ADULT: int = 1
    const GENDER_MALE: int = 0
    const GENDER_FEMALE: int = 1

## 建筑系统配置
class BuildingConstants:
    ## 网格配置
    const PLACEMENT_GRID_SIZE: float = 32.0        # 放置网格大小

    ## 默认建筑网格大小
    const DEFAULT_BUILDING_GRID_SIZES = {
        0: Vector2i(2, 2),  # HOUSE
        1: Vector2i(4, 4),  # FARM
        2: Vector2i(1, 1),  # STORAGE
        3: Vector2i(1, 1),  # FARMLAND
        4: Vector2i(2, 2),  # BLACKSMITH
        5: Vector2i(1, 1),  # WELL
        6: Vector2i(1, 1),  # FISHINGSPOT
        7: Vector2i(2, 1),  # KITCHEN
        8: Vector2i(1, 1),  # MINE
        9: Vector2i(1, 1)   # TRAMCAR
    }

    ## 默认建筑场景路径
    const DEFAULT_BUILDING_SCENES = {
        0: "res://scenes/Buildings/House.tscn",
        1: "res://scenes/Buildings/Farm.tscn",
        2: "res://scenes/Buildings/Storage.tscn",
        3: "res://scenes/Buildings/FarmlandBuilding.tscn",
        5: "res://scenes/Buildings/Well.tscn",
        6: "res://scenes/Buildings/Fishingspot.tscn",
        7: "res://scenes/Buildings/Kitchen.tscn",
        8: "res://scenes/Buildings/Mine.tscn",
        9: "res://scenes/Buildings/Tramcar.tscn"
    }

    ## 默认建筑成本
    const DEFAULT_BUILDING_COSTS = {
        0: {"wood": 10, "stone": 5},   # HOUSE
        1: {"wood": 15, "stone": 8},   # FARM
        2: {"wood": 10, "stone": 5},   # STORAGE
        3: {"wood": 5, "stone": 3},    # FARMLAND
        5: {"wood": 8, "stone": 5},    # WELL
        6: {"wood": 12, "stone": 6},   # FISHINGSPOT
        7: {"wood": 15, "stone": 10},  # KITCHEN
        8: {"wood": 20, "stone": 15},  # MINE
        9: {"wood": 12, "stone": 8}    # TRAMCAR
    }

    ## 建筑名称映射
    const BUILDING_DISPLAY_NAMES = {
        "house": "房屋", "storage": "仓库", "farmland": "农田",
        "well": "水井", "fishingspot": "钓鱼点", "kitchen": "厨房",
        "mine": "矿井", "tramcar": "矿车"
    }

    ## 交互点默认偏移
    const DEFAULT_INTERACTION_OFFSET_LEFT: Vector2 = Vector2(-50, 0)
    const DEFAULT_INTERACTION_OFFSET_RIGHT: Vector2 = Vector2(50, 0)
    const DEFAULT_BOTTOM_POINT_OFFSET: Vector2 = Vector2(0, 16)

## UI系统配置
class UIConstants:
    ## 资源更新配置
    const RESOURCE_UPDATE_INTERVAL: float = 0.5    # 资源更新间隔

    ## 基础资源类型配置（与ResourceManager.ResourceType对应）
    const RESOURCE_TYPES: Array[int] = [0]  # 🔧 修正：只有COINS是基础资源

    ## 基础资源名称映射（与ResourceManager.ResourceType对应）
    const RESOURCE_NAMES = {
        0: "金币"     # ResourceType.COINS - 唯一的基础资源
    }

    ## 渔夫装备卡片配置
    const FISHERMAN_CARD_EXPANDED_HEIGHT: int = 192   # 展开后总高度 (56 + 136)
    const FISHERMAN_CARD_COLLAPSED_HEIGHT: int = 56   # 收起时高度

    ## 基础资源显示顺序
    const BASE_RESOURCES_ORDER: Array[String] = [
        "coins"  # 🔧 修正：只有金币是基础资源
    ]

    ## UI路径配置
    const MAIN_UI_PATHS: Array[String] = [
        "/root/Main/UI/MainUI",
        "/root/World/UI/MainUI",
        "/root/Main/MainUI",
        "../MainUI",
        "../UI/MainUI"
    ]

    ## 等待时间配置
    const UI_INITIALIZATION_DELAY: float = 0.1     # UI初始化延迟
    const PROCESS_FRAME_WAIT_COUNT: int = 3         # 等待帧数

    ## 日志级别
    const LOG_LEVEL_INFO: int = 0
    const LOG_LEVEL_WARNING: int = 1
    const LOG_LEVEL_ERROR: int = 2

## 装备系统配置
class EquipmentConstants:
    ## 配置文件路径
    const EQUIPMENT_CONFIG_PATH: String = "res://config/equipment.json"

    ## 装备槽位定义
    const EQUIPMENT_SLOTS: Array[String] = ["main_hand", "off_hand", "accessory", "consumable"]

    ## 装备分类
    const EQUIPMENT_CATEGORIES: Array[String] = ["main_hand", "off_hand", "accessory", "consumable"]

    ## 角色类型定义
    const CHARACTER_TYPES: Array[String] = ["farmer", "woodcutter", "chef", "miner", "fisherman"]

    ## 装备效果到角色属性的映射表
    const EFFECT_TO_ATTRIBUTE_MAPPING: Dictionary = {
        # 移动相关
        "move_speed": "movement_speed_multiplier",

        # 工作效率相关
        "harvest_bonus": "harvest_bonus_range",
        "harvest_efficiency": "harvest_efficiency_multiplier",
        "harvest_speed": "harvest_speed_multiplier",
        "work_efficiency": "work_efficiency_multiplier",

        # 容量相关
        "water_capacity": "water_capacity_bonus",
        "item_capacity": "item_capacity_bonus",

        # 伤害和效率相关
        "damage_bonus": "damage_multiplier",
        "chopping_efficiency": "chopping_efficiency_multiplier",
        "wood_yield_bonus": "wood_yield_bonus_range",
        "ore_bonus": "ore_bonus_range",

        # 烹饪相关
        "cooking_time_reduction": "cooking_time_reduction_multiplier",
        "cutting_time_reduction": "cutting_time_reduction_multiplier",
        "food_yield_bonus": "food_yield_bonus_range",

        # 其他效果
        "tool_efficiency": "tool_efficiency_multiplier",
        "stamina_efficiency": "stamina_efficiency_multiplier",
        "crop_quality_bonus": "crop_quality_bonus",
        "durability_protection": "durability_protection_bonus",
        "stamina_regeneration": "stamina_regeneration_bonus"
    }

    ## 效果处理类型定义
    enum EffectProcessType {
        ADDITIVE,      # 加法效果 (如 +5 容量)
        MULTIPLIER,    # 乘数效果 (如 +20% 速度)
        RANGE,         # 范围效果 (如 "2-4" 产量加成)
        ABSOLUTE,      # 绝对值效果 (如 water_capacity 直接设置容量)
        TIME_REDUCTION,# 时间减少效果 (如 0.1 表示减少10%时间)
        BOOLEAN,       # 布尔效果 (如 fishing_enabled)
        REPLACE        # 替换效果 (默认行为，使用新值替换旧值)
    }

    ## 效果处理类型映射
    const EFFECT_PROCESS_TYPES: Dictionary = {
        "move_speed": EffectProcessType.MULTIPLIER,
        "harvest_bonus": EffectProcessType.RANGE,
        "harvest_efficiency": EffectProcessType.MULTIPLIER,
        "harvest_speed": EffectProcessType.MULTIPLIER,
        "work_efficiency": EffectProcessType.MULTIPLIER,
        "water_capacity": EffectProcessType.ABSOLUTE,
        "item_capacity": EffectProcessType.ADDITIVE,
        "damage_bonus": EffectProcessType.MULTIPLIER,
        "chopping_efficiency": EffectProcessType.MULTIPLIER,
        "wood_yield_bonus": EffectProcessType.RANGE,
        "ore_bonus": EffectProcessType.RANGE,
        "cooking_time_reduction": EffectProcessType.TIME_REDUCTION,
        "cutting_time_reduction": EffectProcessType.TIME_REDUCTION,
        "food_yield_bonus": EffectProcessType.RANGE,
        "tool_efficiency": EffectProcessType.MULTIPLIER,
        "stamina_efficiency": EffectProcessType.MULTIPLIER,
        "crop_quality_bonus": EffectProcessType.ADDITIVE,
        "durability_protection": EffectProcessType.ADDITIVE,
        "stamina_regeneration": EffectProcessType.ADDITIVE,
        "fishing_configured": EffectProcessType.BOOLEAN,
        "fishing_enabled": EffectProcessType.BOOLEAN
    }

    ## 默认效果描述（回退机制）
    const DEFAULT_EFFECT_DESCRIPTIONS: Dictionary = {
        "harvest_bonus": "收获作物数量加成",
        "water_capacity": "单次携带水量",
        "damage_bonus": "伤害加成",
        "cutting_time_reduction": "切菜时长减少",
        "cooking_time_reduction": "烹饪时长减少",
        "fishing_configured": "钓鱼功能已配置",
        "wood_bonus": "木材掉落加成",
        "ore_bonus": "矿石掉落加成",
        "move_speed": "移动速度加成",
        "item_capacity": "物品容量加成",
        "harvest_efficiency": "收获效率",
        "harvest_speed": "收获速度",
        "work_efficiency": "工作效率",
        "chopping_efficiency": "砍伐效率",
        "cooking_efficiency": "烹饪效率",
        "tool_efficiency": "工具效率",
        "stamina_efficiency": "体力效率",
        "wood_yield": "木材产量",
        "wood_yield_bonus": "木材产量加成",
        "food_quality": "食物品质",
        "crop_quality_bonus": "作物品质加成",
        "durability_protection": "耐久保护",
        "stamina_regeneration": "体力恢复",
        "food_yield_bonus": "食物产量加成"
    }

## 角色系统配置
class CharacterConstants:
    ## 默认角色属性
    const DEFAULT_CHARACTER_HEALTH: int = 100      # 默认角色生命值
    const DEFAULT_CHARACTER_ENERGY: int = 100      # 默认角色能量值

    ## 角色场景路径
    const CHARACTER_SCENES = {
        0: "res://scenes/Characters/Farmer.tscn",      # FARMER
        1: "res://scenes/Characters/Character.tscn",   # VILLAGER
        2: "res://scenes/Characters/Woodcutter.tscn",  # WOODCUTTER
        3: "res://scenes/Characters/Fisherman.tscn",   # FISHER
        4: "res://scenes/Characters/Chef.tscn",        # CHEF
        5: "res://scenes/Characters/Character.tscn",   # CRAFTER
        6: "res://scenes/Characters/Miner.tscn"        # MINER
    }

## 世界系统配置
class WorldConstants:
    ## 默认生成位置
    const DEFAULT_SPAWN_POSITION: Vector2 = Vector2(520, 149)

    ## 初始化配置
    const INITIALIZATION_RETRY_DELAY: float = 1.0  # 初始化重试延迟（秒）