Godot Engine v4.3.stable.official.77dcf97d8 - https://godotengine.org
OpenGL API 3.3.0 NVIDIA 552.44 - Compatibility - Using Device: NVIDIA - NVIDIA GeForce GTX 1060 6GB

[GameLogger] 全局日志级别已设置为: 信息
[GameLogger] 模块 'Crop' 日志级别已设置为: 信息
[GameLogger] 模块 'DataManager' 日志级别已设置为: 信息
[GameLogger] 模块 'FarmerInteraction' 日志级别已设置为: 信息
[信息] [GameManager] 日志系统初始化完成
[DataManager] 配置加载完成: 7/7 成功
ResourceManager: 初始化完成
角色管理器初始化完成
[信息] [GameManager] 渔业数据管理器创建并加载数据成功
[INFO] OreDataManager: 矿物配置加载成功，包含 15 种矿物类型
[信息] [GameManager] 矿业数据管理器创建并加载数据成功
已加载 6 种动物配置
野生动物生成器已启动，间隔: 3000秒
[信息] [GameManager] 管理器初始化完成
[DataManager] 配置加载完成: 7/7 成功
=== 动物外观变体测试器启动 ===
支持动物类型: 牛(C) 羊(S) 猪(P) 马(H) 鸡(K) 鸭(D)
当前动物类型: cow
按F1键查看详细帮助
动物变体测试器控制说明:
数字键1-9: 生成不同变体动物
按键T: 测试所有变体(8种组合)
动物类型切换: C(牛) S(羊) P(猪) H(马) K(鸡) D(鸭)
按键F1: 显示详细帮助
当前动物类型: cow
CharacterEquipmentUI: 管理器引用设置完成
[TramcarBuilding] [INFO] 已将矿车添加到tramcars组
[TradingPost] 已将交易站添加到tradingposts组
[TradingPost] UI交互区域已设置
[TradingPost] 商队定时器已设置，间隔: 1 分钟
[动物围栏--1] UI交互区域已设置
[GridAreaManager] GridAreaManager initialized with TileMap: TerrainTileMap and TileSize: 32
角色管理器: 开始初始化，当前状态:false
角色管理器: 设置世界引用:World
角色管理器: 初始化完成，_world设置为:World
SystemsManager: 开始初始化系统...
SystemsManager: AttributeBoostSystem 已创建并初始化
[TramcarBuilding] [INFO] 矿车已连接到ResourceManager
[TramcarBuilding] [INFO] 矿车支持存储类型: ["stone", "coal", "iron", "copper", "silver", "gold", "platinum", "adamantite", "meteorite", "titanium", "diamond", "emerald", "topaz", "ruby", "sapphire"]
[BlacksmithBuilding] INFO: 成功创建铁匠铺UI
[BlacksmithBuilding] INFO: UI交互区域已设置
[BlacksmithBuilding] INFO: 自动处理计时器已启动
SceneSwitcher: 相机已设置，初始位置: (960, 128)
World: 场景切换器已初始化
角色管理器: 设置世界引用:World
动物系统测试器已启动
Main: 动物系统测试器已添加
AttributeBoostSystem: 已连接EquipmentManager信号
动物系统测试器初始化完成
[信息] [GameManager] 所有子系统初始化完成
[信息] [GameManager] 属性增强系统就绪，装备效果应用系统可用
SystemsManager: 系统初始化完成
OreLifecycleManager: 支持的矿石类型数量: 15
OreLifecycleManager: 包含矿石: ["stone", "coal", "iron", "copper", "silver", "gold", "platinum", "adamantite", "meteorite", "titanium", "diamond", "emerald", "topaz", "ruby", "sapphire"]
OreLifecycleManager: 生成位置计算完成
  - 总可用瓦片: 92
  - 边缘过滤: 0
  - 层级阻挡过滤: 38
  - 最终生成位置: 54
OreLifecycleManager: 初始生成完成，共生成 13 个矿物
[未知建筑--1] [仓库] 交互区域已就绪
[厨房-1] [仓库] 交互区域已就绪
(2) [水井] 初始化完成，永远有水供应
[FarmlandBuilding:-1]: Farmland实例已创建，ID: -1
[FarmlandBuilding:-1]: 设置预览状态为: true
[FarmlandBuilding:3]: Farmland实例已创建，ID: 3
[FarmlandBuilding:3]: FarmlandBuilding初始化完成，ID: 3
[FarmlandBuilding:-1]: Farmland实例已创建，ID: -1
[FarmlandBuilding:-1]: 设置预览状态为: true
[FarmlandBuilding:4]: Farmland实例已创建，ID: 4
[FarmlandBuilding:4]: FarmlandBuilding初始化完成，ID: 4
CharacterEquipmentUI: 开始在场景中生成角色，类型:farmer位置:(492.6004, 149.1836)
角色管理器: 开始生成角色，类型:0位置:(492.6004, 149.1836)
[CharacterPos] 角色管理器: 预设置角色位置 (492.600372314453, 149.183624267578)
角色管理器：调用 farmer.initialize() for ID 411964549161
[CharacterPos] 角色管理器: 使用底部点偏移设置位置 (492.600372314453, 149.183624267578)

Farmer: 设置默认作物类型: wheat
[CharacterManager] 角色生成成功：farmer (ID: 411964549161) at (492.6004, 149.1836)
EquipmentManager: 角色 411964549161 已注册到装备系统
AttributeBoostSystem: 应用移动速度到角色 411964549161: 80.000000 (基础: 80.000000)
WorkerCharacter: 应用移动速度加成 80.000000 -> 80.000000
WorkerCharacter: 应用工作效率加成 1.000000
WorkerCharacter: 应用收获加成 0-0
WorkerCharacter: 应用水桶容量 0
WorkerCharacter: 应用物品容量加成 0
WorkerCharacter: 应用伤害加成 1.000000
WorkerCharacter: 应用木材产量加成范围 0-0
WorkerCharacter: 应用砍伐效率 1.000000
WorkerCharacter: 应用烹饪时间倍数 1.000000
WorkerCharacter: 应用切菜时间倍数 1.000000
WorkerCharacter: 应用食物产量加成范围 0-0
WorkerCharacter: 应用木材产量加成 0-0
WorkerCharacter: 应用钓鱼功能 禁用
Farmer: 收获加成更新为 0-0
Farmer: 水桶容量更新为 0
Farmer: 工作效率更新为 1.000000
Farmer: 收获时间更新为 2.000000 秒 (加速倍数: 1.000000)
Farmer: 种植时间更新为 1.500000 秒 (效率倍数: 1.000000)
Farmer: 根据品质加成 1.000000 调整作物偏好为 yam
AttributeBoostSystem: 注册角色 411964549161，基础属性: { "movement_speed_multiplier": 1, "harvest_bonus_range": "0-0", "harvest_efficiency_multiplier": 1, "harvest_speed_multiplier": 1, "work_efficiency_multiplier": 1, "damage_multiplier": 1, "cutting_time_multiplier": 1, "cooking_time_multiplier": 1, "water_capacity_bonus": 0, "item_capacity_bonus": 0, "wood_yield_bonus": "0-0", "ore_yield_bonus": "0-0", "wood_yield_multiplier": 1, "wood_yield_bonus_range": "0-0", "chopping_efficiency_multiplier": 1, "food_yield_bonus_range": "0-0", "tool_efficiency_multiplier": 1, "stamina_efficiency_multiplier": 1, "crop_quality_multiplier": 1, "durability_protection_multiplier": 1, "stamina_regeneration_multiplier": 1, "fishing_enabled": false }
AttributeBoostSystem: 应用移动速度到角色 411964549161: 80.000000 (基础: 80.000000)
WorkerCharacter: 应用移动速度加成 80.000000 -> 80.000000
WorkerCharacter: 应用工作效率加成 1.000000
WorkerCharacter: 应用收获加成 0-0
WorkerCharacter: 应用水桶容量 0
WorkerCharacter: 应用物品容量加成 0
WorkerCharacter: 应用伤害加成 1.000000
WorkerCharacter: 应用木材产量加成范围 0-0
WorkerCharacter: 应用砍伐效率 1.000000
WorkerCharacter: 应用烹饪时间倍数 1.000000
WorkerCharacter: 应用切菜时间倍数 1.000000
WorkerCharacter: 应用食物产量加成范围 0-0
WorkerCharacter: 应用木材产量加成 0-0
WorkerCharacter: 应用钓鱼功能 禁用
Farmer: 收获加成更新为 0-0
Farmer: 水桶容量更新为 0
Farmer: 工作效率更新为 1.000000
Farmer: 收获时间更新为 2.000000 秒 (加速倍数: 1.000000)
Farmer: 种植时间更新为 1.500000 秒 (效率倍数: 1.000000)
Farmer: 根据品质加成 1.000000 调整作物偏好为 yam
AttributeBoostSystem: 更新角色 411964549161 装备效果: {  }
CharacterEquipmentUI: 角色创建成功 - ID:411964549161名称:农夫1类型:farmer
[WorkerCharacter] 启动工作操作，AI启用: true
[WorkerCharacter] 启用任务管理器AI
[WorkerCharacter] 调用TaskManager的_add_initial_task方法
[WorkerCharacter] 启用计时器管理器AI
AttributeBoostSystem: 应用移动速度到角色 411964549161: 80.000000 (基础: 80.000000)
WorkerCharacter: 应用移动速度加成 80.000000 -> 80.000000
WorkerCharacter: 应用工作效率加成 1.000000
WorkerCharacter: 应用收获加成 0-0
WorkerCharacter: 应用水桶容量 0
WorkerCharacter: 应用物品容量加成 0
WorkerCharacter: 应用伤害加成 1.000000
WorkerCharacter: 应用木材产量加成范围 0-0
WorkerCharacter: 应用砍伐效率 1.000000
WorkerCharacter: 应用烹饪时间倍数 1.000000
WorkerCharacter: 应用切菜时间倍数 1.000000
WorkerCharacter: 应用食物产量加成范围 0-0
WorkerCharacter: 应用木材产量加成 0-0
WorkerCharacter: 应用钓鱼功能 禁用
Farmer: 收获加成更新为 0-0
Farmer: 水桶容量更新为 0
Farmer: 工作效率更新为 1.000000
Farmer: 收获时间更新为 2.000000 秒 (加速倍数: 1.000000)
Farmer: 种植时间更新为 1.500000 秒 (效率倍数: 1.000000)
Farmer: 根据品质加成 1.000000 调整作物偏好为 yam
AttributeBoostSystem: 注册角色 411964549161，基础属性: { "movement_speed_multiplier": 1, "harvest_bonus_range": "0-0", "harvest_efficiency_multiplier": 1, "harvest_speed_multiplier": 1, "work_efficiency_multiplier": 1, "damage_multiplier": 1, "cutting_time_multiplier": 1, "cooking_time_multiplier": 1, "water_capacity_bonus": 0, "item_capacity_bonus": 0, "wood_yield_bonus": "0-0", "ore_yield_bonus": "0-0", "wood_yield_multiplier": 1, "wood_yield_bonus_range": "0-0", "chopping_efficiency_multiplier": 1, "food_yield_bonus_range": "0-0", "tool_efficiency_multiplier": 1, "stamina_efficiency_multiplier": 1, "crop_quality_multiplier": 1, "durability_protection_multiplier": 1, "stamina_regeneration_multiplier": 1, "fishing_enabled": false, "base_move_speed": 80, "work_efficiency": 1, "water_capacity": 0, "item_capacity": 0, "tool_efficiency": 1 }
WorkerCharacter: 已向AttributeBoostSystem注册角色 411964549161
🕒 [农夫动态超时] 任务类型:9, 预计动作时间:5.0秒, 设置超时:20.0秒
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 9)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 0.1秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 0.1秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 0.8秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager:Farmer] 找到 2 个有指定作物的农田，0 个无指定作物的农田
🎯 [农夫TaskManager] 统一时间获取 - 动作:plant, 时间:1.5秒
🕒 [农夫动态超时] 任务类型:2, 预计动作时间:1.5秒, 设置超时:15.0秒
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 2)
农夫: 开始工作流程 [farm_workflow_1753762060.5_357] - 类型: plant_cycle, 目标: Farmland
农夫: 工作流程阶段更新 [farm_workflow_1753762060.5_357] - planting (进度: 0.0%)
[WorkerCharacter] 动画切换: plant -> plant (状态: WORKING_2)
[FarmerTaskManager] 统一状态更新: IDLE -> PLANTING
[FarmerInteractionManager:Farmer] 统一状态更新: IDLE -> PLANTING
[FarmerTimerManager] 统一状态更新: IDLE -> PLANTING
[Farmer] 统一状态变更: IDLE -> PLANTING
[WorkerCharacter] 侧面评分 - 左侧: 97.06, 右侧: 55.30, 选择: left
[WorkerCharacter] 智能选择交互侧面: left (目标: Farmland)
[WorkerCharacter] 使用get_interaction_data成功: 位置(540.0, 184.0), 侧面: left
[BaseInteractionManager] 开始交互: plant, 目标: Farmland, 距离: 58.8, 目标位置: (540.0, 184.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(540.0, 184.0), 侧面: left
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: PLANTING -> MOVING
[FarmerInteractionManager:Farmer] 统一状态更新: PLANTING -> MOVING
[FarmerTimerManager] 统一状态更新: PLANTING -> MOVING
[Farmer] 统一状态变更: PLANTING -> MOVING
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: plant, 目标: Farmland
[WorkerCharacter] 动画切换: plant -> plant (状态: WORKING_2)
[FarmerInteractionManager] 启动动作计时器: plant_action, 预期时长: 1.5秒
[FarmerTimerManager] 使用预设动作时间: plant_action = 1.5秒
[FarmerTimerManager] 🔧 计时器已启动: plant_action, 实际时长: 1.5秒
[BaseInteractionManager] 交互执行结果: true
[FarmerInteractionManager:Farmer] 种植指定作物: potato
[Crop-potato] [DEBUG] 作物初始化完成: 土豆
[FarmerInteractionManager:Farmer] 成功种植作物: potato
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
农夫: 工作流程阶段更新 [farm_workflow_1753762060.5_357] - watering (进度: 50.0%)
🎯 [农夫TaskManager] 统一时间获取 - 动作:fetch_water, 时间:2.0秒
🕒 [农夫动态超时] 任务类型:5, 预计动作时间:2.0秒, 设置超时:15.0秒
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 5)
[Farmer] 统一状态动画: collecting
[FarmerTaskManager] 统一状态更新: MOVING -> FETCHING_WATER
[FarmerInteractionManager:Farmer] 统一状态更新: MOVING -> FETCHING_WATER
[FarmerTimerManager] 统一状态更新: MOVING -> FETCHING_WATER
[Farmer] 统一状态变更: MOVING -> FETCHING_WATER
[WorkerCharacter] 侧面评分 - 左侧: 93.09, 右侧: 53.09, 选择: left
[WorkerCharacter] 智能选择交互侧面: left (目标: Well)
[WorkerCharacter] 使用get_interaction_data成功: 位置(676.0, 160.0), 侧面: left
[BaseInteractionManager] 开始交互: fetch_water, 目标: Well, 距离: 138.1, 目标位置: (676.0, 160.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(676.0, 160.0), 侧面: left
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: FETCHING_WATER -> MOVING
[FarmerInteractionManager:Farmer] 统一状态更新: FETCHING_WATER -> MOVING
[FarmerTimerManager] 统一状态更新: FETCHING_WATER -> MOVING
[Farmer] 统一状态变更: FETCHING_WATER -> MOVING
[Crop-potato] [INFO] 作物因缺水停止生长
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: fetch_water, 目标: Well
[WorkerCharacter] 动画切换: collecting -> collecting (状态: COLLECTING)
[FarmerInteractionManager] 启动动作计时器: fetch_water_action, 预期时长: 2.0秒
[FarmerTimerManager] 使用预设动作时间: fetch_water_action = 2.0秒
[FarmerTimerManager] 🔧 计时器已启动: fetch_water_action, 实际时长: 2.0秒
[BaseInteractionManager] 交互执行结果: true
[FarmerInteractionManager:Farmer] 使用装备水桶容量取水: 5.0 (基础: 5.0 + 装备: 0)
[水井] 农夫请求取水: 5.0 单位，直接提供
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[FarmerTaskManager] 统一状态更新: MOVING -> CARRYING
[FarmerInteractionManager:Farmer] 统一状态更新: MOVING -> CARRYING
[FarmerTimerManager] 统一状态更新: MOVING -> CARRYING
[Farmer] 统一状态变更: MOVING -> CARRYING
[FarmerInteractionManager:Farmer] 成功取水 5.0 单位 (请求: 5.0)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
🎯 [农夫TaskManager] 统一时间获取 - 动作:water, 时间:1.2秒
🕒 [农夫动态超时] 任务类型:6, 预计动作时间:1.2秒, 设置超时:15.0秒
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 6)
[FarmerTaskManager] 统一状态更新: CARRYING -> IDLE
[FarmerInteractionManager:Farmer] 统一状态更新: CARRYING -> IDLE
[FarmerTimerManager] 统一状态更新: CARRYING -> IDLE
[Farmer] 统一状态变更: CARRYING -> IDLE
[WorkerCharacter] 侧面评分 - 左侧: 53.09, 右侧: 95.05, 选择: right
[WorkerCharacter] 智能选择交互侧面: right (目标: Farmland)
[WorkerCharacter] 使用get_interaction_data成功: 位置(580.0, 184.0), 侧面: right
[BaseInteractionManager] 开始交互: water, 目标: Farmland, 距离: 99.0, 目标位置: (580.0, 184.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(580.0, 184.0), 侧面: right
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: IDLE -> MOVING
[FarmerInteractionManager:Farmer] 统一状态更新: IDLE -> MOVING
[FarmerTimerManager] 统一状态更新: IDLE -> MOVING
[Farmer] 统一状态变更: IDLE -> MOVING
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: water, 目标: Farmland
[WorkerCharacter] 动画切换: water -> water (状态: WORKING_3)
[FarmerInteractionManager] 启动动作计时器: water_action, 预期时长: 1.2秒
[FarmerTimerManager] 使用预设动作时间: water_action = 1.2秒
[FarmerTimerManager] 🔧 计时器已启动: water_action, 实际时长: 1.2秒
[BaseInteractionManager] 交互执行结果: true
[FarmerInteractionManager:Farmer] 成功浇水 5.0 单位
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
农夫: 工作流程完成 [farm_workflow_1753762060.5_357] - 状态: completed, 耗时: 8.6秒
[FarmerTaskManager:Farmer] 找到 1 个有指定作物的农田，0 个无指定作物的农田
🎯 [农夫TaskManager] 统一时间获取 - 动作:plant, 时间:1.5秒
🕒 [农夫动态超时] 任务类型:2, 预计动作时间:1.5秒, 设置超时:15.0秒
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 2)
农夫: 开始工作流程 [farm_workflow_1753762069.067_879] - 类型: plant_cycle, 目标: Farmland
农夫: 工作流程阶段更新 [farm_workflow_1753762069.067_879] - planting (进度: 0.0%)
[WorkerCharacter] 动画切换: plant -> plant (状态: WORKING_2)
[FarmerTaskManager] 统一状态更新: MOVING -> PLANTING
[FarmerInteractionManager:Farmer] 统一状态更新: MOVING -> PLANTING
[FarmerTimerManager] 统一状态更新: MOVING -> PLANTING
[Farmer] 统一状态变更: MOVING -> PLANTING
[WorkerCharacter] 侧面评分 - 左侧: 57.44, 右侧: 98.40, 选择: right
[WorkerCharacter] 智能选择交互侧面: right (目标: Farmland)
[WorkerCharacter] 使用get_interaction_data成功: 位置(580.0, 216.0), 侧面: right
[BaseInteractionManager] 开始交互: plant, 目标: Farmland, 距离: 32.0, 目标位置: (580.0, 216.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(580.0, 216.0), 侧面: right
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: PLANTING -> MOVING
[FarmerInteractionManager:Farmer] 统一状态更新: PLANTING -> MOVING
[FarmerTimerManager] 统一状态更新: PLANTING -> MOVING
[Farmer] 统一状态变更: PLANTING -> MOVING
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: plant, 目标: Farmland
[WorkerCharacter] 动画切换: plant -> plant (状态: WORKING_2)
[FarmerInteractionManager] 启动动作计时器: plant_action, 预期时长: 1.5秒
[FarmerTimerManager] 使用预设动作时间: plant_action = 1.5秒
[FarmerTimerManager] 🔧 计时器已启动: plant_action, 实际时长: 1.5秒
[BaseInteractionManager] 交互执行结果: true
[FarmerInteractionManager:Farmer] 种植指定作物: turnip
[Crop-turnip] [DEBUG] 作物初始化完成: 白萝卜
[FarmerInteractionManager:Farmer] 成功种植作物: turnip
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
农夫: 工作流程阶段更新 [farm_workflow_1753762069.067_879] - watering (进度: 50.0%)
🎯 [农夫TaskManager] 统一时间获取 - 动作:fetch_water, 时间:2.0秒
🕒 [农夫动态超时] 任务类型:5, 预计动作时间:2.0秒, 设置超时:15.0秒
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 5)
[Farmer] 统一状态动画: collecting
[FarmerTaskManager] 统一状态更新: MOVING -> FETCHING_WATER
[FarmerInteractionManager:Farmer] 统一状态更新: MOVING -> FETCHING_WATER
[FarmerTimerManager] 统一状态更新: MOVING -> FETCHING_WATER
[Farmer] 统一状态变更: MOVING -> FETCHING_WATER
[WorkerCharacter] 侧面评分 - 左侧: 94.44, 右侧: 54.44, 选择: left
[WorkerCharacter] 智能选择交互侧面: left (目标: Well)
[WorkerCharacter] 使用get_interaction_data成功: 位置(676.0, 160.0), 侧面: left
[BaseInteractionManager] 开始交互: fetch_water, 目标: Well, 距离: 111.1, 目标位置: (676.0, 160.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(676.0, 160.0), 侧面: left
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: FETCHING_WATER -> MOVING
[FarmerInteractionManager:Farmer] 统一状态更新: FETCHING_WATER -> MOVING
[FarmerTimerManager] 统一状态更新: FETCHING_WATER -> MOVING
[Farmer] 统一状态变更: FETCHING_WATER -> MOVING
[Crop-turnip] [INFO] 作物因缺水停止生长
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: fetch_water, 目标: Well
[WorkerCharacter] 动画切换: collecting -> collecting (状态: COLLECTING)
[FarmerInteractionManager] 启动动作计时器: fetch_water_action, 预期时长: 2.0秒
[FarmerTimerManager] 使用预设动作时间: fetch_water_action = 2.0秒
[FarmerTimerManager] 🔧 计时器已启动: fetch_water_action, 实际时长: 2.0秒
[BaseInteractionManager] 交互执行结果: true
[FarmerInteractionManager:Farmer] 使用装备水桶容量取水: 5.0 (基础: 5.0 + 装备: 0)
[水井] 农夫请求取水: 5.0 单位，直接提供
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[FarmerTaskManager] 统一状态更新: MOVING -> CARRYING
[FarmerInteractionManager:Farmer] 统一状态更新: MOVING -> CARRYING
[FarmerTimerManager] 统一状态更新: MOVING -> CARRYING
[Farmer] 统一状态变更: MOVING -> CARRYING
[FarmerInteractionManager:Farmer] 成功取水 5.0 单位 (请求: 5.0)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
🎯 [农夫TaskManager] 统一时间获取 - 动作:water, 时间:1.2秒
🕒 [农夫动态超时] 任务类型:6, 预计动作时间:1.2秒, 设置超时:15.0秒
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 6)
[FarmerTaskManager] 统一状态更新: CARRYING -> IDLE
[FarmerInteractionManager:Farmer] 统一状态更新: CARRYING -> IDLE
[FarmerTimerManager] 统一状态更新: CARRYING -> IDLE
[Farmer] 统一状态变更: CARRYING -> IDLE
[WorkerCharacter] 侧面评分 - 左侧: 52.65, 右侧: 94.44, 选择: right
[WorkerCharacter] 智能选择交互侧面: right (目标: Farmland)
[WorkerCharacter] 使用get_interaction_data成功: 位置(580.0, 216.0), 侧面: right
[BaseInteractionManager] 开始交互: water, 目标: Farmland, 距离: 111.1, 目标位置: (580.0, 216.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(580.0, 216.0), 侧面: right
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: IDLE -> MOVING
[FarmerInteractionManager:Farmer] 统一状态更新: IDLE -> MOVING
[FarmerTimerManager] 统一状态更新: IDLE -> MOVING
[Farmer] 统一状态变更: IDLE -> MOVING
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[Crop-potato] [DEBUG] 作物 '土豆' 进入新阶段: growing1
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: water, 目标: Farmland
[WorkerCharacter] 动画切换: water -> water (状态: WORKING_3)
[FarmerInteractionManager] 启动动作计时器: water_action, 预期时长: 1.2秒
[FarmerTimerManager] 使用预设动作时间: water_action = 1.2秒
[FarmerTimerManager] 🔧 计时器已启动: water_action, 实际时长: 1.2秒
[BaseInteractionManager] 交互执行结果: true
[FarmerInteractionManager:Farmer] 成功浇水 5.0 单位
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
农夫: 工作流程完成 [farm_workflow_1753762069.067_879] - 状态: completed, 耗时: 8.1秒
🎯 [农夫TaskManager] 统一时间获取 - 动作:fetch_water, 时间:2.0秒
🕒 [农夫动态超时] 任务类型:5, 预计动作时间:2.0秒, 设置超时:15.0秒
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 5)
[Farmer] 统一状态动画: collecting
[FarmerTaskManager] 统一状态更新: MOVING -> FETCHING_WATER
[FarmerInteractionManager:Farmer] 统一状态更新: MOVING -> FETCHING_WATER
[FarmerTimerManager] 统一状态更新: MOVING -> FETCHING_WATER
[Farmer] 统一状态变更: MOVING -> FETCHING_WATER
[WorkerCharacter] 侧面评分 - 左侧: 94.44, 右侧: 54.44, 选择: left
[WorkerCharacter] 智能选择交互侧面: left (目标: Well)
[WorkerCharacter] 使用get_interaction_data成功: 位置(676.0, 160.0), 侧面: left
[BaseInteractionManager] 开始交互: fetch_water, 目标: Well, 距离: 111.1, 目标位置: (676.0, 160.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(676.0, 160.0), 侧面: left
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: FETCHING_WATER -> MOVING
[FarmerInteractionManager:Farmer] 统一状态更新: FETCHING_WATER -> MOVING
[FarmerTimerManager] 统一状态更新: FETCHING_WATER -> MOVING
[Farmer] 统一状态变更: FETCHING_WATER -> MOVING
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: fetch_water, 目标: Well
[WorkerCharacter] 动画切换: collecting -> collecting (状态: COLLECTING)
[FarmerInteractionManager] 启动动作计时器: fetch_water_action, 预期时长: 2.0秒
[FarmerTimerManager] 使用预设动作时间: fetch_water_action = 2.0秒
[FarmerTimerManager] 🔧 计时器已启动: fetch_water_action, 实际时长: 2.0秒
[BaseInteractionManager] 交互执行结果: true
[FarmerInteractionManager:Farmer] 使用装备水桶容量取水: 5.0 (基础: 5.0 + 装备: 0)
[水井] 农夫请求取水: 5.0 单位，直接提供
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[FarmerTaskManager] 统一状态更新: MOVING -> CARRYING
[FarmerInteractionManager:Farmer] 统一状态更新: MOVING -> CARRYING
[FarmerTimerManager] 统一状态更新: MOVING -> CARRYING
[Farmer] 统一状态变更: MOVING -> CARRYING
[FarmerInteractionManager:Farmer] 成功取水 5.0 单位 (请求: 5.0)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
🎯 [农夫TaskManager] 统一时间获取 - 动作:water, 时间:1.2秒
🕒 [农夫动态超时] 任务类型:6, 预计动作时间:1.2秒, 设置超时:15.0秒
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 6)
[FarmerTaskManager] 统一状态更新: CARRYING -> IDLE
[FarmerInteractionManager:Farmer] 统一状态更新: CARRYING -> IDLE
[FarmerTimerManager] 统一状态更新: CARRYING -> IDLE
[Farmer] 统一状态变更: CARRYING -> IDLE
[WorkerCharacter] 侧面评分 - 左侧: 52.65, 右侧: 94.44, 选择: right
[WorkerCharacter] 智能选择交互侧面: right (目标: Farmland)
[WorkerCharacter] 使用get_interaction_data成功: 位置(580.0, 216.0), 侧面: right
[BaseInteractionManager] 开始交互: water, 目标: Farmland, 距离: 111.1, 目标位置: (580.0, 216.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(580.0, 216.0), 侧面: right
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: IDLE -> MOVING
[FarmerInteractionManager:Farmer] 统一状态更新: IDLE -> MOVING
[FarmerTimerManager] 统一状态更新: IDLE -> MOVING
[Farmer] 统一状态变更: IDLE -> MOVING
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[Crop-potato] [DEBUG] 作物 '土豆' 进入新阶段: growing2
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: water, 目标: Farmland
[WorkerCharacter] 动画切换: water -> water (状态: WORKING_3)
[FarmerInteractionManager] 启动动作计时器: water_action, 预期时长: 1.2秒
[FarmerTimerManager] 使用预设动作时间: water_action = 1.2秒
[FarmerTimerManager] 🔧 计时器已启动: water_action, 实际时长: 1.2秒
[BaseInteractionManager] 交互执行结果: true
[FarmerInteractionManager:Farmer] 成功浇水 5.0 单位
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
🎯 [农夫TaskManager] 统一时间获取 - 动作:fetch_water, 时间:2.0秒
🕒 [农夫动态超时] 任务类型:5, 预计动作时间:2.0秒, 设置超时:15.0秒
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 5)
[Farmer] 统一状态动画: collecting
[FarmerTaskManager] 统一状态更新: MOVING -> FETCHING_WATER
[FarmerInteractionManager:Farmer] 统一状态更新: MOVING -> FETCHING_WATER
[FarmerTimerManager] 统一状态更新: MOVING -> FETCHING_WATER
[Farmer] 统一状态变更: MOVING -> FETCHING_WATER
[WorkerCharacter] 侧面评分 - 左侧: 94.44, 右侧: 54.44, 选择: left
[WorkerCharacter] 智能选择交互侧面: left (目标: Well)
[WorkerCharacter] 使用get_interaction_data成功: 位置(676.0, 160.0), 侧面: left
[BaseInteractionManager] 开始交互: fetch_water, 目标: Well, 距离: 111.1, 目标位置: (676.0, 160.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(676.0, 160.0), 侧面: left
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: FETCHING_WATER -> MOVING
[FarmerInteractionManager:Farmer] 统一状态更新: FETCHING_WATER -> MOVING
[FarmerTimerManager] 统一状态更新: FETCHING_WATER -> MOVING
[Farmer] 统一状态变更: FETCHING_WATER -> MOVING
[Crop-potato] [INFO] 作物因缺水停止生长
[Crop-turnip] [DEBUG] 作物 '白萝卜' 进入新阶段: growing1
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: fetch_water, 目标: Well
[WorkerCharacter] 动画切换: collecting -> collecting (状态: COLLECTING)
[FarmerInteractionManager] 启动动作计时器: fetch_water_action, 预期时长: 2.0秒
[FarmerTimerManager] 使用预设动作时间: fetch_water_action = 2.0秒
[FarmerTimerManager] 🔧 计时器已启动: fetch_water_action, 实际时长: 2.0秒
[BaseInteractionManager] 交互执行结果: true
[FarmerInteractionManager:Farmer] 使用装备水桶容量取水: 5.0 (基础: 5.0 + 装备: 0)
[水井] 农夫请求取水: 5.0 单位，直接提供
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[FarmerTaskManager] 统一状态更新: MOVING -> CARRYING
[FarmerInteractionManager:Farmer] 统一状态更新: MOVING -> CARRYING
[FarmerTimerManager] 统一状态更新: MOVING -> CARRYING
[Farmer] 统一状态变更: MOVING -> CARRYING
[FarmerInteractionManager:Farmer] 成功取水 5.0 单位 (请求: 5.0)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
🎯 [农夫TaskManager] 统一时间获取 - 动作:water, 时间:1.2秒
🕒 [农夫动态超时] 任务类型:6, 预计动作时间:1.2秒, 设置超时:15.0秒
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 6)
[FarmerTaskManager] 统一状态更新: CARRYING -> IDLE
[FarmerInteractionManager:Farmer] 统一状态更新: CARRYING -> IDLE
[FarmerTimerManager] 统一状态更新: CARRYING -> IDLE
[Farmer] 统一状态变更: CARRYING -> IDLE
[WorkerCharacter] 侧面评分 - 左侧: 53.09, 右侧: 95.05, 选择: right
[WorkerCharacter] 智能选择交互侧面: right (目标: Farmland)
[WorkerCharacter] 使用get_interaction_data成功: 位置(580.0, 184.0), 侧面: right
[BaseInteractionManager] 开始交互: water, 目标: Farmland, 距离: 99.0, 目标位置: (580.0, 184.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(580.0, 184.0), 侧面: right
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: IDLE -> MOVING
[FarmerInteractionManager:Farmer] 统一状态更新: IDLE -> MOVING
[FarmerTimerManager] 统一状态更新: IDLE -> MOVING
[Farmer] 统一状态变更: IDLE -> MOVING
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: water, 目标: Farmland
[WorkerCharacter] 动画切换: water -> water (状态: WORKING_3)
[FarmerInteractionManager] 启动动作计时器: water_action, 预期时长: 1.2秒
[FarmerTimerManager] 使用预设动作时间: water_action = 1.2秒
[FarmerTimerManager] 🔧 计时器已启动: water_action, 实际时长: 1.2秒
[BaseInteractionManager] 交互执行结果: true
[FarmerInteractionManager:Farmer] 成功浇水 5.0 单位
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.944，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 2.1秒
[BaseTaskManager] 立即处理新添加的空闲任务
🕒 [农夫动态超时] 任务类型:9, 预计动作时间:5.0秒, 设置超时:20.0秒
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 9)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 2.1秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 2.1秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 2.1秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.267，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (720.237, 181.0069)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
🕒 [农夫动态超时] 任务类型:8, 预计动作时间:5.0秒, 设置超时:20.0秒
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[Crop-turnip] [DEBUG] 作物 '白萝卜' 进入新阶段: growing2
[Crop-potato] [DEBUG] 作物 '土豆' 进入新阶段: mature
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.999，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 2.3秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
🕒 [农夫动态超时] 任务类型:9, 预计动作时间:5.0秒, 设置超时:20.0秒
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 9)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 2.3秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 2.3秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 2.3秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
🎯 [农夫TaskManager] 统一时间获取 - 动作:fetch_water, 时间:2.0秒
🕒 [农夫动态超时] 任务类型:5, 预计动作时间:2.0秒, 设置超时:15.0秒
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 5)
[Farmer] 统一状态动画: collecting
[FarmerTaskManager] 统一状态更新: MOVING -> FETCHING_WATER
[FarmerInteractionManager:Farmer] 统一状态更新: MOVING -> FETCHING_WATER
[FarmerTimerManager] 统一状态更新: MOVING -> FETCHING_WATER
[Farmer] 统一状态变更: MOVING -> FETCHING_WATER
[WorkerCharacter] 侧面评分 - 左侧: 57.77, 右侧: 97.77, 选择: right
[WorkerCharacter] 智能选择交互侧面: right (目标: Well)
[WorkerCharacter] 使用get_interaction_data成功: 位置(676.0, 160.0), 侧面: right
[BaseInteractionManager] 开始交互: fetch_water, 目标: Well, 距离: 44.6, 目标位置: (676.0, 160.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(676.0, 160.0), 侧面: right
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: FETCHING_WATER -> MOVING
[FarmerInteractionManager:Farmer] 统一状态更新: FETCHING_WATER -> MOVING
[FarmerTimerManager] 统一状态更新: FETCHING_WATER -> MOVING
[Farmer] 统一状态变更: FETCHING_WATER -> MOVING
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: fetch_water, 目标: Well
[WorkerCharacter] 动画切换: collecting -> collecting (状态: COLLECTING)
[FarmerInteractionManager] 启动动作计时器: fetch_water_action, 预期时长: 2.0秒
[FarmerTimerManager] 使用预设动作时间: fetch_water_action = 2.0秒
[FarmerTimerManager] 🔧 计时器已启动: fetch_water_action, 实际时长: 2.0秒
[BaseInteractionManager] 交互执行结果: true
[TradingPost] 商队已抵达，可以进行交易
[FarmerInteractionManager:Farmer] 使用装备水桶容量取水: 5.0 (基础: 5.0 + 装备: 0)
[水井] 农夫请求取水: 5.0 单位，直接提供
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[FarmerTaskManager] 统一状态更新: MOVING -> CARRYING
[FarmerInteractionManager:Farmer] 统一状态更新: MOVING -> CARRYING
[FarmerTimerManager] 统一状态更新: MOVING -> CARRYING
[Farmer] 统一状态变更: MOVING -> CARRYING
[FarmerInteractionManager:Farmer] 成功取水 5.0 单位 (请求: 5.0)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
🎯 [农夫TaskManager] 统一时间获取 - 动作:water, 时间:1.2秒
🕒 [农夫动态超时] 任务类型:6, 预计动作时间:1.2秒, 设置超时:15.0秒
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 6)
[FarmerTaskManager] 统一状态更新: CARRYING -> IDLE
[FarmerInteractionManager:Farmer] 统一状态更新: CARRYING -> IDLE
[FarmerTimerManager] 统一状态更新: CARRYING -> IDLE
[Farmer] 统一状态变更: CARRYING -> IDLE
[WorkerCharacter] 侧面评分 - 左侧: 53.09, 右侧: 95.05, 选择: right
[WorkerCharacter] 智能选择交互侧面: right (目标: Farmland)
[WorkerCharacter] 使用get_interaction_data成功: 位置(580.0, 184.0), 侧面: right
[BaseInteractionManager] 开始交互: water, 目标: Farmland, 距离: 99.0, 目标位置: (580.0, 184.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(580.0, 184.0), 侧面: right
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: IDLE -> MOVING
[FarmerInteractionManager:Farmer] 统一状态更新: IDLE -> MOVING
[FarmerTimerManager] 统一状态更新: IDLE -> MOVING
[Farmer] 统一状态变更: IDLE -> MOVING
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[Crop-potato] [DEBUG] 作物 '土豆' 已成熟!
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: water, 目标: Farmland
[FarmerTaskManager] 任务失败 - 类型: 6, 原因: 农田不需要浇水
[BaseInteractionManager] 交互执行结果: false
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 警告：尝试完成空任务
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.825，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 3.1秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
🕒 [农夫动态超时] 任务类型:9, 预计动作时间:5.0秒, 设置超时:20.0秒
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 9)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 3.1秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 3.1秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 3.1秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[Crop-turnip] [DEBUG] 作物 '白萝卜' 进入新阶段: mature
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
🎯 [农夫TaskManager] 统一时间获取 - 动作:harvest, 时间:1.8秒
🕒 [农夫动态超时] 任务类型:1, 预计动作时间:1.8秒, 设置超时:15.0秒
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 1)
农夫: 开始工作流程 [farm_workflow_1753762102.452_847] - 类型: harvest_cycle, 目标: Farmland
农夫: 工作流程阶段更新 [farm_workflow_1753762102.452_847] - harvesting (进度: 0.0%)
[WorkerCharacter] 动画切换: harvest -> harvest (状态: WORKING_1)
[FarmerTaskManager] 统一状态更新: MOVING -> HARVESTING
[FarmerInteractionManager:Farmer] 统一状态更新: MOVING -> HARVESTING
[FarmerTimerManager] 统一状态更新: MOVING -> HARVESTING
[Farmer] 统一状态变更: MOVING -> HARVESTING
[WorkerCharacter] 侧面评分 - 左侧: 58.00, 右侧: 100.00, 选择: right
[WorkerCharacter] 智能选择交互侧面: right (目标: Farmland)
[WorkerCharacter] 使用get_interaction_data成功: 位置(580.0, 184.0), 侧面: right
[BaseInteractionManager] 开始交互: harvest, 目标: Farmland, 距离: 0.0, 目标位置: (580.0, 184.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(580.0, 184.0), 侧面: right
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: HARVESTING -> MOVING
[FarmerInteractionManager:Farmer] 统一状态更新: HARVESTING -> MOVING
[FarmerTimerManager] 统一状态更新: HARVESTING -> MOVING
[Farmer] 统一状态变更: HARVESTING -> MOVING
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: harvest, 目标: Farmland
[WorkerCharacter] 动画切换: harvest -> harvest (状态: WORKING_1)
[FarmerInteractionManager] 启动动作计时器: harvest_action, 预期时长: 1.8秒
[FarmerTimerManager] 使用预设动作时间: harvest_action = 1.8秒
[FarmerTimerManager] 🔧 计时器已启动: harvest_action, 实际时长: 1.8秒
[BaseInteractionManager] 交互执行结果: true
[Crop-potato] [DEBUG] 作物 '土豆' 被收获，产出: potato_item x4
[Crop-potato] [DEBUG] 作物 '土豆' 被移除。玩家操作: false
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
农夫: 工作流程阶段更新 [farm_workflow_1753762102.452_847] - collecting (进度: 33.3%)
农夫: 动作连贯性 - 立即请求收集任务
农夫: _find_nearest_dropped_crop - 找到 1 个掉落物品
农夫: 检查掉落物品 - 类型: potato_item
农夫: 找到可收集作物 - 类型: potato_item, 距离: 21.5
农夫: 选择最近的掉落作物 - 距离: 21.5
🎯 [农夫TaskManager] 统一时间获取 - 动作:collect, 时间:1.5秒
🕒 [农夫动态超时] 任务类型:11, 预计动作时间:1.5秒, 设置超时:15.0秒
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 11)
[WorkerCharacter] 侧面评分 - 左侧: 35.00, 右侧: 75.00, 选择: right
[WorkerCharacter] 智能选择交互侧面: right (目标: DroppedItem_potato_item)
[WorkerCharacter] 使用降级位置: (590.0, 176.0)
[BaseInteractionManager] 开始交互: collect, 目标: DroppedItem_potato_item, 距离: 12.8, 目标位置: (590.0, 176.0)
[WorkerCharacter] 使用降级位置: (590.0, 176.0)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: collect, 目标: DroppedItem_potato_item
🔍 [农夫] 作物类型检查: potato_item -> potato = true
[WorkerCharacter] 动画切换: collecting -> collecting (状态: COLLECTING)
[FarmerInteractionManager] 启动动作计时器: collect_action, 预期时长: 1.5秒
[FarmerTimerManager] 使用预设动作时间: collect_action = 1.5秒
[FarmerTimerManager] 🔧 计时器已启动: collect_action, 实际时长: 1.5秒
[BaseInteractionManager] 交互执行结果: true
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[FarmerTaskManager] 统一状态更新: MOVING -> CARRYING
[FarmerInteractionManager:Farmer] 统一状态更新: MOVING -> CARRYING
[FarmerTimerManager] 统一状态更新: MOVING -> CARRYING
[Farmer] 统一状态变更: MOVING -> CARRYING
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
农夫: 工作流程阶段更新 [farm_workflow_1753762102.452_847] - storing (进度: 66.7%)
农夫: 动作连贯性 - 立即请求存储任务
🎯 [农夫TaskManager] 统一时间获取 - 动作:store, 时间:1.8秒
🕒 [农夫动态超时] 任务类型:4, 预计动作时间:1.8秒, 设置超时:15.0秒
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 4)
[WorkerCharacter] 侧面评分 - 左侧: 51.36, 右侧: 92.13, 选择: right
[WorkerCharacter] 智能选择交互侧面: right (目标: Storage)
[WorkerCharacter] 使用get_interaction_data成功: 位置(440.0, 224.0), 侧面: right
[BaseInteractionManager] 开始交互: store, 目标: Storage, 距离: 157.5, 目标位置: (440.0, 224.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(440.0, 224.0), 侧面: right
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: CARRYING -> MOVING
[FarmerInteractionManager:Farmer] 统一状态更新: CARRYING -> MOVING
[FarmerTimerManager] 统一状态更新: CARRYING -> MOVING
[Farmer] 统一状态变更: CARRYING -> MOVING
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[Crop-turnip] [DEBUG] 作物 '白萝卜' 已成熟!
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: store, 目标: Storage
[WorkerCharacter] 动画切换: storing -> storing (状态: STORING)
[FarmerInteractionManager] 启动动作计时器: store_action, 预期时长: 1.8秒
[FarmerTimerManager] 使用预设动作时间: store_action = 1.8秒
[FarmerTimerManager] 🔧 计时器已启动: store_action, 实际时长: 1.8秒
[BaseInteractionManager] 交互执行结果: true
[厨房-1] [仓库] 存储资源: potato x4 (实际增加: 4)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
农夫: 工作流程完成 [farm_workflow_1753762102.452_847] - 状态: completed, 耗时: 7.5秒
农夫: 动作连贯性 - 完成工作流程循环
[FarmerTaskManager] 统一状态更新: MOVING -> IDLE
[FarmerInteractionManager:Farmer] 统一状态更新: MOVING -> IDLE
[FarmerTimerManager] 统一状态更新: MOVING -> IDLE
[Farmer] 统一状态变更: MOVING -> IDLE
🎯 [农夫TaskManager] 统一时间获取 - 动作:harvest, 时间:1.8秒
🕒 [农夫动态超时] 任务类型:1, 预计动作时间:1.8秒, 设置超时:15.0秒
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 1)
农夫: 开始工作流程 [farm_workflow_1753762110.003_813] - 类型: harvest_cycle, 目标: Farmland
农夫: 工作流程阶段更新 [farm_workflow_1753762110.003_813] - harvesting (进度: 0.0%)
[WorkerCharacter] 动画切换: harvest -> harvest (状态: WORKING_1)
[FarmerTaskManager] 统一状态更新: IDLE -> HARVESTING
[FarmerInteractionManager:Farmer] 统一状态更新: IDLE -> HARVESTING
[FarmerTimerManager] 统一状态更新: IDLE -> HARVESTING
[Farmer] 统一状态变更: IDLE -> HARVESTING
[WorkerCharacter] 侧面评分 - 左侧: 94.98, 右侧: 52.99, 选择: left
[WorkerCharacter] 智能选择交互侧面: left (目标: Farmland)
[WorkerCharacter] 使用get_interaction_data成功: 位置(540.0, 216.0), 侧面: left
[BaseInteractionManager] 开始交互: harvest, 目标: Farmland, 距离: 100.3, 目标位置: (540.0, 216.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(540.0, 216.0), 侧面: left
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: HARVESTING -> MOVING
[FarmerInteractionManager:Farmer] 统一状态更新: HARVESTING -> MOVING
[FarmerTimerManager] 统一状态更新: HARVESTING -> MOVING
[Farmer] 统一状态变更: HARVESTING -> MOVING
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: harvest, 目标: Farmland
[WorkerCharacter] 动画切换: harvest -> harvest (状态: WORKING_1)
[FarmerInteractionManager] 启动动作计时器: harvest_action, 预期时长: 1.8秒
[FarmerTimerManager] 使用预设动作时间: harvest_action = 1.8秒
[FarmerTimerManager] 🔧 计时器已启动: harvest_action, 实际时长: 1.8秒
[BaseInteractionManager] 交互执行结果: true
