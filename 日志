Godot Engine v4.3.stable.official.77dcf97d8 - https://godotengine.org
OpenGL API 3.3.0 NVIDIA 552.44 - Compatibility - Using Device: NVIDIA - NVIDIA GeForce GTX 1060 6GB

[GameLogger] 全局日志级别已设置为: 信息
[GameLogger] 模块 'Crop' 日志级别已设置为: 信息
[GameLogger] 模块 'DataManager' 日志级别已设置为: 信息
[GameLogger] 模块 'FarmerInteraction' 日志级别已设置为: 信息
[信息] [GameManager] 日志系统初始化完成
[DataManager] 配置加载完成: 7/7 成功
ResourceManager: 初始化完成
角色管理器初始化完成
[信息] [GameManager] 渔业数据管理器创建并加载数据成功
[INFO] OreDataManager: 矿物配置加载成功，包含 15 种矿物类型
[信息] [GameManager] 矿业数据管理器创建并加载数据成功
已加载 6 种动物配置
野生动物生成器已启动，间隔: 3000秒
[信息] [GameManager] 管理器初始化完成
[DataManager] 配置加载完成: 7/7 成功
=== 动物外观变体测试器启动 ===
支持动物类型: 牛(C) 羊(S) 猪(P) 马(H) 鸡(K) 鸭(D)
当前动物类型: cow
按F1键查看详细帮助
动物变体测试器控制说明:
数字键1-9: 生成不同变体动物
按键T: 测试所有变体(8种组合)
动物类型切换: C(牛) S(羊) P(猪) H(马) K(鸡) D(鸭)
按键F1: 显示详细帮助
当前动物类型: cow
CharacterEquipmentUI: 管理器引用设置完成
[TramcarBuilding] [INFO] 已将矿车添加到tramcars组
[TradingPost] 已将交易站添加到tradingposts组
[TradingPost] UI交互区域已设置
[TradingPost] 商队定时器已设置，间隔: 1 分钟
[动物围栏--1] UI交互区域已设置
[GridAreaManager] GridAreaManager initialized with TileMap: TerrainTileMap and TileSize: 32
角色管理器: 开始初始化，当前状态:false
角色管理器: 设置世界引用:World
角色管理器: 初始化完成，_world设置为:World
SystemsManager: 开始初始化系统...
SystemsManager: AttributeBoostSystem 已创建并初始化
[TramcarBuilding] [INFO] 矿车已连接到ResourceManager
[TramcarBuilding] [INFO] 矿车支持存储类型: ["stone", "coal", "iron", "copper", "silver", "gold", "platinum", "adamantite", "meteorite", "titanium", "diamond", "emerald", "topaz", "ruby", "sapphire"]
[BlacksmithBuilding] INFO: 成功创建铁匠铺UI
[BlacksmithBuilding] INFO: UI交互区域已设置
[BlacksmithBuilding] INFO: 自动处理计时器已启动
SceneSwitcher: 相机已设置，初始位置: (960, 128)
World: 场景切换器已初始化
角色管理器: 设置世界引用:World
动物系统测试器已启动
Main: 动物系统测试器已添加
AttributeBoostSystem: 已连接EquipmentManager信号
动物系统测试器初始化完成
[信息] [GameManager] 所有子系统初始化完成
[信息] [GameManager] 属性增强系统就绪，装备效果应用系统可用
SystemsManager: 系统初始化完成
[FarmlandBuilding:-1]: Farmland实例已创建，ID: -1
[FarmlandBuilding:-1]: 设置预览状态为: true
OreLifecycleManager: 支持的矿石类型数量: 15
OreLifecycleManager: 包含矿石: ["stone", "coal", "iron", "copper", "silver", "gold", "platinum", "adamantite", "meteorite", "titanium", "diamond", "emerald", "topaz", "ruby", "sapphire"]
OreLifecycleManager: 生成位置计算完成
  - 总可用瓦片: 92
  - 边缘过滤: 0
  - 层级阻挡过滤: 38
  - 最终生成位置: 54
OreLifecycleManager: 初始生成完成，共生成 16 个矿物
[FarmlandBuilding:1]: Farmland实例已创建，ID: 1
[FarmlandBuilding:1]: FarmlandBuilding初始化完成，ID: 1
[FarmlandBuilding:-1]: Farmland实例已创建，ID: -1
[FarmlandBuilding:-1]: 设置预览状态为: true
[FarmlandBuilding:2]: Farmland实例已创建，ID: 2
[FarmlandBuilding:2]: FarmlandBuilding初始化完成，ID: 2
[FarmlandBuilding:-1]: Farmland实例已创建，ID: -1
[FarmlandBuilding:-1]: 设置预览状态为: true
[FarmlandBuilding:3]: Farmland实例已创建，ID: 3
[FarmlandBuilding:3]: FarmlandBuilding初始化完成，ID: 3
[FarmlandBuilding:-1]: Farmland实例已创建，ID: -1
[FarmlandBuilding:-1]: 设置预览状态为: true
[FarmlandBuilding:4]: Farmland实例已创建，ID: 4
[FarmlandBuilding:4]: FarmlandBuilding初始化完成，ID: 4
[未知建筑--1] [仓库] 交互区域已就绪
[厨房-5] [仓库] 交互区域已就绪
(2) [水井] 初始化完成，永远有水供应
CharacterEquipmentUI: 开始在场景中生成角色，类型:farmer位置:(514.1652, 161.7276)
角色管理器: 开始生成角色，类型:0位置:(514.1652, 161.7276)
[CharacterPos] 角色管理器: 预设置角色位置 (514.165161132813, 161.727645874023)
角色管理器：调用 farmer.initialize() for ID 419715622901
[CharacterPos] 角色管理器: 使用底部点偏移设置位置 (514.165161132813, 161.727645874023)
[CharacterManager] 角色生成成功：farmer (ID: 419715622901) at (514.1652, 161.7276)
EquipmentManager: 角色 419715622901 已注册到装备系统
AttributeBoostSystem: 应用移动速度到角色 419715622901: 80.000000 (基础: 80.000000)
WorkerCharacter: 应用移动速度加成 80.000000 -> 80.000000
WorkerCharacter: 应用工作效率加成 1.000000
WorkerCharacter: 应用收获加成 0-0
WorkerCharacter: 应用水桶容量 0
WorkerCharacter: 应用物品容量加成 0
WorkerCharacter: 应用伤害加成 1.000000
WorkerCharacter: 应用木材产量加成范围 0-0
WorkerCharacter: 应用砍伐效率 1.000000
WorkerCharacter: 应用烹饪时间倍数 1.000000
WorkerCharacter: 应用切菜时间倍数 1.000000
WorkerCharacter: 应用食物产量加成范围 0-0
WorkerCharacter: 应用木材产量加成 0-0
WorkerCharacter: 应用钓鱼功能 禁用
Farmer: 收获加成更新为 0-0
Farmer: 水桶容量更新为 0
Farmer: 工作效率更新为 1.000000
Farmer: 收获时间更新为 2.000000 秒 (加速倍数: 1.000000)
Farmer: 种植时间更新为 1.500000 秒 (效率倍数: 1.000000)
Farmer: 根据品质加成 1.000000 调整作物偏好为 wheat
AttributeBoostSystem: 注册角色 419715622901，基础属性: { "movement_speed_multiplier": 1, "harvest_bonus_range": "0-0", "harvest_efficiency_multiplier": 1, "harvest_speed_multiplier": 1, "work_efficiency_multiplier": 1, "damage_multiplier": 1, "cutting_time_multiplier": 1, "cooking_time_multiplier": 1, "water_capacity_bonus": 0, "item_capacity_bonus": 0, "wood_yield_bonus": "0-0", "ore_yield_bonus": "0-0", "wood_yield_multiplier": 1, "wood_yield_bonus_range": "0-0", "chopping_efficiency_multiplier": 1, "food_yield_bonus_range": "0-0", "tool_efficiency_multiplier": 1, "stamina_efficiency_multiplier": 1, "crop_quality_multiplier": 1, "durability_protection_multiplier": 1, "stamina_regeneration_multiplier": 1, "fishing_enabled": false }
AttributeBoostSystem: 应用移动速度到角色 419715622901: 80.000000 (基础: 80.000000)
WorkerCharacter: 应用移动速度加成 80.000000 -> 80.000000
WorkerCharacter: 应用工作效率加成 1.000000
WorkerCharacter: 应用收获加成 0-0
WorkerCharacter: 应用水桶容量 0
WorkerCharacter: 应用物品容量加成 0
WorkerCharacter: 应用伤害加成 1.000000
WorkerCharacter: 应用木材产量加成范围 0-0
WorkerCharacter: 应用砍伐效率 1.000000
WorkerCharacter: 应用烹饪时间倍数 1.000000
WorkerCharacter: 应用切菜时间倍数 1.000000
WorkerCharacter: 应用食物产量加成范围 0-0
WorkerCharacter: 应用木材产量加成 0-0
WorkerCharacter: 应用钓鱼功能 禁用
Farmer: 收获加成更新为 0-0
Farmer: 水桶容量更新为 0
Farmer: 工作效率更新为 1.000000
Farmer: 收获时间更新为 2.000000 秒 (加速倍数: 1.000000)
Farmer: 种植时间更新为 1.500000 秒 (效率倍数: 1.000000)
Farmer: 根据品质加成 1.000000 调整作物偏好为 wheat
AttributeBoostSystem: 更新角色 419715622901 装备效果: {  }
CharacterEquipmentUI: 角色创建成功 - ID:419715622901名称:农夫1类型:farmer
[WorkerCharacter] 启动工作操作，AI启用: true
[WorkerCharacter] 启用任务管理器AI
[WorkerCharacter] 调用TaskManager的_add_initial_task方法
[WorkerCharacter] 启用计时器管理器AI
AttributeBoostSystem: 应用移动速度到角色 419715622901: 80.000000 (基础: 80.000000)
WorkerCharacter: 应用移动速度加成 80.000000 -> 80.000000
WorkerCharacter: 应用工作效率加成 1.000000
WorkerCharacter: 应用收获加成 0-0
WorkerCharacter: 应用水桶容量 0
WorkerCharacter: 应用物品容量加成 0
WorkerCharacter: 应用伤害加成 1.000000
WorkerCharacter: 应用木材产量加成范围 0-0
WorkerCharacter: 应用砍伐效率 1.000000
WorkerCharacter: 应用烹饪时间倍数 1.000000
WorkerCharacter: 应用切菜时间倍数 1.000000
WorkerCharacter: 应用食物产量加成范围 0-0
WorkerCharacter: 应用木材产量加成 0-0
WorkerCharacter: 应用钓鱼功能 禁用
Farmer: 收获加成更新为 0-0
Farmer: 水桶容量更新为 0
Farmer: 工作效率更新为 1.000000
Farmer: 收获时间更新为 2.000000 秒 (加速倍数: 1.000000)
Farmer: 种植时间更新为 1.500000 秒 (效率倍数: 1.000000)
Farmer: 根据品质加成 1.000000 调整作物偏好为 wheat
AttributeBoostSystem: 注册角色 419715622901，基础属性: { "movement_speed_multiplier": 1, "harvest_bonus_range": "0-0", "harvest_efficiency_multiplier": 1, "harvest_speed_multiplier": 1, "work_efficiency_multiplier": 1, "damage_multiplier": 1, "cutting_time_multiplier": 1, "cooking_time_multiplier": 1, "water_capacity_bonus": 0, "item_capacity_bonus": 0, "wood_yield_bonus": "0-0", "ore_yield_bonus": "0-0", "wood_yield_multiplier": 1, "wood_yield_bonus_range": "0-0", "chopping_efficiency_multiplier": 1, "food_yield_bonus_range": "0-0", "tool_efficiency_multiplier": 1, "stamina_efficiency_multiplier": 1, "crop_quality_multiplier": 1, "durability_protection_multiplier": 1, "stamina_regeneration_multiplier": 1, "fishing_enabled": false, "base_move_speed": 80, "work_efficiency": 1, "water_capacity": 0, "item_capacity": 0, "tool_efficiency": 1 }
WorkerCharacter: 已向AttributeBoostSystem注册角色 419715622901
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 0.1秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 0.1秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 0.8秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
CharacterEquipmentUI: 开始在场景中生成角色，类型:farmer位置:(529.7851, 162.529)
角色管理器: 开始生成角色，类型:0位置:(529.7851, 162.529)
[CharacterPos] 角色管理器: 预设置角色位置 (529.785095214844, 162.529022216797)
角色管理器：调用 farmer.initialize() for ID 425252104298
[CharacterPos] 角色管理器: 使用底部点偏移设置位置 (529.785095214844, 162.529022216797)
[CharacterManager] 角色生成成功：farmer (ID: 425252104298) at (529.7851, 162.529)
EquipmentManager: 角色 425252104298 已注册到装备系统
AttributeBoostSystem: 应用移动速度到角色 425252104298: 80.000000 (基础: 80.000000)
WorkerCharacter: 应用移动速度加成 80.000000 -> 80.000000
WorkerCharacter: 应用工作效率加成 1.000000
WorkerCharacter: 应用收获加成 0-0
WorkerCharacter: 应用水桶容量 0
WorkerCharacter: 应用物品容量加成 0
WorkerCharacter: 应用伤害加成 1.000000
WorkerCharacter: 应用木材产量加成范围 0-0
WorkerCharacter: 应用砍伐效率 1.000000
WorkerCharacter: 应用烹饪时间倍数 1.000000
WorkerCharacter: 应用切菜时间倍数 1.000000
WorkerCharacter: 应用食物产量加成范围 0-0
WorkerCharacter: 应用木材产量加成 0-0
WorkerCharacter: 应用钓鱼功能 禁用
Farmer: 收获加成更新为 0-0
Farmer: 水桶容量更新为 0
Farmer: 工作效率更新为 1.000000
Farmer: 收获时间更新为 2.000000 秒 (加速倍数: 1.000000)
Farmer: 种植时间更新为 1.500000 秒 (效率倍数: 1.000000)
Farmer: 根据品质加成 1.000000 调整作物偏好为 wheat
AttributeBoostSystem: 注册角色 425252104298，基础属性: { "movement_speed_multiplier": 1, "harvest_bonus_range": "0-0", "harvest_efficiency_multiplier": 1, "harvest_speed_multiplier": 1, "work_efficiency_multiplier": 1, "damage_multiplier": 1, "cutting_time_multiplier": 1, "cooking_time_multiplier": 1, "water_capacity_bonus": 0, "item_capacity_bonus": 0, "wood_yield_bonus": "0-0", "ore_yield_bonus": "0-0", "wood_yield_multiplier": 1, "wood_yield_bonus_range": "0-0", "chopping_efficiency_multiplier": 1, "food_yield_bonus_range": "0-0", "tool_efficiency_multiplier": 1, "stamina_efficiency_multiplier": 1, "crop_quality_multiplier": 1, "durability_protection_multiplier": 1, "stamina_regeneration_multiplier": 1, "fishing_enabled": false }
AttributeBoostSystem: 应用移动速度到角色 425252104298: 80.000000 (基础: 80.000000)
WorkerCharacter: 应用移动速度加成 80.000000 -> 80.000000
WorkerCharacter: 应用工作效率加成 1.000000
WorkerCharacter: 应用收获加成 0-0
WorkerCharacter: 应用水桶容量 0
WorkerCharacter: 应用物品容量加成 0
WorkerCharacter: 应用伤害加成 1.000000
WorkerCharacter: 应用木材产量加成范围 0-0
WorkerCharacter: 应用砍伐效率 1.000000
WorkerCharacter: 应用烹饪时间倍数 1.000000
WorkerCharacter: 应用切菜时间倍数 1.000000
WorkerCharacter: 应用食物产量加成范围 0-0
WorkerCharacter: 应用木材产量加成 0-0
WorkerCharacter: 应用钓鱼功能 禁用
Farmer: 收获加成更新为 0-0
Farmer: 水桶容量更新为 0
Farmer: 工作效率更新为 1.000000
Farmer: 收获时间更新为 2.000000 秒 (加速倍数: 1.000000)
Farmer: 种植时间更新为 1.500000 秒 (效率倍数: 1.000000)
Farmer: 根据品质加成 1.000000 调整作物偏好为 wheat
AttributeBoostSystem: 更新角色 425252104298 装备效果: {  }
CharacterEquipmentUI: 角色创建成功 - ID:425252104298名称:农夫2类型:farmer
[WorkerCharacter] 启动工作操作，AI启用: true
[WorkerCharacter] 启用任务管理器AI
[WorkerCharacter] 调用TaskManager的_add_initial_task方法
[WorkerCharacter] 启用计时器管理器AI
AttributeBoostSystem: 应用移动速度到角色 425252104298: 80.000000 (基础: 80.000000)
WorkerCharacter: 应用移动速度加成 80.000000 -> 80.000000
WorkerCharacter: 应用工作效率加成 1.000000
WorkerCharacter: 应用收获加成 0-0
WorkerCharacter: 应用水桶容量 0
WorkerCharacter: 应用物品容量加成 0
WorkerCharacter: 应用伤害加成 1.000000
WorkerCharacter: 应用木材产量加成范围 0-0
WorkerCharacter: 应用砍伐效率 1.000000
WorkerCharacter: 应用烹饪时间倍数 1.000000
WorkerCharacter: 应用切菜时间倍数 1.000000
WorkerCharacter: 应用食物产量加成范围 0-0
WorkerCharacter: 应用木材产量加成 0-0
WorkerCharacter: 应用钓鱼功能 禁用
Farmer: 收获加成更新为 0-0
Farmer: 水桶容量更新为 0
Farmer: 工作效率更新为 1.000000
Farmer: 收获时间更新为 2.000000 秒 (加速倍数: 1.000000)
Farmer: 种植时间更新为 1.500000 秒 (效率倍数: 1.000000)
Farmer: 根据品质加成 1.000000 调整作物偏好为 wheat
AttributeBoostSystem: 注册角色 425252104298，基础属性: { "movement_speed_multiplier": 1, "harvest_bonus_range": "0-0", "harvest_efficiency_multiplier": 1, "harvest_speed_multiplier": 1, "work_efficiency_multiplier": 1, "damage_multiplier": 1, "cutting_time_multiplier": 1, "cooking_time_multiplier": 1, "water_capacity_bonus": 0, "item_capacity_bonus": 0, "wood_yield_bonus": "0-0", "ore_yield_bonus": "0-0", "wood_yield_multiplier": 1, "wood_yield_bonus_range": "0-0", "chopping_efficiency_multiplier": 1, "food_yield_bonus_range": "0-0", "tool_efficiency_multiplier": 1, "stamina_efficiency_multiplier": 1, "crop_quality_multiplier": 1, "durability_protection_multiplier": 1, "stamina_regeneration_multiplier": 1, "fishing_enabled": false, "base_move_speed": 80, "work_efficiency": 1, "water_capacity": 0, "item_capacity": 0, "tool_efficiency": 1 }
WorkerCharacter: 已向AttributeBoostSystem注册角色 425252104298
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 0.1秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 0.1秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 0.8秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 2)
[WorkerCharacter] 动画切换: plant -> plant (状态: WORKING_2)
[FarmerTaskManager] 进入种植状态 - 调整任务优先级
[FarmerTaskManager] 统一状态更新: IDLE -> PLANTING
[FarmerInteractionManager] 准备planting交互模式
[FarmerInteractionManager:Farmer] 统一状态更新: IDLE -> PLANTING
[FarmerTimerManager] 统一状态更新: IDLE -> PLANTING
[Farmer] 统一状态变更: IDLE -> PLANTING
[WorkerCharacter] 侧面评分 - 左侧: 85.00, 右侧: 45.00, 选择: left
[WorkerCharacter] 智能选择交互侧面: left (目标: Farmland)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1212.0, 184.0), 侧面: left
[BaseInteractionManager] 开始交互: plant, 目标: Farmland, 距离: 698.2, 目标位置: (1212.0, 184.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1212.0, 184.0), 侧面: left
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: PLANTING -> MOVING
[FarmerInteractionManager:Farmer] 统一状态更新: PLANTING -> MOVING
[FarmerTimerManager] 统一状态更新: PLANTING -> MOVING
[Farmer] 统一状态变更: PLANTING -> MOVING
CharacterEquipmentUI: 开始在场景中生成角色，类型:farmer位置:(544.6549, 163.1822)
角色管理器: 开始生成角色，类型:0位置:(544.6549, 163.1822)
[CharacterPos] 角色管理器: 预设置角色位置 (544.654907226563, 163.182159423828)
角色管理器：调用 farmer.initialize() for ID 432046876709
[CharacterPos] 角色管理器: 使用底部点偏移设置位置 (544.654907226563, 163.182159423828)
[CharacterManager] 角色生成成功：farmer (ID: 432046876709) at (544.6549, 163.1822)
EquipmentManager: 角色 432046876709 已注册到装备系统
AttributeBoostSystem: 应用移动速度到角色 432046876709: 80.000000 (基础: 80.000000)
WorkerCharacter: 应用移动速度加成 80.000000 -> 80.000000
WorkerCharacter: 应用工作效率加成 1.000000
WorkerCharacter: 应用收获加成 0-0
WorkerCharacter: 应用水桶容量 0
WorkerCharacter: 应用物品容量加成 0
WorkerCharacter: 应用伤害加成 1.000000
WorkerCharacter: 应用木材产量加成范围 0-0
WorkerCharacter: 应用砍伐效率 1.000000
WorkerCharacter: 应用烹饪时间倍数 1.000000
WorkerCharacter: 应用切菜时间倍数 1.000000
WorkerCharacter: 应用食物产量加成范围 0-0
WorkerCharacter: 应用木材产量加成 0-0
WorkerCharacter: 应用钓鱼功能 禁用
Farmer: 收获加成更新为 0-0
Farmer: 水桶容量更新为 0
Farmer: 工作效率更新为 1.000000
Farmer: 收获时间更新为 2.000000 秒 (加速倍数: 1.000000)
Farmer: 种植时间更新为 1.500000 秒 (效率倍数: 1.000000)
Farmer: 根据品质加成 1.000000 调整作物偏好为 wheat
AttributeBoostSystem: 注册角色 432046876709，基础属性: { "movement_speed_multiplier": 1, "harvest_bonus_range": "0-0", "harvest_efficiency_multiplier": 1, "harvest_speed_multiplier": 1, "work_efficiency_multiplier": 1, "damage_multiplier": 1, "cutting_time_multiplier": 1, "cooking_time_multiplier": 1, "water_capacity_bonus": 0, "item_capacity_bonus": 0, "wood_yield_bonus": "0-0", "ore_yield_bonus": "0-0", "wood_yield_multiplier": 1, "wood_yield_bonus_range": "0-0", "chopping_efficiency_multiplier": 1, "food_yield_bonus_range": "0-0", "tool_efficiency_multiplier": 1, "stamina_efficiency_multiplier": 1, "crop_quality_multiplier": 1, "durability_protection_multiplier": 1, "stamina_regeneration_multiplier": 1, "fishing_enabled": false }
AttributeBoostSystem: 应用移动速度到角色 432046876709: 80.000000 (基础: 80.000000)
WorkerCharacter: 应用移动速度加成 80.000000 -> 80.000000
WorkerCharacter: 应用工作效率加成 1.000000
WorkerCharacter: 应用收获加成 0-0
WorkerCharacter: 应用水桶容量 0
WorkerCharacter: 应用物品容量加成 0
WorkerCharacter: 应用伤害加成 1.000000
WorkerCharacter: 应用木材产量加成范围 0-0
WorkerCharacter: 应用砍伐效率 1.000000
WorkerCharacter: 应用烹饪时间倍数 1.000000
WorkerCharacter: 应用切菜时间倍数 1.000000
WorkerCharacter: 应用食物产量加成范围 0-0
WorkerCharacter: 应用木材产量加成 0-0
WorkerCharacter: 应用钓鱼功能 禁用
Farmer: 收获加成更新为 0-0
Farmer: 水桶容量更新为 0
Farmer: 工作效率更新为 1.000000
Farmer: 收获时间更新为 2.000000 秒 (加速倍数: 1.000000)
Farmer: 种植时间更新为 1.500000 秒 (效率倍数: 1.000000)
Farmer: 根据品质加成 1.000000 调整作物偏好为 wheat
AttributeBoostSystem: 更新角色 432046876709 装备效果: {  }
CharacterEquipmentUI: 角色创建成功 - ID:432046876709名称:农夫3类型:farmer
[WorkerCharacter] 启动工作操作，AI启用: true
[WorkerCharacter] 启用任务管理器AI
[WorkerCharacter] 调用TaskManager的_add_initial_task方法
[WorkerCharacter] 启用计时器管理器AI
AttributeBoostSystem: 应用移动速度到角色 432046876709: 80.000000 (基础: 80.000000)
WorkerCharacter: 应用移动速度加成 80.000000 -> 80.000000
WorkerCharacter: 应用工作效率加成 1.000000
WorkerCharacter: 应用收获加成 0-0
WorkerCharacter: 应用水桶容量 0
WorkerCharacter: 应用物品容量加成 0
WorkerCharacter: 应用伤害加成 1.000000
WorkerCharacter: 应用木材产量加成范围 0-0
WorkerCharacter: 应用砍伐效率 1.000000
WorkerCharacter: 应用烹饪时间倍数 1.000000
WorkerCharacter: 应用切菜时间倍数 1.000000
WorkerCharacter: 应用食物产量加成范围 0-0
WorkerCharacter: 应用木材产量加成 0-0
WorkerCharacter: 应用钓鱼功能 禁用
Farmer: 收获加成更新为 0-0
Farmer: 水桶容量更新为 0
Farmer: 工作效率更新为 1.000000
Farmer: 收获时间更新为 2.000000 秒 (加速倍数: 1.000000)
Farmer: 种植时间更新为 1.500000 秒 (效率倍数: 1.000000)
Farmer: 根据品质加成 1.000000 调整作物偏好为 wheat
AttributeBoostSystem: 注册角色 432046876709，基础属性: { "movement_speed_multiplier": 1, "harvest_bonus_range": "0-0", "harvest_efficiency_multiplier": 1, "harvest_speed_multiplier": 1, "work_efficiency_multiplier": 1, "damage_multiplier": 1, "cutting_time_multiplier": 1, "cooking_time_multiplier": 1, "water_capacity_bonus": 0, "item_capacity_bonus": 0, "wood_yield_bonus": "0-0", "ore_yield_bonus": "0-0", "wood_yield_multiplier": 1, "wood_yield_bonus_range": "0-0", "chopping_efficiency_multiplier": 1, "food_yield_bonus_range": "0-0", "tool_efficiency_multiplier": 1, "stamina_efficiency_multiplier": 1, "crop_quality_multiplier": 1, "durability_protection_multiplier": 1, "stamina_regeneration_multiplier": 1, "fishing_enabled": false, "base_move_speed": 80, "work_efficiency": 1, "water_capacity": 0, "item_capacity": 0, "tool_efficiency": 1 }
WorkerCharacter: 已向AttributeBoostSystem注册角色 432046876709
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 0.1秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 0.1秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 0.8秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 2)
[WorkerCharacter] 动画切换: plant -> plant (状态: WORKING_2)
[FarmerTaskManager] 进入种植状态 - 调整任务优先级
[FarmerTaskManager] 统一状态更新: IDLE -> PLANTING
[FarmerInteractionManager] 准备planting交互模式
[FarmerInteractionManager:@Node2D@597] 统一状态更新: IDLE -> PLANTING
[FarmerTimerManager] 统一状态更新: IDLE -> PLANTING
[Farmer] 统一状态变更: IDLE -> PLANTING
[WorkerCharacter] 侧面评分 - 左侧: 85.00, 右侧: 45.00, 选择: left
[WorkerCharacter] 智能选择交互侧面: left (目标: Farmland)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1244.0, 184.0), 侧面: left
[BaseInteractionManager] 开始交互: plant, 目标: Farmland, 距离: 714.5, 目标位置: (1244.0, 184.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1244.0, 184.0), 侧面: left
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: PLANTING -> MOVING
[FarmerInteractionManager:@Node2D@597] 统一状态更新: PLANTING -> MOVING
[FarmerTimerManager] 统一状态更新: PLANTING -> MOVING
[Farmer] 统一状态变更: PLANTING -> MOVING
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 2)
[WorkerCharacter] 动画切换: plant -> plant (状态: WORKING_2)
[FarmerTaskManager] 进入种植状态 - 调整任务优先级
[FarmerTaskManager] 统一状态更新: IDLE -> PLANTING
[FarmerInteractionManager] 准备planting交互模式
[FarmerInteractionManager:@Node2D@604] 统一状态更新: IDLE -> PLANTING
[FarmerTimerManager] 统一状态更新: IDLE -> PLANTING
[Farmer] 统一状态变更: IDLE -> PLANTING
[WorkerCharacter] 侧面评分 - 左侧: 85.00, 右侧: 45.00, 选择: left
[WorkerCharacter] 智能选择交互侧面: left (目标: Farmland)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1212.0, 216.0), 侧面: left
[BaseInteractionManager] 开始交互: plant, 目标: Farmland, 距离: 669.4, 目标位置: (1212.0, 216.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1212.0, 216.0), 侧面: left
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: PLANTING -> MOVING
[FarmerInteractionManager:@Node2D@604] 统一状态更新: PLANTING -> MOVING
[FarmerTimerManager] 统一状态更新: PLANTING -> MOVING
[Farmer] 统一状态变更: PLANTING -> MOVING
CharacterEquipmentUI: 开始在场景中生成角色，类型:farmer位置:(531.3827, 147.7701)
角色管理器: 开始生成角色，类型:0位置:(531.3827, 147.7701)
[CharacterPos] 角色管理器: 预设置角色位置 (531.382690429688, 147.770065307617)
角色管理器：调用 farmer.initialize() for ID 443958700315
[CharacterPos] 角色管理器: 使用底部点偏移设置位置 (531.382690429688, 147.770065307617)
[CharacterManager] 角色生成成功：farmer (ID: 443958700315) at (531.3827, 147.7701)
EquipmentManager: 角色 443958700315 已注册到装备系统
AttributeBoostSystem: 应用移动速度到角色 443958700315: 80.000000 (基础: 80.000000)
WorkerCharacter: 应用移动速度加成 80.000000 -> 80.000000
WorkerCharacter: 应用工作效率加成 1.000000
WorkerCharacter: 应用收获加成 0-0
WorkerCharacter: 应用水桶容量 0
WorkerCharacter: 应用物品容量加成 0
WorkerCharacter: 应用伤害加成 1.000000
WorkerCharacter: 应用木材产量加成范围 0-0
WorkerCharacter: 应用砍伐效率 1.000000
WorkerCharacter: 应用烹饪时间倍数 1.000000
WorkerCharacter: 应用切菜时间倍数 1.000000
WorkerCharacter: 应用食物产量加成范围 0-0
WorkerCharacter: 应用木材产量加成 0-0
WorkerCharacter: 应用钓鱼功能 禁用
Farmer: 收获加成更新为 0-0
Farmer: 水桶容量更新为 0
Farmer: 工作效率更新为 1.000000
Farmer: 收获时间更新为 2.000000 秒 (加速倍数: 1.000000)
Farmer: 种植时间更新为 1.500000 秒 (效率倍数: 1.000000)
Farmer: 根据品质加成 1.000000 调整作物偏好为 wheat
AttributeBoostSystem: 注册角色 443958700315，基础属性: { "movement_speed_multiplier": 1, "harvest_bonus_range": "0-0", "harvest_efficiency_multiplier": 1, "harvest_speed_multiplier": 1, "work_efficiency_multiplier": 1, "damage_multiplier": 1, "cutting_time_multiplier": 1, "cooking_time_multiplier": 1, "water_capacity_bonus": 0, "item_capacity_bonus": 0, "wood_yield_bonus": "0-0", "ore_yield_bonus": "0-0", "wood_yield_multiplier": 1, "wood_yield_bonus_range": "0-0", "chopping_efficiency_multiplier": 1, "food_yield_bonus_range": "0-0", "tool_efficiency_multiplier": 1, "stamina_efficiency_multiplier": 1, "crop_quality_multiplier": 1, "durability_protection_multiplier": 1, "stamina_regeneration_multiplier": 1, "fishing_enabled": false }
AttributeBoostSystem: 应用移动速度到角色 443958700315: 80.000000 (基础: 80.000000)
WorkerCharacter: 应用移动速度加成 80.000000 -> 80.000000
WorkerCharacter: 应用工作效率加成 1.000000
WorkerCharacter: 应用收获加成 0-0
WorkerCharacter: 应用水桶容量 0
WorkerCharacter: 应用物品容量加成 0
WorkerCharacter: 应用伤害加成 1.000000
WorkerCharacter: 应用木材产量加成范围 0-0
WorkerCharacter: 应用砍伐效率 1.000000
WorkerCharacter: 应用烹饪时间倍数 1.000000
WorkerCharacter: 应用切菜时间倍数 1.000000
WorkerCharacter: 应用食物产量加成范围 0-0
WorkerCharacter: 应用木材产量加成 0-0
WorkerCharacter: 应用钓鱼功能 禁用
Farmer: 收获加成更新为 0-0
Farmer: 水桶容量更新为 0
Farmer: 工作效率更新为 1.000000
Farmer: 收获时间更新为 2.000000 秒 (加速倍数: 1.000000)
Farmer: 种植时间更新为 1.500000 秒 (效率倍数: 1.000000)
Farmer: 根据品质加成 1.000000 调整作物偏好为 wheat
AttributeBoostSystem: 更新角色 443958700315 装备效果: {  }
CharacterEquipmentUI: 角色创建成功 - ID:443958700315名称:农夫4类型:farmer
[WorkerCharacter] 启动工作操作，AI启用: true
[WorkerCharacter] 启用任务管理器AI
[WorkerCharacter] 调用TaskManager的_add_initial_task方法
[WorkerCharacter] 启用计时器管理器AI
AttributeBoostSystem: 应用移动速度到角色 443958700315: 80.000000 (基础: 80.000000)
WorkerCharacter: 应用移动速度加成 80.000000 -> 80.000000
WorkerCharacter: 应用工作效率加成 1.000000
WorkerCharacter: 应用收获加成 0-0
WorkerCharacter: 应用水桶容量 0
WorkerCharacter: 应用物品容量加成 0
WorkerCharacter: 应用伤害加成 1.000000
WorkerCharacter: 应用木材产量加成范围 0-0
WorkerCharacter: 应用砍伐效率 1.000000
WorkerCharacter: 应用烹饪时间倍数 1.000000
WorkerCharacter: 应用切菜时间倍数 1.000000
WorkerCharacter: 应用食物产量加成范围 0-0
WorkerCharacter: 应用木材产量加成 0-0
WorkerCharacter: 应用钓鱼功能 禁用
Farmer: 收获加成更新为 0-0
Farmer: 水桶容量更新为 0
Farmer: 工作效率更新为 1.000000
Farmer: 收获时间更新为 2.000000 秒 (加速倍数: 1.000000)
Farmer: 种植时间更新为 1.500000 秒 (效率倍数: 1.000000)
Farmer: 根据品质加成 1.000000 调整作物偏好为 wheat
AttributeBoostSystem: 注册角色 443958700315，基础属性: { "movement_speed_multiplier": 1, "harvest_bonus_range": "0-0", "harvest_efficiency_multiplier": 1, "harvest_speed_multiplier": 1, "work_efficiency_multiplier": 1, "damage_multiplier": 1, "cutting_time_multiplier": 1, "cooking_time_multiplier": 1, "water_capacity_bonus": 0, "item_capacity_bonus": 0, "wood_yield_bonus": "0-0", "ore_yield_bonus": "0-0", "wood_yield_multiplier": 1, "wood_yield_bonus_range": "0-0", "chopping_efficiency_multiplier": 1, "food_yield_bonus_range": "0-0", "tool_efficiency_multiplier": 1, "stamina_efficiency_multiplier": 1, "crop_quality_multiplier": 1, "durability_protection_multiplier": 1, "stamina_regeneration_multiplier": 1, "fishing_enabled": false, "base_move_speed": 80, "work_efficiency": 1, "water_capacity": 0, "item_capacity": 0, "tool_efficiency": 1 }
WorkerCharacter: 已向AttributeBoostSystem注册角色 443958700315
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 0.1秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 0.1秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 0.8秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
CharacterEquipmentUI: 开始在场景中生成角色，类型:farmer位置:(510.1024, 147.0854)
角色管理器: 开始生成角色，类型:0位置:(510.1024, 147.0854)
[CharacterPos] 角色管理器: 预设置角色位置 (510.102386474609, 147.085357666016)
角色管理器：调用 farmer.initialize() for ID 456088627479
[CharacterPos] 角色管理器: 使用底部点偏移设置位置 (510.102386474609, 147.085357666016)
[CharacterManager] 角色生成成功：farmer (ID: 456088627479) at (510.1024, 147.0854)
EquipmentManager: 角色 456088627479 已注册到装备系统
AttributeBoostSystem: 应用移动速度到角色 456088627479: 80.000000 (基础: 80.000000)
WorkerCharacter: 应用移动速度加成 80.000000 -> 80.000000
WorkerCharacter: 应用工作效率加成 1.000000
WorkerCharacter: 应用收获加成 0-0
WorkerCharacter: 应用水桶容量 0
WorkerCharacter: 应用物品容量加成 0
WorkerCharacter: 应用伤害加成 1.000000
WorkerCharacter: 应用木材产量加成范围 0-0
WorkerCharacter: 应用砍伐效率 1.000000
WorkerCharacter: 应用烹饪时间倍数 1.000000
WorkerCharacter: 应用切菜时间倍数 1.000000
WorkerCharacter: 应用食物产量加成范围 0-0
WorkerCharacter: 应用木材产量加成 0-0
WorkerCharacter: 应用钓鱼功能 禁用
Farmer: 收获加成更新为 0-0
Farmer: 水桶容量更新为 0
Farmer: 工作效率更新为 1.000000
Farmer: 收获时间更新为 2.000000 秒 (加速倍数: 1.000000)
Farmer: 种植时间更新为 1.500000 秒 (效率倍数: 1.000000)
Farmer: 根据品质加成 1.000000 调整作物偏好为 wheat
AttributeBoostSystem: 注册角色 456088627479，基础属性: { "movement_speed_multiplier": 1, "harvest_bonus_range": "0-0", "harvest_efficiency_multiplier": 1, "harvest_speed_multiplier": 1, "work_efficiency_multiplier": 1, "damage_multiplier": 1, "cutting_time_multiplier": 1, "cooking_time_multiplier": 1, "water_capacity_bonus": 0, "item_capacity_bonus": 0, "wood_yield_bonus": "0-0", "ore_yield_bonus": "0-0", "wood_yield_multiplier": 1, "wood_yield_bonus_range": "0-0", "chopping_efficiency_multiplier": 1, "food_yield_bonus_range": "0-0", "tool_efficiency_multiplier": 1, "stamina_efficiency_multiplier": 1, "crop_quality_multiplier": 1, "durability_protection_multiplier": 1, "stamina_regeneration_multiplier": 1, "fishing_enabled": false }
AttributeBoostSystem: 应用移动速度到角色 456088627479: 80.000000 (基础: 80.000000)
WorkerCharacter: 应用移动速度加成 80.000000 -> 80.000000
WorkerCharacter: 应用工作效率加成 1.000000
WorkerCharacter: 应用收获加成 0-0
WorkerCharacter: 应用水桶容量 0
WorkerCharacter: 应用物品容量加成 0
WorkerCharacter: 应用伤害加成 1.000000
WorkerCharacter: 应用木材产量加成范围 0-0
WorkerCharacter: 应用砍伐效率 1.000000
WorkerCharacter: 应用烹饪时间倍数 1.000000
WorkerCharacter: 应用切菜时间倍数 1.000000
WorkerCharacter: 应用食物产量加成范围 0-0
WorkerCharacter: 应用木材产量加成 0-0
WorkerCharacter: 应用钓鱼功能 禁用
Farmer: 收获加成更新为 0-0
Farmer: 水桶容量更新为 0
Farmer: 工作效率更新为 1.000000
Farmer: 收获时间更新为 2.000000 秒 (加速倍数: 1.000000)
Farmer: 种植时间更新为 1.500000 秒 (效率倍数: 1.000000)
Farmer: 根据品质加成 1.000000 调整作物偏好为 wheat
AttributeBoostSystem: 更新角色 456088627479 装备效果: {  }
CharacterEquipmentUI: 角色创建成功 - ID:456088627479名称:农夫5类型:farmer
[WorkerCharacter] 启动工作操作，AI启用: true
[WorkerCharacter] 启用任务管理器AI
[WorkerCharacter] 调用TaskManager的_add_initial_task方法
[WorkerCharacter] 启用计时器管理器AI
AttributeBoostSystem: 应用移动速度到角色 456088627479: 80.000000 (基础: 80.000000)
WorkerCharacter: 应用移动速度加成 80.000000 -> 80.000000
WorkerCharacter: 应用工作效率加成 1.000000
WorkerCharacter: 应用收获加成 0-0
WorkerCharacter: 应用水桶容量 0
WorkerCharacter: 应用物品容量加成 0
WorkerCharacter: 应用伤害加成 1.000000
WorkerCharacter: 应用木材产量加成范围 0-0
WorkerCharacter: 应用砍伐效率 1.000000
WorkerCharacter: 应用烹饪时间倍数 1.000000
WorkerCharacter: 应用切菜时间倍数 1.000000
WorkerCharacter: 应用食物产量加成范围 0-0
WorkerCharacter: 应用木材产量加成 0-0
WorkerCharacter: 应用钓鱼功能 禁用
Farmer: 收获加成更新为 0-0
Farmer: 水桶容量更新为 0
Farmer: 工作效率更新为 1.000000
Farmer: 收获时间更新为 2.000000 秒 (加速倍数: 1.000000)
Farmer: 种植时间更新为 1.500000 秒 (效率倍数: 1.000000)
Farmer: 根据品质加成 1.000000 调整作物偏好为 wheat
AttributeBoostSystem: 注册角色 456088627479，基础属性: { "movement_speed_multiplier": 1, "harvest_bonus_range": "0-0", "harvest_efficiency_multiplier": 1, "harvest_speed_multiplier": 1, "work_efficiency_multiplier": 1, "damage_multiplier": 1, "cutting_time_multiplier": 1, "cooking_time_multiplier": 1, "water_capacity_bonus": 0, "item_capacity_bonus": 0, "wood_yield_bonus": "0-0", "ore_yield_bonus": "0-0", "wood_yield_multiplier": 1, "wood_yield_bonus_range": "0-0", "chopping_efficiency_multiplier": 1, "food_yield_bonus_range": "0-0", "tool_efficiency_multiplier": 1, "stamina_efficiency_multiplier": 1, "crop_quality_multiplier": 1, "durability_protection_multiplier": 1, "stamina_regeneration_multiplier": 1, "fishing_enabled": false, "base_move_speed": 80, "work_efficiency": 1, "water_capacity": 0, "item_capacity": 0, "tool_efficiency": 1 }
WorkerCharacter: 已向AttributeBoostSystem注册角色 456088627479
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 0.1秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 0.1秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 0.8秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 2)
[WorkerCharacter] 动画切换: plant -> plant (状态: WORKING_2)
[FarmerTaskManager] 进入种植状态 - 调整任务优先级
[FarmerTaskManager] 统一状态更新: IDLE -> PLANTING
[FarmerInteractionManager] 准备planting交互模式
[FarmerInteractionManager:@Node2D@612] 统一状态更新: IDLE -> PLANTING
[FarmerTimerManager] 统一状态更新: IDLE -> PLANTING
[Farmer] 统一状态变更: IDLE -> PLANTING
[WorkerCharacter] 侧面评分 - 左侧: 85.00, 右侧: 45.00, 选择: left
[WorkerCharacter] 智能选择交互侧面: left (目标: Farmland)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1244.0, 216.0), 侧面: left
[BaseInteractionManager] 开始交互: plant, 目标: Farmland, 距离: 715.9, 目标位置: (1244.0, 216.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1244.0, 216.0), 侧面: left
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: PLANTING -> MOVING
[FarmerInteractionManager:@Node2D@612] 统一状态更新: PLANTING -> MOVING
[FarmerTimerManager] 统一状态更新: PLANTING -> MOVING
[Farmer] 统一状态变更: PLANTING -> MOVING
CharacterEquipmentUI: 开始在场景中生成角色，类型:farmer位置:(525.6131, 162.777)
角色管理器: 开始生成角色，类型:0位置:(525.6131, 162.777)
[CharacterPos] 角色管理器: 预设置角色位置 (525.613098144531, 162.77702331543)
角色管理器：调用 farmer.initialize() for ID 469862721817
[CharacterPos] 角色管理器: 使用底部点偏移设置位置 (525.613098144531, 162.77702331543)
[CharacterManager] 角色生成成功：farmer (ID: 469862721817) at (525.6131, 162.777)
EquipmentManager: 角色 469862721817 已注册到装备系统
AttributeBoostSystem: 应用移动速度到角色 469862721817: 80.000000 (基础: 80.000000)
WorkerCharacter: 应用移动速度加成 80.000000 -> 80.000000
WorkerCharacter: 应用工作效率加成 1.000000
WorkerCharacter: 应用收获加成 0-0
WorkerCharacter: 应用水桶容量 0
WorkerCharacter: 应用物品容量加成 0
WorkerCharacter: 应用伤害加成 1.000000
WorkerCharacter: 应用木材产量加成范围 0-0
WorkerCharacter: 应用砍伐效率 1.000000
WorkerCharacter: 应用烹饪时间倍数 1.000000
WorkerCharacter: 应用切菜时间倍数 1.000000
WorkerCharacter: 应用食物产量加成范围 0-0
WorkerCharacter: 应用木材产量加成 0-0
WorkerCharacter: 应用钓鱼功能 禁用
Farmer: 收获加成更新为 0-0
Farmer: 水桶容量更新为 0
Farmer: 工作效率更新为 1.000000
Farmer: 收获时间更新为 2.000000 秒 (加速倍数: 1.000000)
Farmer: 种植时间更新为 1.500000 秒 (效率倍数: 1.000000)
Farmer: 根据品质加成 1.000000 调整作物偏好为 wheat
AttributeBoostSystem: 注册角色 469862721817，基础属性: { "movement_speed_multiplier": 1, "harvest_bonus_range": "0-0", "harvest_efficiency_multiplier": 1, "harvest_speed_multiplier": 1, "work_efficiency_multiplier": 1, "damage_multiplier": 1, "cutting_time_multiplier": 1, "cooking_time_multiplier": 1, "water_capacity_bonus": 0, "item_capacity_bonus": 0, "wood_yield_bonus": "0-0", "ore_yield_bonus": "0-0", "wood_yield_multiplier": 1, "wood_yield_bonus_range": "0-0", "chopping_efficiency_multiplier": 1, "food_yield_bonus_range": "0-0", "tool_efficiency_multiplier": 1, "stamina_efficiency_multiplier": 1, "crop_quality_multiplier": 1, "durability_protection_multiplier": 1, "stamina_regeneration_multiplier": 1, "fishing_enabled": false }
AttributeBoostSystem: 应用移动速度到角色 469862721817: 80.000000 (基础: 80.000000)
WorkerCharacter: 应用移动速度加成 80.000000 -> 80.000000
WorkerCharacter: 应用工作效率加成 1.000000
WorkerCharacter: 应用收获加成 0-0
WorkerCharacter: 应用水桶容量 0
WorkerCharacter: 应用物品容量加成 0
WorkerCharacter: 应用伤害加成 1.000000
WorkerCharacter: 应用木材产量加成范围 0-0
WorkerCharacter: 应用砍伐效率 1.000000
WorkerCharacter: 应用烹饪时间倍数 1.000000
WorkerCharacter: 应用切菜时间倍数 1.000000
WorkerCharacter: 应用食物产量加成范围 0-0
WorkerCharacter: 应用木材产量加成 0-0
WorkerCharacter: 应用钓鱼功能 禁用
Farmer: 收获加成更新为 0-0
Farmer: 水桶容量更新为 0
Farmer: 工作效率更新为 1.000000
Farmer: 收获时间更新为 2.000000 秒 (加速倍数: 1.000000)
Farmer: 种植时间更新为 1.500000 秒 (效率倍数: 1.000000)
Farmer: 根据品质加成 1.000000 调整作物偏好为 wheat
AttributeBoostSystem: 更新角色 469862721817 装备效果: {  }
CharacterEquipmentUI: 角色创建成功 - ID:469862721817名称:农夫6类型:farmer
[WorkerCharacter] 启动工作操作，AI启用: true
[WorkerCharacter] 启用任务管理器AI
[WorkerCharacter] 调用TaskManager的_add_initial_task方法
[WorkerCharacter] 启用计时器管理器AI
AttributeBoostSystem: 应用移动速度到角色 469862721817: 80.000000 (基础: 80.000000)
WorkerCharacter: 应用移动速度加成 80.000000 -> 80.000000
WorkerCharacter: 应用工作效率加成 1.000000
WorkerCharacter: 应用收获加成 0-0
WorkerCharacter: 应用水桶容量 0
WorkerCharacter: 应用物品容量加成 0
WorkerCharacter: 应用伤害加成 1.000000
WorkerCharacter: 应用木材产量加成范围 0-0
WorkerCharacter: 应用砍伐效率 1.000000
WorkerCharacter: 应用烹饪时间倍数 1.000000
WorkerCharacter: 应用切菜时间倍数 1.000000
WorkerCharacter: 应用食物产量加成范围 0-0
WorkerCharacter: 应用木材产量加成 0-0
WorkerCharacter: 应用钓鱼功能 禁用
Farmer: 收获加成更新为 0-0
Farmer: 水桶容量更新为 0
Farmer: 工作效率更新为 1.000000
Farmer: 收获时间更新为 2.000000 秒 (加速倍数: 1.000000)
Farmer: 种植时间更新为 1.500000 秒 (效率倍数: 1.000000)
Farmer: 根据品质加成 1.000000 调整作物偏好为 wheat
AttributeBoostSystem: 注册角色 469862721817，基础属性: { "movement_speed_multiplier": 1, "harvest_bonus_range": "0-0", "harvest_efficiency_multiplier": 1, "harvest_speed_multiplier": 1, "work_efficiency_multiplier": 1, "damage_multiplier": 1, "cutting_time_multiplier": 1, "cooking_time_multiplier": 1, "water_capacity_bonus": 0, "item_capacity_bonus": 0, "wood_yield_bonus": "0-0", "ore_yield_bonus": "0-0", "wood_yield_multiplier": 1, "wood_yield_bonus_range": "0-0", "chopping_efficiency_multiplier": 1, "food_yield_bonus_range": "0-0", "tool_efficiency_multiplier": 1, "stamina_efficiency_multiplier": 1, "crop_quality_multiplier": 1, "durability_protection_multiplier": 1, "stamina_regeneration_multiplier": 1, "fishing_enabled": false, "base_move_speed": 80, "work_efficiency": 1, "water_capacity": 0, "item_capacity": 0, "tool_efficiency": 1 }
WorkerCharacter: 已向AttributeBoostSystem注册角色 469862721817
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 0.1秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 0.1秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 0.8秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.007，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (438.3135, 89.62406)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: IDLE -> MOVING
[FarmerInteractionManager:@Node2D@621] 统一状态更新: IDLE -> MOVING
[FarmerTimerManager] 统一状态更新: IDLE -> MOVING
[Farmer] 统一状态变更: IDLE -> MOVING
CharacterEquipmentUI: 开始在场景中生成角色，类型:farmer位置:(502.4645, 139.447)
角色管理器: 开始生成角色，类型:0位置:(502.4645, 139.447)
[CharacterPos] 角色管理器: 预设置角色位置 (502.464477539063, 139.447036743164)
角色管理器：调用 farmer.initialize() for ID 484593118103
[CharacterPos] 角色管理器: 使用底部点偏移设置位置 (502.464477539063, 139.447036743164)
[CharacterManager] 角色生成成功：farmer (ID: 484593118103) at (502.4645, 139.447)
EquipmentManager: 角色 484593118103 已注册到装备系统
AttributeBoostSystem: 应用移动速度到角色 484593118103: 80.000000 (基础: 80.000000)
WorkerCharacter: 应用移动速度加成 80.000000 -> 80.000000
WorkerCharacter: 应用工作效率加成 1.000000
WorkerCharacter: 应用收获加成 0-0
WorkerCharacter: 应用水桶容量 0
WorkerCharacter: 应用物品容量加成 0
WorkerCharacter: 应用伤害加成 1.000000
WorkerCharacter: 应用木材产量加成范围 0-0
WorkerCharacter: 应用砍伐效率 1.000000
WorkerCharacter: 应用烹饪时间倍数 1.000000
WorkerCharacter: 应用切菜时间倍数 1.000000
WorkerCharacter: 应用食物产量加成范围 0-0
WorkerCharacter: 应用木材产量加成 0-0
WorkerCharacter: 应用钓鱼功能 禁用
Farmer: 收获加成更新为 0-0
Farmer: 水桶容量更新为 0
Farmer: 工作效率更新为 1.000000
Farmer: 收获时间更新为 2.000000 秒 (加速倍数: 1.000000)
Farmer: 种植时间更新为 1.500000 秒 (效率倍数: 1.000000)
Farmer: 根据品质加成 1.000000 调整作物偏好为 wheat
AttributeBoostSystem: 注册角色 484593118103，基础属性: { "movement_speed_multiplier": 1, "harvest_bonus_range": "0-0", "harvest_efficiency_multiplier": 1, "harvest_speed_multiplier": 1, "work_efficiency_multiplier": 1, "damage_multiplier": 1, "cutting_time_multiplier": 1, "cooking_time_multiplier": 1, "water_capacity_bonus": 0, "item_capacity_bonus": 0, "wood_yield_bonus": "0-0", "ore_yield_bonus": "0-0", "wood_yield_multiplier": 1, "wood_yield_bonus_range": "0-0", "chopping_efficiency_multiplier": 1, "food_yield_bonus_range": "0-0", "tool_efficiency_multiplier": 1, "stamina_efficiency_multiplier": 1, "crop_quality_multiplier": 1, "durability_protection_multiplier": 1, "stamina_regeneration_multiplier": 1, "fishing_enabled": false }
AttributeBoostSystem: 应用移动速度到角色 484593118103: 80.000000 (基础: 80.000000)
WorkerCharacter: 应用移动速度加成 80.000000 -> 80.000000
WorkerCharacter: 应用工作效率加成 1.000000
WorkerCharacter: 应用收获加成 0-0
WorkerCharacter: 应用水桶容量 0
WorkerCharacter: 应用物品容量加成 0
WorkerCharacter: 应用伤害加成 1.000000
WorkerCharacter: 应用木材产量加成范围 0-0
WorkerCharacter: 应用砍伐效率 1.000000
WorkerCharacter: 应用烹饪时间倍数 1.000000
WorkerCharacter: 应用切菜时间倍数 1.000000
WorkerCharacter: 应用食物产量加成范围 0-0
WorkerCharacter: 应用木材产量加成 0-0
WorkerCharacter: 应用钓鱼功能 禁用
Farmer: 收获加成更新为 0-0
Farmer: 水桶容量更新为 0
Farmer: 工作效率更新为 1.000000
Farmer: 收获时间更新为 2.000000 秒 (加速倍数: 1.000000)
Farmer: 种植时间更新为 1.500000 秒 (效率倍数: 1.000000)
Farmer: 根据品质加成 1.000000 调整作物偏好为 wheat
AttributeBoostSystem: 更新角色 484593118103 装备效果: {  }
CharacterEquipmentUI: 角色创建成功 - ID:484593118103名称:农夫7类型:farmer
[WorkerCharacter] 启动工作操作，AI启用: true
[WorkerCharacter] 启用任务管理器AI
[WorkerCharacter] 调用TaskManager的_add_initial_task方法
[WorkerCharacter] 启用计时器管理器AI
AttributeBoostSystem: 应用移动速度到角色 484593118103: 80.000000 (基础: 80.000000)
WorkerCharacter: 应用移动速度加成 80.000000 -> 80.000000
WorkerCharacter: 应用工作效率加成 1.000000
WorkerCharacter: 应用收获加成 0-0
WorkerCharacter: 应用水桶容量 0
WorkerCharacter: 应用物品容量加成 0
WorkerCharacter: 应用伤害加成 1.000000
WorkerCharacter: 应用木材产量加成范围 0-0
WorkerCharacter: 应用砍伐效率 1.000000
WorkerCharacter: 应用烹饪时间倍数 1.000000
WorkerCharacter: 应用切菜时间倍数 1.000000
WorkerCharacter: 应用食物产量加成范围 0-0
WorkerCharacter: 应用木材产量加成 0-0
WorkerCharacter: 应用钓鱼功能 禁用
Farmer: 收获加成更新为 0-0
Farmer: 水桶容量更新为 0
Farmer: 工作效率更新为 1.000000
Farmer: 收获时间更新为 2.000000 秒 (加速倍数: 1.000000)
Farmer: 种植时间更新为 1.500000 秒 (效率倍数: 1.000000)
Farmer: 根据品质加成 1.000000 调整作物偏好为 wheat
AttributeBoostSystem: 注册角色 484593118103，基础属性: { "movement_speed_multiplier": 1, "harvest_bonus_range": "0-0", "harvest_efficiency_multiplier": 1, "harvest_speed_multiplier": 1, "work_efficiency_multiplier": 1, "damage_multiplier": 1, "cutting_time_multiplier": 1, "cooking_time_multiplier": 1, "water_capacity_bonus": 0, "item_capacity_bonus": 0, "wood_yield_bonus": "0-0", "ore_yield_bonus": "0-0", "wood_yield_multiplier": 1, "wood_yield_bonus_range": "0-0", "chopping_efficiency_multiplier": 1, "food_yield_bonus_range": "0-0", "tool_efficiency_multiplier": 1, "stamina_efficiency_multiplier": 1, "crop_quality_multiplier": 1, "durability_protection_multiplier": 1, "stamina_regeneration_multiplier": 1, "fishing_enabled": false, "base_move_speed": 80, "work_efficiency": 1, "water_capacity": 0, "item_capacity": 0, "tool_efficiency": 1 }
WorkerCharacter: 已向AttributeBoostSystem注册角色 484593118103
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 0.1秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 0.1秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 0.8秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.319，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (560.3402, 212.1668)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: IDLE -> MOVING
[FarmerInteractionManager:@Node2D@631] 统一状态更新: IDLE -> MOVING
[FarmerTimerManager] 统一状态更新: IDLE -> MOVING
[Farmer] 统一状态变更: IDLE -> MOVING
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.606，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (384.8484, 52.21349)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
CharacterEquipmentUI: 开始在场景中生成角色，类型:farmer位置:(538.4356, 143.5748)
角色管理器: 开始生成角色，类型:0位置:(538.4356, 143.5748)
[CharacterPos] 角色管理器: 预设置角色位置 (538.435607910156, 143.574813842773)
角色管理器：调用 farmer.initialize() for ID 500883795019
[CharacterPos] 角色管理器: 使用底部点偏移设置位置 (538.435607910156, 143.574813842773)
[CharacterManager] 角色生成成功：farmer (ID: 500883795019) at (538.4356, 143.5748)
EquipmentManager: 角色 500883795019 已注册到装备系统
AttributeBoostSystem: 应用移动速度到角色 500883795019: 80.000000 (基础: 80.000000)
WorkerCharacter: 应用移动速度加成 80.000000 -> 80.000000
WorkerCharacter: 应用工作效率加成 1.000000
WorkerCharacter: 应用收获加成 0-0
WorkerCharacter: 应用水桶容量 0
WorkerCharacter: 应用物品容量加成 0
WorkerCharacter: 应用伤害加成 1.000000
WorkerCharacter: 应用木材产量加成范围 0-0
WorkerCharacter: 应用砍伐效率 1.000000
WorkerCharacter: 应用烹饪时间倍数 1.000000
WorkerCharacter: 应用切菜时间倍数 1.000000
WorkerCharacter: 应用食物产量加成范围 0-0
WorkerCharacter: 应用木材产量加成 0-0
WorkerCharacter: 应用钓鱼功能 禁用
Farmer: 收获加成更新为 0-0
Farmer: 水桶容量更新为 0
Farmer: 工作效率更新为 1.000000
Farmer: 收获时间更新为 2.000000 秒 (加速倍数: 1.000000)
Farmer: 种植时间更新为 1.500000 秒 (效率倍数: 1.000000)
Farmer: 根据品质加成 1.000000 调整作物偏好为 wheat
AttributeBoostSystem: 注册角色 500883795019，基础属性: { "movement_speed_multiplier": 1, "harvest_bonus_range": "0-0", "harvest_efficiency_multiplier": 1, "harvest_speed_multiplier": 1, "work_efficiency_multiplier": 1, "damage_multiplier": 1, "cutting_time_multiplier": 1, "cooking_time_multiplier": 1, "water_capacity_bonus": 0, "item_capacity_bonus": 0, "wood_yield_bonus": "0-0", "ore_yield_bonus": "0-0", "wood_yield_multiplier": 1, "wood_yield_bonus_range": "0-0", "chopping_efficiency_multiplier": 1, "food_yield_bonus_range": "0-0", "tool_efficiency_multiplier": 1, "stamina_efficiency_multiplier": 1, "crop_quality_multiplier": 1, "durability_protection_multiplier": 1, "stamina_regeneration_multiplier": 1, "fishing_enabled": false }
AttributeBoostSystem: 应用移动速度到角色 500883795019: 80.000000 (基础: 80.000000)
WorkerCharacter: 应用移动速度加成 80.000000 -> 80.000000
WorkerCharacter: 应用工作效率加成 1.000000
WorkerCharacter: 应用收获加成 0-0
WorkerCharacter: 应用水桶容量 0
WorkerCharacter: 应用物品容量加成 0
WorkerCharacter: 应用伤害加成 1.000000
WorkerCharacter: 应用木材产量加成范围 0-0
WorkerCharacter: 应用砍伐效率 1.000000
WorkerCharacter: 应用烹饪时间倍数 1.000000
WorkerCharacter: 应用切菜时间倍数 1.000000
WorkerCharacter: 应用食物产量加成范围 0-0
WorkerCharacter: 应用木材产量加成 0-0
WorkerCharacter: 应用钓鱼功能 禁用
Farmer: 收获加成更新为 0-0
Farmer: 水桶容量更新为 0
Farmer: 工作效率更新为 1.000000
Farmer: 收获时间更新为 2.000000 秒 (加速倍数: 1.000000)
Farmer: 种植时间更新为 1.500000 秒 (效率倍数: 1.000000)
Farmer: 根据品质加成 1.000000 调整作物偏好为 wheat
AttributeBoostSystem: 更新角色 500883795019 装备效果: {  }
CharacterEquipmentUI: 角色创建成功 - ID:500883795019名称:农夫8类型:farmer
[WorkerCharacter] 启动工作操作，AI启用: true
[WorkerCharacter] 启用任务管理器AI
[WorkerCharacter] 调用TaskManager的_add_initial_task方法
[WorkerCharacter] 启用计时器管理器AI
AttributeBoostSystem: 应用移动速度到角色 500883795019: 80.000000 (基础: 80.000000)
WorkerCharacter: 应用移动速度加成 80.000000 -> 80.000000
WorkerCharacter: 应用工作效率加成 1.000000
WorkerCharacter: 应用收获加成 0-0
WorkerCharacter: 应用水桶容量 0
WorkerCharacter: 应用物品容量加成 0
WorkerCharacter: 应用伤害加成 1.000000
WorkerCharacter: 应用木材产量加成范围 0-0
WorkerCharacter: 应用砍伐效率 1.000000
WorkerCharacter: 应用烹饪时间倍数 1.000000
WorkerCharacter: 应用切菜时间倍数 1.000000
WorkerCharacter: 应用食物产量加成范围 0-0
WorkerCharacter: 应用木材产量加成 0-0
WorkerCharacter: 应用钓鱼功能 禁用
Farmer: 收获加成更新为 0-0
Farmer: 水桶容量更新为 0
Farmer: 工作效率更新为 1.000000
Farmer: 收获时间更新为 2.000000 秒 (加速倍数: 1.000000)
Farmer: 种植时间更新为 1.500000 秒 (效率倍数: 1.000000)
Farmer: 根据品质加成 1.000000 调整作物偏好为 wheat
AttributeBoostSystem: 注册角色 500883795019，基础属性: { "movement_speed_multiplier": 1, "harvest_bonus_range": "0-0", "harvest_efficiency_multiplier": 1, "harvest_speed_multiplier": 1, "work_efficiency_multiplier": 1, "damage_multiplier": 1, "cutting_time_multiplier": 1, "cooking_time_multiplier": 1, "water_capacity_bonus": 0, "item_capacity_bonus": 0, "wood_yield_bonus": "0-0", "ore_yield_bonus": "0-0", "wood_yield_multiplier": 1, "wood_yield_bonus_range": "0-0", "chopping_efficiency_multiplier": 1, "food_yield_bonus_range": "0-0", "tool_efficiency_multiplier": 1, "stamina_efficiency_multiplier": 1, "crop_quality_multiplier": 1, "durability_protection_multiplier": 1, "stamina_regeneration_multiplier": 1, "fishing_enabled": false, "base_move_speed": 80, "work_efficiency": 1, "water_capacity": 0, "item_capacity": 0, "tool_efficiency": 1 }
WorkerCharacter: 已向AttributeBoostSystem注册角色 500883795019
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 0.1秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 0.1秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 0.8秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.907，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 4.9秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 4.9秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 4.9秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 4.9秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.148，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (479.4482, 158.9792)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.555，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (321.0349, -4.265839)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
CharacterEquipmentUI: 开始在场景中生成角色，类型:farmer位置:(542.8599, 159.2434)
角色管理器: 开始生成角色，类型:0位置:(542.8599, 159.2434)
[CharacterPos] 角色管理器: 预设置角色位置 (542.859924316406, 159.243408203125)
角色管理器：调用 farmer.initialize() for ID 518097217823
[CharacterPos] 角色管理器: 使用底部点偏移设置位置 (542.859924316406, 159.243408203125)
[CharacterManager] 角色生成成功：farmer (ID: 518097217823) at (542.8599, 159.2434)
EquipmentManager: 角色 518097217823 已注册到装备系统
AttributeBoostSystem: 应用移动速度到角色 518097217823: 80.000000 (基础: 80.000000)
WorkerCharacter: 应用移动速度加成 80.000000 -> 80.000000
WorkerCharacter: 应用工作效率加成 1.000000
WorkerCharacter: 应用收获加成 0-0
WorkerCharacter: 应用水桶容量 0
WorkerCharacter: 应用物品容量加成 0
WorkerCharacter: 应用伤害加成 1.000000
WorkerCharacter: 应用木材产量加成范围 0-0
WorkerCharacter: 应用砍伐效率 1.000000
WorkerCharacter: 应用烹饪时间倍数 1.000000
WorkerCharacter: 应用切菜时间倍数 1.000000
WorkerCharacter: 应用食物产量加成范围 0-0
WorkerCharacter: 应用木材产量加成 0-0
WorkerCharacter: 应用钓鱼功能 禁用
Farmer: 收获加成更新为 0-0
Farmer: 水桶容量更新为 0
Farmer: 工作效率更新为 1.000000
Farmer: 收获时间更新为 2.000000 秒 (加速倍数: 1.000000)
Farmer: 种植时间更新为 1.500000 秒 (效率倍数: 1.000000)
Farmer: 根据品质加成 1.000000 调整作物偏好为 wheat
AttributeBoostSystem: 注册角色 518097217823，基础属性: { "movement_speed_multiplier": 1, "harvest_bonus_range": "0-0", "harvest_efficiency_multiplier": 1, "harvest_speed_multiplier": 1, "work_efficiency_multiplier": 1, "damage_multiplier": 1, "cutting_time_multiplier": 1, "cooking_time_multiplier": 1, "water_capacity_bonus": 0, "item_capacity_bonus": 0, "wood_yield_bonus": "0-0", "ore_yield_bonus": "0-0", "wood_yield_multiplier": 1, "wood_yield_bonus_range": "0-0", "chopping_efficiency_multiplier": 1, "food_yield_bonus_range": "0-0", "tool_efficiency_multiplier": 1, "stamina_efficiency_multiplier": 1, "crop_quality_multiplier": 1, "durability_protection_multiplier": 1, "stamina_regeneration_multiplier": 1, "fishing_enabled": false }
AttributeBoostSystem: 应用移动速度到角色 518097217823: 80.000000 (基础: 80.000000)
WorkerCharacter: 应用移动速度加成 80.000000 -> 80.000000
WorkerCharacter: 应用工作效率加成 1.000000
WorkerCharacter: 应用收获加成 0-0
WorkerCharacter: 应用水桶容量 0
WorkerCharacter: 应用物品容量加成 0
WorkerCharacter: 应用伤害加成 1.000000
WorkerCharacter: 应用木材产量加成范围 0-0
WorkerCharacter: 应用砍伐效率 1.000000
WorkerCharacter: 应用烹饪时间倍数 1.000000
WorkerCharacter: 应用切菜时间倍数 1.000000
WorkerCharacter: 应用食物产量加成范围 0-0
WorkerCharacter: 应用木材产量加成 0-0
WorkerCharacter: 应用钓鱼功能 禁用
Farmer: 收获加成更新为 0-0
Farmer: 水桶容量更新为 0
Farmer: 工作效率更新为 1.000000
Farmer: 收获时间更新为 2.000000 秒 (加速倍数: 1.000000)
Farmer: 种植时间更新为 1.500000 秒 (效率倍数: 1.000000)
Farmer: 根据品质加成 1.000000 调整作物偏好为 wheat
AttributeBoostSystem: 更新角色 518097217823 装备效果: {  }
CharacterEquipmentUI: 角色创建成功 - ID:518097217823名称:农夫9类型:farmer
[WorkerCharacter] 启动工作操作，AI启用: true
[WorkerCharacter] 启用任务管理器AI
[WorkerCharacter] 调用TaskManager的_add_initial_task方法
[WorkerCharacter] 启用计时器管理器AI
AttributeBoostSystem: 应用移动速度到角色 518097217823: 80.000000 (基础: 80.000000)
WorkerCharacter: 应用移动速度加成 80.000000 -> 80.000000
WorkerCharacter: 应用工作效率加成 1.000000
WorkerCharacter: 应用收获加成 0-0
WorkerCharacter: 应用水桶容量 0
WorkerCharacter: 应用物品容量加成 0
WorkerCharacter: 应用伤害加成 1.000000
WorkerCharacter: 应用木材产量加成范围 0-0
WorkerCharacter: 应用砍伐效率 1.000000
WorkerCharacter: 应用烹饪时间倍数 1.000000
WorkerCharacter: 应用切菜时间倍数 1.000000
WorkerCharacter: 应用食物产量加成范围 0-0
WorkerCharacter: 应用木材产量加成 0-0
WorkerCharacter: 应用钓鱼功能 禁用
Farmer: 收获加成更新为 0-0
Farmer: 水桶容量更新为 0
Farmer: 工作效率更新为 1.000000
Farmer: 收获时间更新为 2.000000 秒 (加速倍数: 1.000000)
Farmer: 种植时间更新为 1.500000 秒 (效率倍数: 1.000000)
Farmer: 根据品质加成 1.000000 调整作物偏好为 wheat
AttributeBoostSystem: 注册角色 518097217823，基础属性: { "movement_speed_multiplier": 1, "harvest_bonus_range": "0-0", "harvest_efficiency_multiplier": 1, "harvest_speed_multiplier": 1, "work_efficiency_multiplier": 1, "damage_multiplier": 1, "cutting_time_multiplier": 1, "cooking_time_multiplier": 1, "water_capacity_bonus": 0, "item_capacity_bonus": 0, "wood_yield_bonus": "0-0", "ore_yield_bonus": "0-0", "wood_yield_multiplier": 1, "wood_yield_bonus_range": "0-0", "chopping_efficiency_multiplier": 1, "food_yield_bonus_range": "0-0", "tool_efficiency_multiplier": 1, "stamina_efficiency_multiplier": 1, "crop_quality_multiplier": 1, "durability_protection_multiplier": 1, "stamina_regeneration_multiplier": 1, "fishing_enabled": false, "base_move_speed": 80, "work_efficiency": 1, "water_capacity": 0, "item_capacity": 0, "tool_efficiency": 1 }
WorkerCharacter: 已向AttributeBoostSystem注册角色 518097217823
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 0.1秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 0.1秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 0.8秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.792，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 5.0秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 5.0秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 5.0秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 5.0秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.429，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (532.9219, 127.9465)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.196，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (363.1473, 124.448)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.258，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (492.7794, 30.58798)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: IDLE -> MOVING
[FarmerInteractionManager:@Node2D@667] 统一状态更新: IDLE -> MOVING
[FarmerTimerManager] 统一状态更新: IDLE -> MOVING
[Farmer] 统一状态变更: IDLE -> MOVING
CharacterEquipmentUI: 开始在场景中生成角色，类型:farmer位置:(510.2977, 154.7772)
角色管理器: 开始生成角色，类型:0位置:(510.2977, 154.7772)
[CharacterPos] 角色管理器: 预设置角色位置 (510.297668457031, 154.777206420898)
角色管理器：调用 farmer.initialize() for ID 538531868025
[CharacterPos] 角色管理器: 使用底部点偏移设置位置 (510.297668457031, 154.777206420898)
[CharacterManager] 角色生成成功：farmer (ID: 538531868025) at (510.2977, 154.7772)
EquipmentManager: 角色 538531868025 已注册到装备系统
AttributeBoostSystem: 应用移动速度到角色 538531868025: 80.000000 (基础: 80.000000)
WorkerCharacter: 应用移动速度加成 80.000000 -> 80.000000
WorkerCharacter: 应用工作效率加成 1.000000
WorkerCharacter: 应用收获加成 0-0
WorkerCharacter: 应用水桶容量 0
WorkerCharacter: 应用物品容量加成 0
WorkerCharacter: 应用伤害加成 1.000000
WorkerCharacter: 应用木材产量加成范围 0-0
WorkerCharacter: 应用砍伐效率 1.000000
WorkerCharacter: 应用烹饪时间倍数 1.000000
WorkerCharacter: 应用切菜时间倍数 1.000000
WorkerCharacter: 应用食物产量加成范围 0-0
WorkerCharacter: 应用木材产量加成 0-0
WorkerCharacter: 应用钓鱼功能 禁用
Farmer: 收获加成更新为 0-0
Farmer: 水桶容量更新为 0
Farmer: 工作效率更新为 1.000000
Farmer: 收获时间更新为 2.000000 秒 (加速倍数: 1.000000)
Farmer: 种植时间更新为 1.500000 秒 (效率倍数: 1.000000)
Farmer: 根据品质加成 1.000000 调整作物偏好为 wheat
AttributeBoostSystem: 注册角色 538531868025，基础属性: { "movement_speed_multiplier": 1, "harvest_bonus_range": "0-0", "harvest_efficiency_multiplier": 1, "harvest_speed_multiplier": 1, "work_efficiency_multiplier": 1, "damage_multiplier": 1, "cutting_time_multiplier": 1, "cooking_time_multiplier": 1, "water_capacity_bonus": 0, "item_capacity_bonus": 0, "wood_yield_bonus": "0-0", "ore_yield_bonus": "0-0", "wood_yield_multiplier": 1, "wood_yield_bonus_range": "0-0", "chopping_efficiency_multiplier": 1, "food_yield_bonus_range": "0-0", "tool_efficiency_multiplier": 1, "stamina_efficiency_multiplier": 1, "crop_quality_multiplier": 1, "durability_protection_multiplier": 1, "stamina_regeneration_multiplier": 1, "fishing_enabled": false }
AttributeBoostSystem: 应用移动速度到角色 538531868025: 80.000000 (基础: 80.000000)
WorkerCharacter: 应用移动速度加成 80.000000 -> 80.000000
WorkerCharacter: 应用工作效率加成 1.000000
WorkerCharacter: 应用收获加成 0-0
WorkerCharacter: 应用水桶容量 0
WorkerCharacter: 应用物品容量加成 0
WorkerCharacter: 应用伤害加成 1.000000
WorkerCharacter: 应用木材产量加成范围 0-0
WorkerCharacter: 应用砍伐效率 1.000000
WorkerCharacter: 应用烹饪时间倍数 1.000000
WorkerCharacter: 应用切菜时间倍数 1.000000
WorkerCharacter: 应用食物产量加成范围 0-0
WorkerCharacter: 应用木材产量加成 0-0
WorkerCharacter: 应用钓鱼功能 禁用
Farmer: 收获加成更新为 0-0
Farmer: 水桶容量更新为 0
Farmer: 工作效率更新为 1.000000
Farmer: 收获时间更新为 2.000000 秒 (加速倍数: 1.000000)
Farmer: 种植时间更新为 1.500000 秒 (效率倍数: 1.000000)
Farmer: 根据品质加成 1.000000 调整作物偏好为 wheat
AttributeBoostSystem: 更新角色 538531868025 装备效果: {  }
CharacterEquipmentUI: 角色创建成功 - ID:538531868025名称:农夫10类型:farmer
[WorkerCharacter] 启动工作操作，AI启用: true
[WorkerCharacter] 启用任务管理器AI
[WorkerCharacter] 调用TaskManager的_add_initial_task方法
[WorkerCharacter] 启用计时器管理器AI
AttributeBoostSystem: 应用移动速度到角色 538531868025: 80.000000 (基础: 80.000000)
WorkerCharacter: 应用移动速度加成 80.000000 -> 80.000000
WorkerCharacter: 应用工作效率加成 1.000000
WorkerCharacter: 应用收获加成 0-0
WorkerCharacter: 应用水桶容量 0
WorkerCharacter: 应用物品容量加成 0
WorkerCharacter: 应用伤害加成 1.000000
WorkerCharacter: 应用木材产量加成范围 0-0
WorkerCharacter: 应用砍伐效率 1.000000
WorkerCharacter: 应用烹饪时间倍数 1.000000
WorkerCharacter: 应用切菜时间倍数 1.000000
WorkerCharacter: 应用食物产量加成范围 0-0
WorkerCharacter: 应用木材产量加成 0-0
WorkerCharacter: 应用钓鱼功能 禁用
Farmer: 收获加成更新为 0-0
Farmer: 水桶容量更新为 0
Farmer: 工作效率更新为 1.000000
Farmer: 收获时间更新为 2.000000 秒 (加速倍数: 1.000000)
Farmer: 种植时间更新为 1.500000 秒 (效率倍数: 1.000000)
Farmer: 根据品质加成 1.000000 调整作物偏好为 wheat
AttributeBoostSystem: 注册角色 538531868025，基础属性: { "movement_speed_multiplier": 1, "harvest_bonus_range": "0-0", "harvest_efficiency_multiplier": 1, "harvest_speed_multiplier": 1, "work_efficiency_multiplier": 1, "damage_multiplier": 1, "cutting_time_multiplier": 1, "cooking_time_multiplier": 1, "water_capacity_bonus": 0, "item_capacity_bonus": 0, "wood_yield_bonus": "0-0", "ore_yield_bonus": "0-0", "wood_yield_multiplier": 1, "wood_yield_bonus_range": "0-0", "chopping_efficiency_multiplier": 1, "food_yield_bonus_range": "0-0", "tool_efficiency_multiplier": 1, "stamina_efficiency_multiplier": 1, "crop_quality_multiplier": 1, "durability_protection_multiplier": 1, "stamina_regeneration_multiplier": 1, "fishing_enabled": false, "base_move_speed": 80, "work_efficiency": 1, "water_capacity": 0, "item_capacity": 0, "tool_efficiency": 1 }
WorkerCharacter: 已向AttributeBoostSystem注册角色 538531868025
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 0.1秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 0.1秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 0.8秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.323，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (424.7428, 185.1796)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.513，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (463.3387, 210.3184)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.339，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (530.5208, 285.6677)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: IDLE -> MOVING
[FarmerInteractionManager:@Node2D@681] 统一状态更新: IDLE -> MOVING
[FarmerTimerManager] 统一状态更新: IDLE -> MOVING
[Farmer] 统一状态变更: IDLE -> MOVING
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.013，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (567.5408, 207.5882)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.361，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (423.539, 113.4177)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.520，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (576.4622, 336.2769)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.699，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (459.2481, 266.1549)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.496，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (724.3882, 267.7937)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.185，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (542.5853, 127.2288)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.748，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 2.6秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 2.6秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 2.6秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 2.6秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.177，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (509.6561, 94.0012)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.709，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 4.8秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 4.8秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 4.8秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 4.8秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: plant, 目标: Farmland
[WorkerCharacter] 动画切换: plant -> plant (状态: WORKING_2)
[FarmerInteractionManager] 启动动作计时器: plant_action, 预期时长: 1.5秒
[FarmerTimerManager] 使用预设动作时间: plant_action = 1.5秒
[FarmerTimerManager] 🔧 计时器已启动: plant_action, 实际时长: 1.5秒
[BaseInteractionManager] 交互执行结果: true
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.311，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (544.6445, 30.51331)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: IDLE -> MOVING
[FarmerInteractionManager:@Node2D@654] 统一状态更新: IDLE -> MOVING
[FarmerTimerManager] 统一状态更新: IDLE -> MOVING
[Farmer] 统一状态变更: IDLE -> MOVING
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: plant, 目标: Farmland
[WorkerCharacter] 动画切换: plant -> plant (状态: WORKING_2)
[FarmerInteractionManager] 启动动作计时器: plant_action, 预期时长: 1.5秒
[FarmerTimerManager] 使用预设动作时间: plant_action = 1.5秒
[FarmerTimerManager] 🔧 计时器已启动: plant_action, 实际时长: 1.5秒
[BaseInteractionManager] 交互执行结果: true
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.949，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 2.2秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 2.2秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 2.2秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 2.2秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.218，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (754.0718, 207.1989)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: plant, 目标: Farmland
[WorkerCharacter] 动画切换: plant -> plant (状态: WORKING_2)
[FarmerInteractionManager] 启动动作计时器: plant_action, 预期时长: 1.5秒
[FarmerTimerManager] 使用预设动作时间: plant_action = 1.5秒
[FarmerTimerManager] 🔧 计时器已启动: plant_action, 实际时长: 1.5秒
[BaseInteractionManager] 交互执行结果: true
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.869，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 2.9秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 2.9秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 2.9秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 2.9秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.949，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 5.0秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 5.0秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 5.0秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 5.0秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[Crop-wheat] [DEBUG] 作物初始化完成: 小麦
[FarmerInteractionManager:Farmer] 成功种植作物: wheat
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 5)
[WorkerCharacter] 动画切换: collecting -> collecting (状态: COLLECTING)
[FarmerTaskManager] 统一状态更新: MOVING -> FETCHING_WATER
[FarmerInteractionManager:Farmer] 统一状态更新: MOVING -> FETCHING_WATER
[FarmerTimerManager] 统一状态更新: MOVING -> FETCHING_WATER
[Farmer] 统一状态变更: MOVING -> FETCHING_WATER
[WorkerCharacter] 侧面评分 - 左侧: 56.56, 右侧: 96.56, 选择: right
[WorkerCharacter] 智能选择交互侧面: right (目标: Well)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1156.0, 224.0), 侧面: right
[BaseInteractionManager] 开始交互: fetch_water, 目标: Well, 距离: 68.8, 目标位置: (1156.0, 224.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1156.0, 224.0), 侧面: right
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: FETCHING_WATER -> MOVING
[FarmerInteractionManager:Farmer] 统一状态更新: FETCHING_WATER -> MOVING
[FarmerTimerManager] 统一状态更新: FETCHING_WATER -> MOVING
[Farmer] 统一状态变更: FETCHING_WATER -> MOVING
[Crop-wheat] [INFO] 作物因缺水停止生长
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: plant, 目标: Farmland
[WorkerCharacter] 动画切换: plant -> plant (状态: WORKING_2)
[FarmerInteractionManager] 启动动作计时器: plant_action, 预期时长: 1.5秒
[FarmerTimerManager] 使用预设动作时间: plant_action = 1.5秒
[FarmerTimerManager] 🔧 计时器已启动: plant_action, 实际时长: 1.5秒
[BaseInteractionManager] 交互执行结果: true
[BaseTaskManager] 生成随机值: 0.687，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (594.0991, 117.8828)
[BaseTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[Crop-wheat] [DEBUG] 作物初始化完成: 小麦
[FarmerInteractionManager:@Node2D@604] 成功种植作物: wheat
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 5)
[WorkerCharacter] 动画切换: collecting -> collecting (状态: COLLECTING)
[FarmerTaskManager] 统一状态更新: MOVING -> FETCHING_WATER
[FarmerInteractionManager:@Node2D@604] 统一状态更新: MOVING -> FETCHING_WATER
[FarmerTimerManager] 统一状态更新: MOVING -> FETCHING_WATER
[Farmer] 统一状态变更: MOVING -> FETCHING_WATER
[WorkerCharacter] 侧面评分 - 左侧: 57.17, 右侧: 97.17, 选择: right
[WorkerCharacter] 智能选择交互侧面: right (目标: Well)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1156.0, 224.0), 侧面: right
[BaseInteractionManager] 开始交互: fetch_water, 目标: Well, 距离: 56.6, 目标位置: (1156.0, 224.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1156.0, 224.0), 侧面: right
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: FETCHING_WATER -> MOVING
[FarmerInteractionManager:@Node2D@604] 统一状态更新: FETCHING_WATER -> MOVING
[FarmerTimerManager] 统一状态更新: FETCHING_WATER -> MOVING
[Farmer] 统一状态变更: FETCHING_WATER -> MOVING
[Crop-wheat] [INFO] 作物因缺水停止生长
[Crop-wheat] [DEBUG] 作物初始化完成: 小麦
[FarmerInteractionManager:@Node2D@597] 成功种植作物: wheat
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 5)
[WorkerCharacter] 动画切换: collecting -> collecting (状态: COLLECTING)
[FarmerTaskManager] 统一状态更新: MOVING -> FETCHING_WATER
[FarmerInteractionManager:@Node2D@597] 统一状态更新: MOVING -> FETCHING_WATER
[FarmerTimerManager] 统一状态更新: MOVING -> FETCHING_WATER
[Farmer] 统一状态变更: MOVING -> FETCHING_WATER
[WorkerCharacter] 侧面评分 - 左侧: 55.17, 右侧: 95.17, 选择: right
[WorkerCharacter] 智能选择交互侧面: right (目标: Well)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1156.0, 224.0), 侧面: right
[BaseInteractionManager] 开始交互: fetch_water, 目标: Well, 距离: 96.7, 目标位置: (1156.0, 224.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1156.0, 224.0), 侧面: right
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: FETCHING_WATER -> MOVING
[FarmerInteractionManager:@Node2D@597] 统一状态更新: FETCHING_WATER -> MOVING
[FarmerTimerManager] 统一状态更新: FETCHING_WATER -> MOVING
[Farmer] 统一状态变更: FETCHING_WATER -> MOVING
[Crop-wheat] [INFO] 作物因缺水停止生长
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: fetch_water, 目标: Well
[WorkerCharacter] 动画切换: collecting -> collecting (状态: COLLECTING)
[FarmerInteractionManager] 启动动作计时器: fetch_water_action, 预期时长: 2.0秒
[FarmerTimerManager] 使用预设动作时间: fetch_water_action = 2.0秒
[FarmerTimerManager] 🔧 计时器已启动: fetch_water_action, 实际时长: 2.0秒
[BaseInteractionManager] 交互执行结果: true
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[BaseTaskManager] 生成随机值: 0.269，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (536.9761, 28.89652)
[BaseTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: fetch_water, 目标: Well
[WorkerCharacter] 动画切换: collecting -> collecting (状态: COLLECTING)
[FarmerInteractionManager] 启动动作计时器: fetch_water_action, 预期时长: 2.0秒
[FarmerTimerManager] 使用预设动作时间: fetch_water_action = 2.0秒
[FarmerTimerManager] 🔧 计时器已启动: fetch_water_action, 实际时长: 2.0秒
[BaseInteractionManager] 交互执行结果: true
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.251，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (529.2265, -47.15639)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.039，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (693.5711, 88.742)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.390，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (733.4449, 264.8141)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.887，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 4.7秒
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 4.7秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 4.7秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 4.7秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[Crop-wheat] [DEBUG] 作物初始化完成: 小麦
[FarmerInteractionManager:@Node2D@612] 成功种植作物: wheat
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: fetch_water, 目标: Well
[WorkerCharacter] 动画切换: collecting -> collecting (状态: COLLECTING)
[FarmerInteractionManager] 启动动作计时器: fetch_water_action, 预期时长: 2.0秒
[FarmerTimerManager] 使用预设动作时间: fetch_water_action = 2.0秒
[FarmerTimerManager] 🔧 计时器已启动: fetch_water_action, 实际时长: 2.0秒
[BaseInteractionManager] 交互执行结果: true
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 5)
[WorkerCharacter] 动画切换: collecting -> collecting (状态: COLLECTING)
[FarmerTaskManager] 统一状态更新: MOVING -> FETCHING_WATER
[FarmerInteractionManager:@Node2D@612] 统一状态更新: MOVING -> FETCHING_WATER
[FarmerTimerManager] 统一状态更新: MOVING -> FETCHING_WATER
[Farmer] 统一状态变更: MOVING -> FETCHING_WATER
[WorkerCharacter] 侧面评分 - 左侧: 55.58, 右侧: 95.58, 选择: right
[WorkerCharacter] 智能选择交互侧面: right (目标: Well)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1156.0, 224.0), 侧面: right
[BaseInteractionManager] 开始交互: fetch_water, 目标: Well, 距离: 88.4, 目标位置: (1156.0, 224.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1156.0, 224.0), 侧面: right
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: FETCHING_WATER -> MOVING
[FarmerInteractionManager:@Node2D@612] 统一状态更新: FETCHING_WATER -> MOVING
[FarmerTimerManager] 统一状态更新: FETCHING_WATER -> MOVING
[Farmer] 统一状态变更: FETCHING_WATER -> MOVING
[Crop-wheat] [INFO] 作物因缺水停止生长
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[BaseTaskManager] 生成随机值: 0.408，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (677.0443, 155.403)
[BaseTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.709，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 4.4秒
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 4.4秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 4.4秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 4.4秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[BaseTaskManager] 生成随机值: 0.323，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (532.4321, 24.85542)
[BaseTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: IDLE -> MOVING
[FarmerInteractionManager:@Node2D@642] 统一状态更新: IDLE -> MOVING
[FarmerTimerManager] 统一状态更新: IDLE -> MOVING
[Farmer] 统一状态变更: IDLE -> MOVING
[FarmerInteractionManager:Farmer] 使用装备水桶容量取水: 5.0 (基础: 5.0 + 装备: 0)
[水井] 农夫请求取水: 5.0 单位，直接提供
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[FarmerTaskManager] 统一状态更新: MOVING -> CARRYING
[FarmerInteractionManager:Farmer] 统一状态更新: MOVING -> CARRYING
[FarmerTimerManager] 统一状态更新: MOVING -> CARRYING
[Farmer] 统一状态变更: MOVING -> CARRYING
[Farmer] 尝试显示携带图标，资源类型: water
[Farmer] DataManager返回的图标数据: {  }
[Farmer] 警告：无法找到任何携带图标，资源类型: water
[FarmerInteractionManager:Farmer] 成功取水 5.0 单位 (请求: 5.0)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 6)
[FarmerTaskManager] 进入空闲状态 - 准备新任务决策
[FarmerTaskManager] 统一状态更新: CARRYING -> IDLE
[FarmerInteractionManager:Farmer] 统一状态更新: CARRYING -> IDLE
[FarmerTimerManager] 统一状态更新: CARRYING -> IDLE
[Farmer] 统一状态变更: CARRYING -> IDLE
[WorkerCharacter] 侧面评分 - 左侧: 96.56, 右侧: 54.80, 选择: left
[WorkerCharacter] 智能选择交互侧面: left (目标: Farmland)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1212.0, 184.0), 侧面: left
[BaseInteractionManager] 开始交互: water, 目标: Farmland, 距离: 68.8, 目标位置: (1212.0, 184.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1212.0, 184.0), 侧面: left
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: IDLE -> MOVING
[FarmerInteractionManager:Farmer] 统一状态更新: IDLE -> MOVING
[FarmerTimerManager] 统一状态更新: IDLE -> MOVING
[Farmer] 统一状态变更: IDLE -> MOVING
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: fetch_water, 目标: Well
[WorkerCharacter] 动画切换: collecting -> collecting (状态: COLLECTING)
[FarmerInteractionManager] 启动动作计时器: fetch_water_action, 预期时长: 2.0秒
[FarmerTimerManager] 使用预设动作时间: fetch_water_action = 2.0秒
[FarmerTimerManager] 🔧 计时器已启动: fetch_water_action, 实际时长: 2.0秒
[BaseInteractionManager] 交互执行结果: true
[FarmerInteractionManager:@Node2D@604] 使用装备水桶容量取水: 5.0 (基础: 5.0 + 装备: 0)
[水井] 农夫请求取水: 5.0 单位，直接提供
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[FarmerTaskManager] 统一状态更新: MOVING -> CARRYING
[FarmerInteractionManager:@Node2D@604] 统一状态更新: MOVING -> CARRYING
[FarmerTimerManager] 统一状态更新: MOVING -> CARRYING
[Farmer] 统一状态变更: MOVING -> CARRYING
[Farmer] 尝试显示携带图标，资源类型: water
[Farmer] DataManager返回的图标数据: {  }
[Farmer] 警告：无法找到任何携带图标，资源类型: water
[FarmerInteractionManager:@Node2D@604] 成功取水 5.0 单位 (请求: 5.0)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 6)
[FarmerTaskManager] 进入空闲状态 - 准备新任务决策
[FarmerTaskManager] 统一状态更新: CARRYING -> IDLE
[FarmerInteractionManager:@Node2D@604] 统一状态更新: CARRYING -> IDLE
[FarmerTimerManager] 统一状态更新: CARRYING -> IDLE
[Farmer] 统一状态变更: CARRYING -> IDLE
[WorkerCharacter] 侧面评分 - 左侧: 97.17, 右侧: 55.18, 选择: left
[WorkerCharacter] 智能选择交互侧面: left (目标: Farmland)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1212.0, 216.0), 侧面: left
[BaseInteractionManager] 开始交互: water, 目标: Farmland, 距离: 56.6, 目标位置: (1212.0, 216.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1212.0, 216.0), 侧面: left
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: IDLE -> MOVING
[FarmerInteractionManager:@Node2D@604] 统一状态更新: IDLE -> MOVING
[FarmerTimerManager] 统一状态更新: IDLE -> MOVING
[Farmer] 统一状态变更: IDLE -> MOVING
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.053，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (565.4082, 36.65278)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: water, 目标: Farmland
[WorkerCharacter] 动画切换: water -> water (状态: WORKING_3)
[FarmerInteractionManager] 启动动作计时器: water_action, 预期时长: 1.2秒
[FarmerTimerManager] 使用预设动作时间: water_action = 1.2秒
[FarmerTimerManager] 🔧 计时器已启动: water_action, 实际时长: 1.2秒
[BaseInteractionManager] 交互执行结果: true
[FarmerInteractionManager:@Node2D@597] 使用装备水桶容量取水: 5.0 (基础: 5.0 + 装备: 0)
[水井] 农夫请求取水: 5.0 单位，直接提供
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[FarmerTaskManager] 统一状态更新: MOVING -> CARRYING
[FarmerInteractionManager:@Node2D@597] 统一状态更新: MOVING -> CARRYING
[FarmerTimerManager] 统一状态更新: MOVING -> CARRYING
[Farmer] 统一状态变更: MOVING -> CARRYING
[Farmer] 尝试显示携带图标，资源类型: water
[Farmer] DataManager返回的图标数据: {  }
[Farmer] 警告：无法找到任何携带图标，资源类型: water
[FarmerInteractionManager:@Node2D@597] 成功取水 5.0 单位 (请求: 5.0)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 6)
[FarmerTaskManager] 进入空闲状态 - 准备新任务决策
[FarmerTaskManager] 统一状态更新: CARRYING -> IDLE
[FarmerInteractionManager:@Node2D@597] 统一状态更新: CARRYING -> IDLE
[FarmerTimerManager] 统一状态更新: CARRYING -> IDLE
[Farmer] 统一状态变更: CARRYING -> IDLE
[WorkerCharacter] 侧面评分 - 左侧: 95.17, 右侧: 53.29, 选择: left
[WorkerCharacter] 智能选择交互侧面: left (目标: Farmland)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1244.0, 184.0), 侧面: left
[BaseInteractionManager] 开始交互: water, 目标: Farmland, 距离: 96.7, 目标位置: (1244.0, 184.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1244.0, 184.0), 侧面: left
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: IDLE -> MOVING
[FarmerInteractionManager:@Node2D@597] 统一状态更新: IDLE -> MOVING
[FarmerTimerManager] 统一状态更新: IDLE -> MOVING
[Farmer] 统一状态变更: IDLE -> MOVING
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: water, 目标: Farmland
[WorkerCharacter] 动画切换: water -> water (状态: WORKING_3)
[FarmerInteractionManager] 启动动作计时器: water_action, 预期时长: 1.2秒
[FarmerTimerManager] 使用预设动作时间: water_action = 1.2秒
[FarmerTimerManager] 🔧 计时器已启动: water_action, 实际时长: 1.2秒
[BaseInteractionManager] 交互执行结果: true
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.292，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (627.3243, 58.48575)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.975，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 2.6秒
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 2.6秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 2.6秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 2.6秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.942，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 3.3秒
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 3.3秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 3.3秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 3.3秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[BaseTaskManager] 生成随机值: 0.217，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (533.8776, 176.8469)
[BaseTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerInteractionManager:Farmer] 成功浇水 5.0 单位
(2) [WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.400，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (507.9619, 20.56431)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 5)
[WorkerCharacter] 动画切换: collecting -> collecting (状态: COLLECTING)
[FarmerTaskManager] 统一状态更新: MOVING -> FETCHING_WATER
[FarmerInteractionManager:Farmer] 统一状态更新: MOVING -> FETCHING_WATER
[FarmerTimerManager] 统一状态更新: MOVING -> FETCHING_WATER
[Farmer] 统一状态变更: MOVING -> FETCHING_WATER
[WorkerCharacter] 侧面评分 - 左侧: 56.56, 右侧: 96.56, 选择: right
[WorkerCharacter] 智能选择交互侧面: right (目标: Well)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1156.0, 224.0), 侧面: right
[BaseInteractionManager] 开始交互: fetch_water, 目标: Well, 距离: 68.8, 目标位置: (1156.0, 224.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1156.0, 224.0), 侧面: right
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: FETCHING_WATER -> MOVING
[FarmerInteractionManager:Farmer] 统一状态更新: FETCHING_WATER -> MOVING
[FarmerTimerManager] 统一状态更新: FETCHING_WATER -> MOVING
[Farmer] 统一状态变更: FETCHING_WATER -> MOVING
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerInteractionManager:@Node2D@612] 使用装备水桶容量取水: 5.0 (基础: 5.0 + 装备: 0)
[水井] 农夫请求取水: 5.0 单位，直接提供
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[FarmerTaskManager] 统一状态更新: MOVING -> CARRYING
[FarmerInteractionManager:@Node2D@612] 统一状态更新: MOVING -> CARRYING
[FarmerTimerManager] 统一状态更新: MOVING -> CARRYING
[Farmer] 统一状态变更: MOVING -> CARRYING
[Farmer] 尝试显示携带图标，资源类型: water
[Farmer] DataManager返回的图标数据: {  }
[Farmer] 警告：无法找到任何携带图标，资源类型: water
[FarmerInteractionManager:@Node2D@612] 成功取水 5.0 单位 (请求: 5.0)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 6)
[FarmerTaskManager] 进入空闲状态 - 准备新任务决策
[FarmerTaskManager] 统一状态更新: CARRYING -> IDLE
[FarmerInteractionManager:@Node2D@612] 统一状态更新: CARRYING -> IDLE
[FarmerTimerManager] 统一状态更新: CARRYING -> IDLE
[Farmer] 统一状态变更: CARRYING -> IDLE
[WorkerCharacter] 侧面评分 - 左侧: 95.58, 右侧: 53.59, 选择: left
[WorkerCharacter] 智能选择交互侧面: left (目标: Farmland)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1244.0, 216.0), 侧面: left
[BaseInteractionManager] 开始交互: water, 目标: Farmland, 距离: 88.4, 目标位置: (1244.0, 216.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1244.0, 216.0), 侧面: left
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: IDLE -> MOVING
[FarmerInteractionManager:@Node2D@612] 统一状态更新: IDLE -> MOVING
[FarmerTimerManager] 统一状态更新: IDLE -> MOVING
[Farmer] 统一状态变更: IDLE -> MOVING
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: water, 目标: Farmland
[WorkerCharacter] 动画切换: water -> water (状态: WORKING_3)
[FarmerInteractionManager] 启动动作计时器: water_action, 预期时长: 1.2秒
[FarmerTimerManager] 使用预设动作时间: water_action = 1.2秒
[FarmerTimerManager] 🔧 计时器已启动: water_action, 实际时长: 1.2秒
[BaseInteractionManager] 交互执行结果: true
[FarmerInteractionManager:@Node2D@604] 成功浇水 5.0 单位
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 5)
[WorkerCharacter] 动画切换: collecting -> collecting (状态: COLLECTING)
[FarmerTaskManager] 统一状态更新: MOVING -> FETCHING_WATER
[FarmerInteractionManager:@Node2D@604] 统一状态更新: MOVING -> FETCHING_WATER
[FarmerTimerManager] 统一状态更新: MOVING -> FETCHING_WATER
[Farmer] 统一状态变更: MOVING -> FETCHING_WATER
[WorkerCharacter] 侧面评分 - 左侧: 57.17, 右侧: 97.17, 选择: right
[WorkerCharacter] 智能选择交互侧面: right (目标: Well)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1156.0, 224.0), 侧面: right
[BaseInteractionManager] 开始交互: fetch_water, 目标: Well, 距离: 56.6, 目标位置: (1156.0, 224.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1156.0, 224.0), 侧面: right
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: FETCHING_WATER -> MOVING
[FarmerInteractionManager:@Node2D@604] 统一状态更新: FETCHING_WATER -> MOVING
[FarmerTimerManager] 统一状态更新: FETCHING_WATER -> MOVING
[Farmer] 统一状态变更: FETCHING_WATER -> MOVING
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: fetch_water, 目标: Well
[WorkerCharacter] 动画切换: collecting -> collecting (状态: COLLECTING)
[FarmerInteractionManager] 启动动作计时器: fetch_water_action, 预期时长: 2.0秒
[FarmerTimerManager] 使用预设动作时间: fetch_water_action = 2.0秒
[FarmerTimerManager] 🔧 计时器已启动: fetch_water_action, 实际时长: 2.0秒
[BaseInteractionManager] 交互执行结果: true
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: fetch_water, 目标: Well
[WorkerCharacter] 动画切换: collecting -> collecting (状态: COLLECTING)
[FarmerInteractionManager] 启动动作计时器: fetch_water_action, 预期时长: 2.0秒
[FarmerTimerManager] 使用预设动作时间: fetch_water_action = 2.0秒
[FarmerTimerManager] 🔧 计时器已启动: fetch_water_action, 实际时长: 2.0秒
[BaseInteractionManager] 交互执行结果: true
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.582，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (480.7975, 167.1441)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: water, 目标: Farmland
[WorkerCharacter] 动画切换: water -> water (状态: WORKING_3)
[FarmerInteractionManager] 启动动作计时器: water_action, 预期时长: 1.2秒
[FarmerTimerManager] 使用预设动作时间: water_action = 1.2秒
[FarmerTimerManager] 🔧 计时器已启动: water_action, 实际时长: 1.2秒
[BaseInteractionManager] 交互执行结果: true
[FarmerInteractionManager:@Node2D@597] 成功浇水 5.0 单位
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 5)
[WorkerCharacter] 动画切换: collecting -> collecting (状态: COLLECTING)
[FarmerTaskManager] 统一状态更新: MOVING -> FETCHING_WATER
[FarmerInteractionManager:@Node2D@597] 统一状态更新: MOVING -> FETCHING_WATER
[FarmerTimerManager] 统一状态更新: MOVING -> FETCHING_WATER
[Farmer] 统一状态变更: MOVING -> FETCHING_WATER
[WorkerCharacter] 侧面评分 - 左侧: 55.17, 右侧: 95.17, 选择: right
[WorkerCharacter] 智能选择交互侧面: right (目标: Well)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1156.0, 224.0), 侧面: right
[BaseInteractionManager] 开始交互: fetch_water, 目标: Well, 距离: 96.7, 目标位置: (1156.0, 224.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1156.0, 224.0), 侧面: right
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: FETCHING_WATER -> MOVING
[FarmerInteractionManager:@Node2D@597] 统一状态更新: FETCHING_WATER -> MOVING
[FarmerTimerManager] 统一状态更新: FETCHING_WATER -> MOVING
[Farmer] 统一状态变更: FETCHING_WATER -> MOVING
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[BaseTaskManager] 生成随机值: 0.086，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (590.9023, 162.3582)
[BaseTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.276，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (479.3093, 14.17953)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.425，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (426.8676, 181.3293)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[BaseTaskManager] 生成随机值: 0.721，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 4.7秒
[BaseTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 4.7秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 4.7秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 4.7秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.476，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (439.9473, 155.6264)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[BaseTaskManager] 生成随机值: 0.020，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (712.9674, 7.989716)
[BaseTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.877，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 3.9秒
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 3.9秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 3.9秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 3.9秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.292，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (493.3818, 84.64458)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerInteractionManager:@Node2D@612] 成功浇水 5.0 单位
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 5)
[WorkerCharacter] 动画切换: collecting -> collecting (状态: COLLECTING)
[FarmerTaskManager] 统一状态更新: MOVING -> FETCHING_WATER
[FarmerInteractionManager:@Node2D@612] 统一状态更新: MOVING -> FETCHING_WATER
[FarmerTimerManager] 统一状态更新: MOVING -> FETCHING_WATER
[Farmer] 统一状态变更: MOVING -> FETCHING_WATER
[WorkerCharacter] 侧面评分 - 左侧: 55.58, 右侧: 95.58, 选择: right
[WorkerCharacter] 智能选择交互侧面: right (目标: Well)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1156.0, 224.0), 侧面: right
[BaseInteractionManager] 开始交互: fetch_water, 目标: Well, 距离: 88.4, 目标位置: (1156.0, 224.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1156.0, 224.0), 侧面: right
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: FETCHING_WATER -> MOVING
[FarmerInteractionManager:@Node2D@612] 统一状态更新: FETCHING_WATER -> MOVING
[FarmerTimerManager] 统一状态更新: FETCHING_WATER -> MOVING
[Farmer] 统一状态变更: FETCHING_WATER -> MOVING
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: fetch_water, 目标: Well
[WorkerCharacter] 动画切换: collecting -> collecting (状态: COLLECTING)
[FarmerInteractionManager] 启动动作计时器: fetch_water_action, 预期时长: 2.0秒
[FarmerTimerManager] 使用预设动作时间: fetch_water_action = 2.0秒
[FarmerTimerManager] 🔧 计时器已启动: fetch_water_action, 实际时长: 2.0秒
[BaseInteractionManager] 交互执行结果: true
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[BaseTaskManager] 生成随机值: 0.769，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 4.7秒
[BaseTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 4.7秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 4.7秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 4.7秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.111，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (536.4355, 215.1165)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerInteractionManager:Farmer] 使用装备水桶容量取水: 5.0 (基础: 5.0 + 装备: 0)
[水井] 农夫请求取水: 5.0 单位，直接提供
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[FarmerTaskManager] 统一状态更新: MOVING -> CARRYING
[FarmerInteractionManager:Farmer] 统一状态更新: MOVING -> CARRYING
[FarmerTimerManager] 统一状态更新: MOVING -> CARRYING
[Farmer] 统一状态变更: MOVING -> CARRYING
[Farmer] 尝试显示携带图标，资源类型: water
[Farmer] DataManager返回的图标数据: {  }
[Farmer] 警告：无法找到任何携带图标，资源类型: water
[FarmerInteractionManager:Farmer] 成功取水 5.0 单位 (请求: 5.0)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 6)
[FarmerTaskManager] 进入空闲状态 - 准备新任务决策
[FarmerTaskManager] 统一状态更新: CARRYING -> IDLE
[FarmerInteractionManager:Farmer] 统一状态更新: CARRYING -> IDLE
[FarmerTimerManager] 统一状态更新: CARRYING -> IDLE
[Farmer] 统一状态变更: CARRYING -> IDLE
[WorkerCharacter] 侧面评分 - 左侧: 96.56, 右侧: 54.80, 选择: left
[WorkerCharacter] 智能选择交互侧面: left (目标: Farmland)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1212.0, 184.0), 侧面: left
[BaseInteractionManager] 开始交互: water, 目标: Farmland, 距离: 68.8, 目标位置: (1212.0, 184.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1212.0, 184.0), 侧面: left
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: IDLE -> MOVING
[FarmerInteractionManager:Farmer] 统一状态更新: IDLE -> MOVING
[FarmerTimerManager] 统一状态更新: IDLE -> MOVING
[Farmer] 统一状态变更: IDLE -> MOVING
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[FarmerInteractionManager:@Node2D@604] 使用装备水桶容量取水: 5.0 (基础: 5.0 + 装备: 0)
[水井] 农夫请求取水: 5.0 单位，直接提供
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[FarmerTaskManager] 统一状态更新: MOVING -> CARRYING
[FarmerInteractionManager:@Node2D@604] 统一状态更新: MOVING -> CARRYING
[FarmerTimerManager] 统一状态更新: MOVING -> CARRYING
[Farmer] 统一状态变更: MOVING -> CARRYING
[Farmer] 尝试显示携带图标，资源类型: water
[Farmer] DataManager返回的图标数据: {  }
[Farmer] 警告：无法找到任何携带图标，资源类型: water
[FarmerInteractionManager:@Node2D@604] 成功取水 5.0 单位 (请求: 5.0)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 6)
[FarmerTaskManager] 进入空闲状态 - 准备新任务决策
[FarmerTaskManager] 统一状态更新: CARRYING -> IDLE
[FarmerInteractionManager:@Node2D@604] 统一状态更新: CARRYING -> IDLE
[FarmerTimerManager] 统一状态更新: CARRYING -> IDLE
[Farmer] 统一状态变更: CARRYING -> IDLE
[WorkerCharacter] 侧面评分 - 左侧: 97.17, 右侧: 55.18, 选择: left
[WorkerCharacter] 智能选择交互侧面: left (目标: Farmland)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1212.0, 216.0), 侧面: left
[BaseInteractionManager] 开始交互: water, 目标: Farmland, 距离: 56.6, 目标位置: (1212.0, 216.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1212.0, 216.0), 侧面: left
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: IDLE -> MOVING
[FarmerInteractionManager:@Node2D@604] 统一状态更新: IDLE -> MOVING
[FarmerTimerManager] 统一状态更新: IDLE -> MOVING
[Farmer] 统一状态变更: IDLE -> MOVING
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.627，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (452.5582, 238.6103)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: fetch_water, 目标: Well
[WorkerCharacter] 动画切换: collecting -> collecting (状态: COLLECTING)
[FarmerInteractionManager] 启动动作计时器: fetch_water_action, 预期时长: 2.0秒
[FarmerTimerManager] 使用预设动作时间: fetch_water_action = 2.0秒
[FarmerTimerManager] 🔧 计时器已启动: fetch_water_action, 实际时长: 2.0秒
[BaseInteractionManager] 交互执行结果: true
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: water, 目标: Farmland
[WorkerCharacter] 动画切换: water -> water (状态: WORKING_3)
[FarmerInteractionManager] 启动动作计时器: water_action, 预期时长: 1.2秒
[FarmerTimerManager] 使用预设动作时间: water_action = 1.2秒
[FarmerTimerManager] 🔧 计时器已启动: water_action, 实际时长: 1.2秒
[BaseInteractionManager] 交互执行结果: true
[Crop-wheat] [DEBUG] 作物 '小麦' 进入新阶段: growing1
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.636，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (466.3978, 151.3203)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: water, 目标: Farmland
[WorkerCharacter] 动画切换: water -> water (状态: WORKING_3)
[FarmerInteractionManager] 启动动作计时器: water_action, 预期时长: 1.2秒
[FarmerTimerManager] 使用预设动作时间: water_action = 1.2秒
[FarmerTimerManager] 🔧 计时器已启动: water_action, 实际时长: 1.2秒
[BaseInteractionManager] 交互执行结果: true
[Crop-wheat] [DEBUG] 作物 '小麦' 进入新阶段: growing1
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.334，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (384.9073, 94.54559)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.105，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (477.0865, 302.5297)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.859，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 3.4秒
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 3.4秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 3.4秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 3.4秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[FarmerInteractionManager:@Node2D@597] 使用装备水桶容量取水: 5.0 (基础: 5.0 + 装备: 0)
[水井] 农夫请求取水: 5.0 单位，直接提供
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[FarmerTaskManager] 统一状态更新: MOVING -> CARRYING
[FarmerInteractionManager:@Node2D@597] 统一状态更新: MOVING -> CARRYING
[FarmerTimerManager] 统一状态更新: MOVING -> CARRYING
[Farmer] 统一状态变更: MOVING -> CARRYING
[Farmer] 尝试显示携带图标，资源类型: water
[Farmer] DataManager返回的图标数据: {  }
[Farmer] 警告：无法找到任何携带图标，资源类型: water
[FarmerInteractionManager:@Node2D@597] 成功取水 5.0 单位 (请求: 5.0)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 6)
[FarmerTaskManager] 进入空闲状态 - 准备新任务决策
[FarmerTaskManager] 统一状态更新: CARRYING -> IDLE
[FarmerInteractionManager:@Node2D@597] 统一状态更新: CARRYING -> IDLE
[FarmerTimerManager] 统一状态更新: CARRYING -> IDLE
[Farmer] 统一状态变更: CARRYING -> IDLE
[WorkerCharacter] 侧面评分 - 左侧: 95.17, 右侧: 53.29, 选择: left
[WorkerCharacter] 智能选择交互侧面: left (目标: Farmland)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1244.0, 184.0), 侧面: left
[BaseInteractionManager] 开始交互: water, 目标: Farmland, 距离: 96.7, 目标位置: (1244.0, 184.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1244.0, 184.0), 侧面: left
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: IDLE -> MOVING
[FarmerInteractionManager:@Node2D@597] 统一状态更新: IDLE -> MOVING
[FarmerTimerManager] 统一状态更新: IDLE -> MOVING
[Farmer] 统一状态变更: IDLE -> MOVING
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[FarmerInteractionManager:@Node2D@604] 成功浇水 5.0 单位
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.136，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1173.55, 111.9188)
[BaseTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerInteractionManager:Farmer] 成功浇水 5.0 单位
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.948，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 2.0秒
[BaseTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 2.0秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 2.0秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 2.0秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.912，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 2.5秒
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 2.5秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 2.5秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 2.5秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[Crop-wheat] [DEBUG] 作物 '小麦' 进入新阶段: growing1
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.129，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (306.049, 131.1627)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerInteractionManager:@Node2D@612] 使用装备水桶容量取水: 5.0 (基础: 5.0 + 装备: 0)
[水井] 农夫请求取水: 5.0 单位，直接提供
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[FarmerTaskManager] 统一状态更新: MOVING -> CARRYING
[FarmerInteractionManager:@Node2D@612] 统一状态更新: MOVING -> CARRYING
[FarmerTimerManager] 统一状态更新: MOVING -> CARRYING
[Farmer] 统一状态变更: MOVING -> CARRYING
[Farmer] 尝试显示携带图标，资源类型: water
[Farmer] DataManager返回的图标数据: {  }
[Farmer] 警告：无法找到任何携带图标，资源类型: water
[FarmerInteractionManager:@Node2D@612] 成功取水 5.0 单位 (请求: 5.0)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 6)
[FarmerTaskManager] 进入空闲状态 - 准备新任务决策
[FarmerTaskManager] 统一状态更新: CARRYING -> IDLE
[FarmerInteractionManager:@Node2D@612] 统一状态更新: CARRYING -> IDLE
[FarmerTimerManager] 统一状态更新: CARRYING -> IDLE
[Farmer] 统一状态变更: CARRYING -> IDLE
[WorkerCharacter] 侧面评分 - 左侧: 95.58, 右侧: 53.59, 选择: left
[WorkerCharacter] 智能选择交互侧面: left (目标: Farmland)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1244.0, 216.0), 侧面: left
[BaseInteractionManager] 开始交互: water, 目标: Farmland, 距离: 88.4, 目标位置: (1244.0, 216.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1244.0, 216.0), 侧面: left
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: IDLE -> MOVING
[FarmerInteractionManager:@Node2D@612] 统一状态更新: IDLE -> MOVING
[FarmerTimerManager] 统一状态更新: IDLE -> MOVING
[Farmer] 统一状态变更: IDLE -> MOVING
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: water, 目标: Farmland
[WorkerCharacter] 动画切换: water -> water (状态: WORKING_3)
[FarmerInteractionManager] 启动动作计时器: water_action, 预期时长: 1.2秒
[FarmerTimerManager] 使用预设动作时间: water_action = 1.2秒
[FarmerTimerManager] 🔧 计时器已启动: water_action, 实际时长: 1.2秒
[BaseInteractionManager] 交互执行结果: true
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[BaseTaskManager] 生成随机值: 0.763，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 4.8秒
[BaseTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 4.8秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 4.8秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 4.8秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[Crop-wheat] [DEBUG] 作物 '小麦' 进入新阶段: growing1
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.812，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 3.3秒
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 3.3秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 3.3秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 3.3秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.833，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 3.0秒
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 3.0秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 3.0秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 3.0秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[BaseTaskManager] 生成随机值: 0.129，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (725.3992, 69.77922)
[BaseTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: water, 目标: Farmland
[WorkerCharacter] 动画切换: water -> water (状态: WORKING_3)
[FarmerInteractionManager] 启动动作计时器: water_action, 预期时长: 1.2秒
[FarmerTimerManager] 使用预设动作时间: water_action = 1.2秒
[FarmerTimerManager] 🔧 计时器已启动: water_action, 实际时长: 1.2秒
[BaseInteractionManager] 交互执行结果: true
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[BaseTaskManager] 生成随机值: 0.079，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1285.838, 259.6732)
[BaseTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerInteractionManager:@Node2D@597] 成功浇水 5.0 单位
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.703，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 2.5秒
[BaseTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 2.5秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 2.5秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 2.5秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[BaseTaskManager] 生成随机值: 0.563，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (683.1752, 133.6189)
[BaseTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.654，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (721.6849, 184.774)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[Crop-wheat] [DEBUG] 作物 '小麦' 进入新阶段: growing2
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[BaseTaskManager] 生成随机值: 0.075，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (477.4716, 73.76866)
[BaseTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[Crop-wheat] [DEBUG] 作物 '小麦' 进入新阶段: growing2
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[BaseTaskManager] 生成随机值: 0.907，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 4.0秒
[BaseTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 4.0秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 4.0秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 4.0秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[FarmerInteractionManager:@Node2D@612] 成功浇水 5.0 单位
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.582，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1257.892, 75.49223)
[BaseTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.471，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (730.2581, 100.0885)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.267，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1419.313, 233.7235)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.998，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 2.6秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 2.6秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 2.6秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 2.6秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.630，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (778.1498, 220.8208)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[Crop-wheat] [DEBUG] 作物 '小麦' 进入新阶段: growing2
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.766，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 3.6秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 3.6秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 3.6秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 3.6秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.448，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (267.3611, 11.6226)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.974，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 2.7秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 2.7秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 2.7秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 2.7秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.828，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 3.8秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 3.8秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 3.8秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 3.8秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.195，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1246.917, 136.0892)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.557，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1381.096, 117.1667)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[Crop-wheat] [DEBUG] 作物 '小麦' 进入新阶段: growing2
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.318，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (848.9592, 180.2605)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.884，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 3.0秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 3.0秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 3.0秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 3.0秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.604，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1292.63, 94.13153)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.963，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 4.9秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 4.9秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 4.9秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 4.9秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.048，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (794.1637, 149.1377)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.907，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 2.9秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 2.9秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 2.9秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 2.9秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[Crop-wheat] [DEBUG] 作物 '小麦' 进入新阶段: mature
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.253，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1390.181, 1.050034)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.513，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1446.459, 177.4127)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[Crop-wheat] [DEBUG] 作物 '小麦' 进入新阶段: mature
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.919，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 3.2秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 3.2秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 3.2秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 3.2秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.453，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (806.307, 48.12259)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.230，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1471.118, 206.341)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.394，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1304.443, 211.315)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.333，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1159.224, 191.2282)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.284，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (809.2452, 124.4135)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.558，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1443.912, 171.7299)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[Crop-wheat] [DEBUG] 作物 '小麦' 进入新阶段: mature
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.394，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (939.0667, 95.01877)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.009，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1297.771, 154.7746)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.429，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (225.3559, -10.57925)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.315，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1287.918, 119.2061)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.388，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1134.569, 41.25235)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.612，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (736.4226, 131.8936)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[Crop-wheat] [DEBUG] 作物 '小麦' 进入新阶段: mature
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.558，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (214.0315, -10.05417)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.543，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1043.783, 29.06039)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.141，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (226.4212, 201.1929)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.198，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (451.6682, 133.3073)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.319，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1314.465, 90.80437)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.855，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 3.2秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 3.2秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 3.2秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 3.2秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.019，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (707.6301, 51.57904)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.616，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (908.6118, 151.0565)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.210，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (392.0757, 145.3795)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[Crop-wheat] [DEBUG] 作物 '小麦' 已成熟!
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 1)
[WorkerCharacter] 动画切换: harvest -> harvest (状态: WORKING_1)
[FarmerTaskManager] 进入收获状态 - 调整任务优先级
[FarmerTaskManager] 统一状态更新: MOVING -> HARVESTING
[FarmerInteractionManager] 准备harvesting交互模式
[FarmerInteractionManager:@Node2D@621] 统一状态更新: MOVING -> HARVESTING
[FarmerTimerManager] 统一状态更新: MOVING -> HARVESTING
[Farmer] 统一状态变更: MOVING -> HARVESTING
[WorkerCharacter] 侧面评分 - 左侧: 85.00, 右侧: 45.00, 选择: left
[WorkerCharacter] 智能选择交互侧面: left (目标: Farmland)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1212.0, 184.0), 侧面: left
[BaseInteractionManager] 开始交互: harvest, 目标: Farmland, 距离: 311.4, 目标位置: (1212.0, 184.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1212.0, 184.0), 侧面: left
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: HARVESTING -> MOVING
[FarmerInteractionManager:@Node2D@621] 统一状态更新: HARVESTING -> MOVING
[FarmerTimerManager] 统一状态更新: HARVESTING -> MOVING
[Farmer] 统一状态变更: HARVESTING -> MOVING
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.555，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (545.7126, 255.1746)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.611，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1334.115, 236.8344)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[Crop-wheat] [DEBUG] 作物 '小麦' 已成熟!
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 1)
[WorkerCharacter] 动画切换: harvest -> harvest (状态: WORKING_1)
[FarmerTaskManager] 进入收获状态 - 调整任务优先级
[FarmerTaskManager] 统一状态更新: MOVING -> HARVESTING
[FarmerInteractionManager] 准备harvesting交互模式
[FarmerInteractionManager:@Node2D@631] 统一状态更新: MOVING -> HARVESTING
[FarmerTimerManager] 统一状态更新: MOVING -> HARVESTING
[Farmer] 统一状态变更: MOVING -> HARVESTING
[WorkerCharacter] 侧面评分 - 左侧: 85.00, 右侧: 45.00, 选择: left
[WorkerCharacter] 智能选择交互侧面: left (目标: Farmland)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1212.0, 216.0), 侧面: left
[BaseInteractionManager] 开始交互: harvest, 目标: Farmland, 距离: 823.0, 目标位置: (1212.0, 216.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1212.0, 216.0), 侧面: left
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: HARVESTING -> MOVING
[FarmerInteractionManager:@Node2D@631] 统一状态更新: HARVESTING -> MOVING
[FarmerTimerManager] 统一状态更新: HARVESTING -> MOVING
[Farmer] 统一状态变更: HARVESTING -> MOVING
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.277，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1211.689, 188.7186)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.482，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (746.5778, 87.72415)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.777，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 4.8秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 4.8秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 4.8秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 4.8秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.749，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 4.3秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 4.3秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 4.3秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 4.3秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.253，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (448.1329, 246.3175)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[Crop-wheat] [DEBUG] 作物 '小麦' 已成熟!
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 1)
[WorkerCharacter] 动画切换: harvest -> harvest (状态: WORKING_1)
[FarmerTaskManager] 进入收获状态 - 调整任务优先级
[FarmerTaskManager] 统一状态更新: MOVING -> HARVESTING
[FarmerInteractionManager] 准备harvesting交互模式
[FarmerInteractionManager:@Node2D@597] 统一状态更新: MOVING -> HARVESTING
[FarmerTimerManager] 统一状态更新: MOVING -> HARVESTING
[Farmer] 统一状态变更: MOVING -> HARVESTING
[WorkerCharacter] 侧面评分 - 左侧: 54.78, 右侧: 96.36, 选择: right
[WorkerCharacter] 智能选择交互侧面: right (目标: Farmland)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1284.0, 184.0), 侧面: right
[BaseInteractionManager] 开始交互: harvest, 目标: Farmland, 距离: 72.8, 目标位置: (1284.0, 184.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1284.0, 184.0), 侧面: right
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: HARVESTING -> MOVING
[FarmerInteractionManager:@Node2D@597] 统一状态更新: HARVESTING -> MOVING
[FarmerTimerManager] 统一状态更新: HARVESTING -> MOVING
[Farmer] 统一状态变更: HARVESTING -> MOVING
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.014，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (435.3024, 147.9759)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.807，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 4.4秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 4.4秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 4.4秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 4.4秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[Crop-wheat] [DEBUG] 作物 '小麦' 已成熟!
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 1)
[WorkerCharacter] 动画切换: harvest -> harvest (状态: WORKING_1)
[FarmerTaskManager] 进入收获状态 - 调整任务优先级
[FarmerTaskManager] 统一状态更新: MOVING -> HARVESTING
[FarmerInteractionManager] 准备harvesting交互模式
[FarmerInteractionManager:Farmer] 统一状态更新: MOVING -> HARVESTING
[FarmerTimerManager] 统一状态更新: MOVING -> HARVESTING
[Farmer] 统一状态变更: MOVING -> HARVESTING
[WorkerCharacter] 侧面评分 - 左侧: 55.93, 右侧: 96.86, 选择: right
[WorkerCharacter] 智能选择交互侧面: right (目标: Farmland)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1284.0, 216.0), 侧面: right
[BaseInteractionManager] 开始交互: harvest, 目标: Farmland, 距离: 62.8, 目标位置: (1284.0, 216.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1284.0, 216.0), 侧面: right
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: HARVESTING -> MOVING
[FarmerInteractionManager:Farmer] 统一状态更新: HARVESTING -> MOVING
[FarmerTimerManager] 统一状态更新: HARVESTING -> MOVING
[Farmer] 统一状态变更: HARVESTING -> MOVING
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: harvest, 目标: Farmland
[WorkerCharacter] 动画切换: harvest -> harvest (状态: WORKING_1)
[FarmerInteractionManager] 启动动作计时器: harvest_action, 预期时长: 1.8秒
[FarmerTimerManager] 使用预设动作时间: harvest_action = 1.8秒
[FarmerTimerManager] 🔧 计时器已启动: harvest_action, 实际时长: 1.8秒
[BaseInteractionManager] 交互执行结果: true
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.343，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1035.325, 143.0856)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.285，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (902.3185, 175.1923)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: harvest, 目标: Farmland
[WorkerCharacter] 动画切换: harvest -> harvest (状态: WORKING_1)
[FarmerInteractionManager] 启动动作计时器: harvest_action, 预期时长: 1.8秒
[FarmerTimerManager] 使用预设动作时间: harvest_action = 1.8秒
[FarmerTimerManager] 🔧 计时器已启动: harvest_action, 实际时长: 1.8秒
[BaseInteractionManager] 交互执行结果: true
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.386，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (492.489, 151.7899)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.072，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1096.88, 175.7341)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.336，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (543.3572, 209.3364)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[Crop-wheat] [DEBUG] 作物 '小麦' 被收获，产出: wheat_item x2
[Crop-wheat] [DEBUG] 作物 '小麦' 被移除。玩家操作: false
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
CoreTech: 动作连贯性 - 立即请求收集任务
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 3)
[WorkerCharacter] 侧面评分 - 左侧: 35.00, 右侧: 75.00, 选择: right
[WorkerCharacter] 智能选择交互侧面: right (目标: DroppedItem_wheat_item)
[WorkerCharacter] 使用降级位置: (1294.0, 174.9)
[BaseInteractionManager] 开始交互: collect, 目标: DroppedItem_wheat_item, 距离: 13.5, 目标位置: (1294.0, 174.9)
[WorkerCharacter] 使用降级位置: (1294.0, 174.9)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: harvest, 目标: Farmland
[WorkerCharacter] 动画切换: harvest -> harvest (状态: WORKING_1)
[FarmerInteractionManager] 启动动作计时器: harvest_action, 预期时长: 1.8秒
[FarmerTimerManager] 使用预设动作时间: harvest_action = 1.8秒
[FarmerTimerManager] 🔧 计时器已启动: harvest_action, 实际时长: 1.8秒
[BaseInteractionManager] 交互执行结果: true
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: collect, 目标: DroppedItem_wheat_item
🔍 [农夫] 作物类型检查: wheat_item -> wheat = true
[WorkerCharacter] 动画切换: collecting -> collecting (状态: COLLECTING)
[FarmerInteractionManager] 启动动作计时器: collect_action, 预期时长: 1.5秒
[FarmerTimerManager] 使用预设动作时间: collect_action = 1.5秒
[FarmerTimerManager] 🔧 计时器已启动: collect_action, 实际时长: 1.5秒
[BaseInteractionManager] 交互执行结果: true
[Crop-wheat] [DEBUG] 作物 '小麦' 被收获，产出: wheat_item x3
[Crop-wheat] [DEBUG] 作物 '小麦' 被移除。玩家操作: false
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
CoreTech: 动作连贯性 - 立即请求收集任务
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 3)
[WorkerCharacter] 侧面评分 - 左侧: 35.00, 右侧: 75.00, 选择: right
[WorkerCharacter] 智能选择交互侧面: right (目标: DroppedItem_wheat_item)
[WorkerCharacter] 使用降级位置: (1294.0, 206.9)
[BaseInteractionManager] 开始交互: collect, 目标: DroppedItem_wheat_item, 距离: 13.5, 目标位置: (1294.0, 206.9)
[WorkerCharacter] 使用降级位置: (1294.0, 206.9)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.433，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (245.114, 255.8553)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.638，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (975.5935, 172.4361)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.698，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (610.2222, 100.6746)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: collect, 目标: DroppedItem_wheat_item
🔍 [农夫] 作物类型检查: wheat_item -> wheat = true
[WorkerCharacter] 动画切换: collecting -> collecting (状态: COLLECTING)
[FarmerInteractionManager] 启动动作计时器: collect_action, 预期时长: 1.5秒
[FarmerTimerManager] 使用预设动作时间: collect_action = 1.5秒
[FarmerTimerManager] 🔧 计时器已启动: collect_action, 实际时长: 1.5秒
[BaseInteractionManager] 交互执行结果: true
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.405，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (609.4727, 37.03682)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.525，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (347.5505, 133.3183)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[FarmerTaskManager] 统一状态更新: MOVING -> CARRYING
[FarmerInteractionManager:@Node2D@597] 统一状态更新: MOVING -> CARRYING
[FarmerTimerManager] 统一状态更新: MOVING -> CARRYING
[Farmer] 统一状态变更: MOVING -> CARRYING
[Farmer] 尝试显示携带图标，资源类型: wheat_item
[Farmer] DataManager返回的图标数据: { "texture_path": "res://assets/textures/crops_spritesheets/all_crops_sheet.png", "region": [P: (32, 240), S: (16, 16)] }
[Farmer] 成功加载Atlas图标: res://assets/textures/crops_spritesheets/all_crops_sheet.png, 区域: [P: (32, 240), S: (16, 16)]
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
CoreTech: 动作连贯性 - 立即请求存储任务
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 4)
[WorkerCharacter] 侧面评分 - 左侧: 96.98, 右侧: 56.20, 选择: left
[WorkerCharacter] 智能选择交互侧面: left (目标: Storage)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1352.0, 192.0), 侧面: left
[BaseInteractionManager] 开始交互: store, 目标: Storage, 距离: 60.5, 目标位置: (1352.0, 192.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1352.0, 192.0), 侧面: left
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: CARRYING -> MOVING
[FarmerInteractionManager:@Node2D@597] 统一状态更新: CARRYING -> MOVING
[FarmerTimerManager] 统一状态更新: CARRYING -> MOVING
[Farmer] 统一状态变更: CARRYING -> MOVING
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.849，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 2.5秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 2.5秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 2.5秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 2.5秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[Crop-wheat] [DEBUG] 作物 '小麦' 被收获，产出: wheat_item x2
[Crop-wheat] [DEBUG] 作物 '小麦' 被移除。玩家操作: false
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
CoreTech: 动作连贯性 - 立即请求收集任务
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 3)
[WorkerCharacter] 侧面评分 - 左侧: 75.00, 右侧: 35.00, 选择: left
[WorkerCharacter] 智能选择交互侧面: left (目标: DroppedItem_wheat_item)
[WorkerCharacter] 使用降级位置: (1202.0, 174.9)
[BaseInteractionManager] 开始交互: collect, 目标: DroppedItem_wheat_item, 距离: 13.5, 目标位置: (1202.0, 174.9)
[WorkerCharacter] 使用降级位置: (1202.0, 174.9)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.155，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (495.6048, 133.2081)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.059，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1076.139, 188.1135)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: collect, 目标: DroppedItem_wheat_item
🔍 [农夫] 作物类型检查: wheat_item -> wheat = true
[WorkerCharacter] 动画切换: collecting -> collecting (状态: COLLECTING)
[FarmerInteractionManager] 启动动作计时器: collect_action, 预期时长: 1.5秒
[FarmerTimerManager] 使用预设动作时间: collect_action = 1.5秒
[FarmerTimerManager] 🔧 计时器已启动: collect_action, 实际时长: 1.5秒
[BaseInteractionManager] 交互执行结果: true
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.314，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1317.212, 204.5834)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.919，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 3.1秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 3.1秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 3.1秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 3.1秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[FarmerTaskManager] 统一状态更新: MOVING -> CARRYING
[FarmerInteractionManager:Farmer] 统一状态更新: MOVING -> CARRYING
[FarmerTimerManager] 统一状态更新: MOVING -> CARRYING
[Farmer] 统一状态变更: MOVING -> CARRYING
[Farmer] 尝试显示携带图标，资源类型: wheat_item
[Farmer] DataManager返回的图标数据: { "texture_path": "res://assets/textures/crops_spritesheets/all_crops_sheet.png", "region": [P: (32, 240), S: (16, 16)] }
[Farmer] 成功加载Atlas图标: res://assets/textures/crops_spritesheets/all_crops_sheet.png, 区域: [P: (32, 240), S: (16, 16)]
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
CoreTech: 动作连贯性 - 立即请求存储任务
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 4)
[WorkerCharacter] 侧面评分 - 左侧: 97.01, 右侧: 56.23, 选择: left
[WorkerCharacter] 智能选择交互侧面: left (目标: Storage)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1352.0, 192.0), 侧面: left
[BaseInteractionManager] 开始交互: store, 目标: Storage, 距离: 59.9, 目标位置: (1352.0, 192.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1352.0, 192.0), 侧面: left
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: CARRYING -> MOVING
[FarmerInteractionManager:Farmer] 统一状态更新: CARRYING -> MOVING
[FarmerTimerManager] 统一状态更新: CARRYING -> MOVING
[Farmer] 统一状态变更: CARRYING -> MOVING
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: store, 目标: Storage
[WorkerCharacter] 动画切换: storing -> storing (状态: STORING)
[FarmerInteractionManager] 启动动作计时器: store_action, 预期时长: 1.8秒
[FarmerTimerManager] 使用预设动作时间: store_action = 1.8秒
[FarmerTimerManager] 🔧 计时器已启动: store_action, 实际时长: 1.8秒
[BaseInteractionManager] 交互执行结果: true
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.487，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (412.1558, 133.5366)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: store, 目标: Storage
[WorkerCharacter] 动画切换: storing -> storing (状态: STORING)
[FarmerInteractionManager] 启动动作计时器: store_action, 预期时长: 1.8秒
[FarmerTimerManager] 使用预设动作时间: store_action = 1.8秒
[FarmerTimerManager] 🔧 计时器已启动: store_action, 实际时长: 1.8秒
[BaseInteractionManager] 交互执行结果: true
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.901，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 4.3秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 4.3秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 4.3秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 4.3秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.592，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (457.2465, 176.6098)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[FarmerTaskManager] 统一状态更新: MOVING -> CARRYING
[FarmerInteractionManager:@Node2D@621] 统一状态更新: MOVING -> CARRYING
[FarmerTimerManager] 统一状态更新: MOVING -> CARRYING
[Farmer] 统一状态变更: MOVING -> CARRYING
[Farmer] 尝试显示携带图标，资源类型: wheat_item
[Farmer] DataManager返回的图标数据: { "texture_path": "res://assets/textures/crops_spritesheets/all_crops_sheet.png", "region": [P: (32, 240), S: (16, 16)] }
[Farmer] 成功加载Atlas图标: res://assets/textures/crops_spritesheets/all_crops_sheet.png, 区域: [P: (32, 240), S: (16, 16)]
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
CoreTech: 动作连贯性 - 立即请求存储任务
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 4)
[WorkerCharacter] 侧面评分 - 左侧: 92.45, 右侧: 51.66, 选择: left
[WorkerCharacter] 智能选择交互侧面: left (目标: Storage)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1352.0, 192.0), 侧面: left
[BaseInteractionManager] 开始交互: store, 目标: Storage, 距离: 151.0, 目标位置: (1352.0, 192.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1352.0, 192.0), 侧面: left
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: CARRYING -> MOVING
[FarmerInteractionManager:@Node2D@621] 统一状态更新: CARRYING -> MOVING
[FarmerTimerManager] 统一状态更新: CARRYING -> MOVING
[Farmer] 统一状态变更: CARRYING -> MOVING
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.810，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 2.0秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 2.0秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 2.0秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 2.0秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.711，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 3.7秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 3.7秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 3.7秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 3.7秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.125，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1092.589, 32.65585)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.663，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (524.4862, 74.01012)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[厨房-5] [仓库] 存储资源: wheat x2 (实际增加: 2)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
CoreTech: 动作连贯性 - 完成工作流程循环
[FarmerTaskManager] 进入空闲状态 - 准备新任务决策
[FarmerTaskManager] 统一状态更新: MOVING -> IDLE
[FarmerInteractionManager:@Node2D@597] 统一状态更新: MOVING -> IDLE
[FarmerTimerManager] 统一状态更新: MOVING -> IDLE
[Farmer] 统一状态变更: MOVING -> IDLE
[TradingPost] 商队已抵达，可以进行交易
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 2)
[WorkerCharacter] 动画切换: plant -> plant (状态: WORKING_2)
[FarmerTaskManager] 进入种植状态 - 调整任务优先级
[FarmerTaskManager] 统一状态更新: IDLE -> PLANTING
[FarmerInteractionManager] 准备planting交互模式
[FarmerInteractionManager:@Node2D@597] 统一状态更新: IDLE -> PLANTING
[FarmerTimerManager] 统一状态更新: IDLE -> PLANTING
[Farmer] 统一状态变更: IDLE -> PLANTING
[WorkerCharacter] 侧面评分 - 左侧: 54.59, 右侧: 96.58, 选择: right
[WorkerCharacter] 智能选择交互侧面: right (目标: Farmland)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1284.0, 184.0), 侧面: right
[BaseInteractionManager] 开始交互: plant, 目标: Farmland, 距离: 68.5, 目标位置: (1284.0, 184.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1284.0, 184.0), 侧面: right
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: PLANTING -> MOVING
[FarmerInteractionManager:@Node2D@597] 统一状态更新: PLANTING -> MOVING
[FarmerTimerManager] 统一状态更新: PLANTING -> MOVING
[Farmer] 统一状态变更: PLANTING -> MOVING
[厨房-5] [仓库] 存储资源: wheat x3 (实际增加: 3)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
CoreTech: 动作连贯性 - 完成工作流程循环
[FarmerTaskManager] 进入空闲状态 - 准备新任务决策
[FarmerTaskManager] 统一状态更新: MOVING -> IDLE
[FarmerInteractionManager:Farmer] 统一状态更新: MOVING -> IDLE
[FarmerTimerManager] 统一状态更新: MOVING -> IDLE
[Farmer] 统一状态变更: MOVING -> IDLE
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 2)
[WorkerCharacter] 动画切换: plant -> plant (状态: WORKING_2)
[FarmerTaskManager] 进入种植状态 - 调整任务优先级
[FarmerTaskManager] 统一状态更新: IDLE -> PLANTING
[FarmerInteractionManager] 准备planting交互模式
[FarmerInteractionManager:Farmer] 统一状态更新: IDLE -> PLANTING
[FarmerTimerManager] 统一状态更新: IDLE -> PLANTING
[Farmer] 统一状态变更: IDLE -> PLANTING
[WorkerCharacter] 侧面评分 - 左侧: 54.47, 右侧: 96.39, 选择: right
[WorkerCharacter] 智能选择交互侧面: right (目标: Farmland)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1284.0, 216.0), 侧面: right
[BaseInteractionManager] 开始交互: plant, 目标: Farmland, 距离: 72.1, 目标位置: (1284.0, 216.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1284.0, 216.0), 侧面: right
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: PLANTING -> MOVING
[FarmerInteractionManager:Farmer] 统一状态更新: PLANTING -> MOVING
[FarmerTimerManager] 统一状态更新: PLANTING -> MOVING
[Farmer] 统一状态变更: PLANTING -> MOVING
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: plant, 目标: Farmland
[WorkerCharacter] 动画切换: plant -> plant (状态: WORKING_2)
[FarmerInteractionManager] 启动动作计时器: plant_action, 预期时长: 1.5秒
[FarmerTimerManager] 使用预设动作时间: plant_action = 1.5秒
[FarmerTimerManager] 🔧 计时器已启动: plant_action, 实际时长: 1.5秒
[BaseInteractionManager] 交互执行结果: true
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.649，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (622.2548, 5.808861)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: store, 目标: Storage
[WorkerCharacter] 动画切换: storing -> storing (状态: STORING)
[FarmerInteractionManager] 启动动作计时器: store_action, 预期时长: 1.8秒
[FarmerTimerManager] 使用预设动作时间: store_action = 1.8秒
[FarmerTimerManager] 🔧 计时器已启动: store_action, 实际时长: 1.8秒
[BaseInteractionManager] 交互执行结果: true
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.000，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1287.464, 75.28616)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.155，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (753.792, 62.52359)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.956，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 2.1秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 2.1秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 2.1秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 2.1秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: plant, 目标: Farmland
[WorkerCharacter] 动画切换: plant -> plant (状态: WORKING_2)
[FarmerInteractionManager] 启动动作计时器: plant_action, 预期时长: 1.5秒
[FarmerTimerManager] 使用预设动作时间: plant_action = 1.5秒
[FarmerTimerManager] 🔧 计时器已启动: plant_action, 实际时长: 1.5秒
[BaseInteractionManager] 交互执行结果: true
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: harvest, 目标: Farmland
[WorkerCharacter] 动画切换: harvest -> harvest (状态: WORKING_1)
[FarmerInteractionManager] 启动动作计时器: harvest_action, 预期时长: 1.8秒
[FarmerTimerManager] 使用预设动作时间: harvest_action = 1.8秒
[FarmerTimerManager] 🔧 计时器已启动: harvest_action, 实际时长: 1.8秒
[BaseInteractionManager] 交互执行结果: true
[Crop-wheat] [DEBUG] 作物初始化完成: 小麦
[FarmerInteractionManager:@Node2D@597] 成功种植作物: wheat
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.206，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1226.009, 164.5058)
[BaseTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[厨房-5] [仓库] 存储资源: wheat x2 (实际增加: 2)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
CoreTech: 动作连贯性 - 完成工作流程循环
[FarmerTaskManager] 进入空闲状态 - 准备新任务决策
[FarmerTaskManager] 统一状态更新: MOVING -> IDLE
[FarmerInteractionManager:@Node2D@621] 统一状态更新: MOVING -> IDLE
[FarmerTimerManager] 统一状态更新: MOVING -> IDLE
[Farmer] 统一状态变更: MOVING -> IDLE
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.558，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1013.742, 167.7537)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.940，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 3.8秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 3.8秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 3.8秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 3.8秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 2)
[WorkerCharacter] 动画切换: plant -> plant (状态: WORKING_2)
[FarmerTaskManager] 进入种植状态 - 调整任务优先级
[FarmerTaskManager] 统一状态更新: IDLE -> PLANTING
[FarmerInteractionManager] 准备planting交互模式
[FarmerInteractionManager:@Node2D@621] 统一状态更新: IDLE -> PLANTING
[FarmerTimerManager] 统一状态更新: IDLE -> PLANTING
[Farmer] 统一状态变更: IDLE -> PLANTING
[WorkerCharacter] 侧面评分 - 左侧: 52.99, 右侧: 94.98, 选择: right
[WorkerCharacter] 智能选择交互侧面: right (目标: Farmland)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1252.0, 184.0), 侧面: right
[BaseInteractionManager] 开始交互: plant, 目标: Farmland, 距离: 100.3, 目标位置: (1252.0, 184.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1252.0, 184.0), 侧面: right
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: PLANTING -> MOVING
[FarmerInteractionManager:@Node2D@621] 统一状态更新: PLANTING -> MOVING
[FarmerTimerManager] 统一状态更新: PLANTING -> MOVING
[Farmer] 统一状态变更: PLANTING -> MOVING
[Crop-wheat] [DEBUG] 作物初始化完成: 小麦
[FarmerInteractionManager:Farmer] 成功种植作物: wheat
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.254，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1320.396, 148.9988)
[BaseTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.938，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 4.2秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.527，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (452.57, 166.8328)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 4.2秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 4.2秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 4.2秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.552，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1034.992, 128.6291)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.149，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (414.2613, 162.0552)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.840，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 2.3秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 2.3秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 2.3秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 2.3秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[Crop-wheat] [DEBUG] 作物 '小麦' 被收获，产出: wheat_item x3
[Crop-wheat] [DEBUG] 作物 '小麦' 被移除。玩家操作: false
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
CoreTech: 动作连贯性 - 立即请求收集任务
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.975，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 2.7秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 2.7秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 2.7秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 2.7秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 3)
[WorkerCharacter] 侧面评分 - 左侧: 75.00, 右侧: 35.00, 选择: left
[WorkerCharacter] 智能选择交互侧面: left (目标: DroppedItem_wheat_item)
[WorkerCharacter] 使用降级位置: (1202.0, 206.9)
[BaseInteractionManager] 开始交互: collect, 目标: DroppedItem_wheat_item, 距离: 13.5, 目标位置: (1202.0, 206.9)
[WorkerCharacter] 使用降级位置: (1202.0, 206.9)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.092，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1239.117, 202.3298)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.729，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 4.3秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: collect, 目标: DroppedItem_wheat_item
🔍 [农夫] 作物类型检查: wheat_item -> wheat = true
[WorkerCharacter] 动画切换: collecting -> collecting (状态: COLLECTING)
[FarmerInteractionManager] 启动动作计时器: collect_action, 预期时长: 1.5秒
[FarmerTimerManager] 使用预设动作时间: collect_action = 1.5秒
[FarmerTimerManager] 🔧 计时器已启动: collect_action, 实际时长: 1.5秒
[BaseInteractionManager] 交互执行结果: true
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 4.3秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 4.3秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 4.3秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: plant, 目标: Farmland
[WorkerCharacter] 动画切换: plant -> plant (状态: WORKING_2)
[FarmerInteractionManager] 启动动作计时器: plant_action, 预期时长: 1.5秒
[FarmerTimerManager] 使用预设动作时间: plant_action = 1.5秒
[FarmerTimerManager] 🔧 计时器已启动: plant_action, 实际时长: 1.5秒
[BaseInteractionManager] 交互执行结果: true
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.739，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 4.8秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 4.8秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 4.8秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 4.8秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.632，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (279.2478, 216.4034)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.867，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 3.5秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 3.5秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 3.5秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 3.5秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[FarmerTaskManager] 统一状态更新: MOVING -> CARRYING
[FarmerInteractionManager:@Node2D@631] 统一状态更新: MOVING -> CARRYING
[FarmerTimerManager] 统一状态更新: MOVING -> CARRYING
[Farmer] 统一状态变更: MOVING -> CARRYING
[Farmer] 尝试显示携带图标，资源类型: wheat_item
[Farmer] DataManager返回的图标数据: { "texture_path": "res://assets/textures/crops_spritesheets/all_crops_sheet.png", "region": [P: (32, 240), S: (16, 16)] }
[Farmer] 成功加载Atlas图标: res://assets/textures/crops_spritesheets/all_crops_sheet.png, 区域: [P: (32, 240), S: (16, 16)]
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
CoreTech: 动作连贯性 - 立即请求存储任务
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 4)
[WorkerCharacter] 侧面评分 - 左侧: 92.46, 右侧: 51.67, 选择: left
[WorkerCharacter] 智能选择交互侧面: left (目标: Storage)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1352.0, 192.0), 侧面: left
[BaseInteractionManager] 开始交互: store, 目标: Storage, 距离: 150.7, 目标位置: (1352.0, 192.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1352.0, 192.0), 侧面: left
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: CARRYING -> MOVING
[FarmerInteractionManager:@Node2D@631] 统一状态更新: CARRYING -> MOVING
[FarmerTimerManager] 统一状态更新: CARRYING -> MOVING
[Farmer] 统一状态变更: CARRYING -> MOVING
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[Crop-wheat] [DEBUG] 作物初始化完成: 小麦
[FarmerInteractionManager:@Node2D@621] 成功种植作物: wheat
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.156，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1295.22, 125.8055)
[BaseTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.754，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 4.7秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 4.7秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 4.7秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 4.7秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[Crop-wheat] [DEBUG] 作物 '小麦' 进入新阶段: growing1
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.269，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1402.656, 179.4672)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 2)
[WorkerCharacter] 动画切换: plant -> plant (状态: WORKING_2)
[FarmerTaskManager] 进入种植状态 - 调整任务优先级
[FarmerTaskManager] 统一状态更新: MOVING -> PLANTING
[FarmerInteractionManager] 准备planting交互模式
[FarmerInteractionManager:@Node2D@612] 统一状态更新: MOVING -> PLANTING
[FarmerTimerManager] 统一状态更新: MOVING -> PLANTING
[Farmer] 统一状态变更: MOVING -> PLANTING
[WorkerCharacter] 侧面评分 - 左侧: 52.14, 右侧: 92.88, 选择: right
[WorkerCharacter] 智能选择交互侧面: right (目标: Farmland)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1252.0, 216.0), 侧面: right
[BaseInteractionManager] 开始交互: plant, 目标: Farmland, 距离: 142.5, 目标位置: (1252.0, 216.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1252.0, 216.0), 侧面: right
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: PLANTING -> MOVING
[FarmerInteractionManager:@Node2D@612] 统一状态更新: PLANTING -> MOVING
[FarmerTimerManager] 统一状态更新: PLANTING -> MOVING
[Farmer] 统一状态变更: PLANTING -> MOVING
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.306，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (719.868, 166.2582)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[Crop-wheat] [DEBUG] 作物 '小麦' 进入新阶段: growing1
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.664，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (369.8495, 250.0088)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.239，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1117.337, 225.9002)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: store, 目标: Storage
[WorkerCharacter] 动画切换: storing -> storing (状态: STORING)
[FarmerInteractionManager] 启动动作计时器: store_action, 预期时长: 1.8秒
[FarmerTimerManager] 使用预设动作时间: store_action = 1.8秒
[FarmerTimerManager] 🔧 计时器已启动: store_action, 实际时长: 1.8秒
[BaseInteractionManager] 交互执行结果: true
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.426，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (766.3293, 277.2495)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.829，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 3.0秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 3.0秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 3.0秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 3.0秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.532，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (441.6598, 325.9969)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.496，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1001.293, 38.37912)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: plant, 目标: Farmland
[WorkerCharacter] 动画切换: plant -> plant (状态: WORKING_2)
[FarmerInteractionManager] 启动动作计时器: plant_action, 预期时长: 1.5秒
[FarmerTimerManager] 使用预设动作时间: plant_action = 1.5秒
[FarmerTimerManager] 🔧 计时器已启动: plant_action, 实际时长: 1.5秒
[BaseInteractionManager] 交互执行结果: true
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.945，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 4.5秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 4.5秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 4.5秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 4.5秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.319，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1199.14, 151.4283)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.952，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 2.6秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 2.6秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 2.6秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 2.6秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[厨房-5] [仓库] 存储资源: wheat x3 (实际增加: 3)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
CoreTech: 动作连贯性 - 完成工作流程循环
[FarmerTaskManager] 进入空闲状态 - 准备新任务决策
[FarmerTaskManager] 统一状态更新: MOVING -> IDLE
[FarmerInteractionManager:@Node2D@631] 统一状态更新: MOVING -> IDLE
[FarmerTimerManager] 统一状态更新: MOVING -> IDLE
[Farmer] 统一状态变更: MOVING -> IDLE
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.195，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1270.034, 80.81791)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: IDLE -> MOVING
[FarmerInteractionManager:@Node2D@631] 统一状态更新: IDLE -> MOVING
[FarmerTimerManager] 统一状态更新: IDLE -> MOVING
[Farmer] 统一状态变更: IDLE -> MOVING
[Crop-wheat] [DEBUG] 作物 '小麦' 进入新阶段: growing1
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.901，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 2.2秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 2.2秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 2.2秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 2.2秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.221，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (957.8448, 226.4772)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.579，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (838.2986, 132.3369)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[Crop-wheat] [DEBUG] 作物初始化完成: 小麦
[FarmerInteractionManager:@Node2D@612] 成功种植作物: wheat
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.220，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1287.834, 261.3799)
[BaseTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[Crop-wheat] [DEBUG] 作物 '小麦' 进入新阶段: growing2
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.086，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1180.105, 92.83258)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[Crop-wheat] [DEBUG] 作物 '小麦' 进入新阶段: growing2
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.883，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 3.4秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 3.4秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 3.4秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 3.4秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.491，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (397.0274, 290.8055)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.536，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1309.486, 99.58253)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.236，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1381.205, 296.4511)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.185，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1239.953, 143.5511)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.613，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (854.3948, 82.32278)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.451，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (883.8843, 146.4562)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.291，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (381.6136, 357.7333)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.760，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 4.9秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 4.9秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 4.9秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 4.9秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.375，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (342.9953, 152.5433)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.323，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1355.973, 318.3826)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.062，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1347.75, 141.3775)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.724，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 3.7秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 3.7秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 3.7秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 3.7秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.391，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1286.361, 325.8911)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.489，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (329.3033, 184.7265)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 5)
[WorkerCharacter] 动画切换: collecting -> collecting (状态: COLLECTING)
[FarmerTaskManager] 统一状态更新: MOVING -> FETCHING_WATER
[FarmerInteractionManager:@Node2D@597] 统一状态更新: MOVING -> FETCHING_WATER
[FarmerTimerManager] 统一状态更新: MOVING -> FETCHING_WATER
[Farmer] 统一状态变更: MOVING -> FETCHING_WATER
[WorkerCharacter] 侧面评分 - 左侧: 50.12, 右侧: 90.12, 选择: right
[WorkerCharacter] 智能选择交互侧面: right (目标: Well)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1156.0, 224.0), 侧面: right
[BaseInteractionManager] 开始交互: fetch_water, 目标: Well, 距离: 197.6, 目标位置: (1156.0, 224.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1156.0, 224.0), 侧面: right
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: FETCHING_WATER -> MOVING
[FarmerInteractionManager:@Node2D@597] 统一状态更新: FETCHING_WATER -> MOVING
[FarmerTimerManager] 统一状态更新: FETCHING_WATER -> MOVING
[Farmer] 统一状态变更: FETCHING_WATER -> MOVING
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 5)
[WorkerCharacter] 动画切换: collecting -> collecting (状态: COLLECTING)
[FarmerTaskManager] 统一状态更新: MOVING -> FETCHING_WATER
[FarmerInteractionManager:Farmer] 统一状态更新: MOVING -> FETCHING_WATER
[FarmerTimerManager] 统一状态更新: MOVING -> FETCHING_WATER
[Farmer] 统一状态变更: MOVING -> FETCHING_WATER
[WorkerCharacter] 侧面评分 - 左侧: 55.71, 右侧: 95.71, 选择: right
[WorkerCharacter] 智能选择交互侧面: right (目标: Well)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1156.0, 224.0), 侧面: right
[BaseInteractionManager] 开始交互: fetch_water, 目标: Well, 距离: 85.9, 目标位置: (1156.0, 224.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1156.0, 224.0), 侧面: right
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: FETCHING_WATER -> MOVING
[FarmerInteractionManager:Farmer] 统一状态更新: FETCHING_WATER -> MOVING
[FarmerTimerManager] 统一状态更新: FETCHING_WATER -> MOVING
[Farmer] 统一状态变更: FETCHING_WATER -> MOVING
[Crop-wheat] [DEBUG] 作物 '小麦' 进入新阶段: growing2
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.686，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1401.832, 275.0854)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.752，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 5.0秒
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.655，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (331.3308, 31.32185)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 5.0秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 5.0秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 5.0秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.022，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (277.8835, 212.2845)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[Crop-wheat] [DEBUG] 作物 '小麦' 进入新阶段: growing1
[Crop-wheat] [DEBUG] 作物 '小麦' 进入新阶段: mature
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[BaseTaskManager] 生成随机值: 0.395，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1296.388, 167.7074)
[BaseTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.932，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 4.4秒
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 4.4秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 4.4秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 4.4秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: fetch_water, 目标: Well
[WorkerCharacter] 动画切换: collecting -> collecting (状态: COLLECTING)
[FarmerInteractionManager] 启动动作计时器: fetch_water_action, 预期时长: 2.0秒
[FarmerTimerManager] 使用预设动作时间: fetch_water_action = 2.0秒
[FarmerTimerManager] 🔧 计时器已启动: fetch_water_action, 实际时长: 2.0秒
[BaseInteractionManager] 交互执行结果: true
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.487，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (352.7098, 131.2535)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[Crop-wheat] [DEBUG] 作物 '小麦' 进入新阶段: mature
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.022，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (396.9604, 198.0668)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.675，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1345.819, 367.9589)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: fetch_water, 目标: Well
[WorkerCharacter] 动画切换: collecting -> collecting (状态: COLLECTING)
[FarmerInteractionManager] 启动动作计时器: fetch_water_action, 预期时长: 2.0秒
[FarmerTimerManager] 使用预设动作时间: fetch_water_action = 2.0秒
[FarmerTimerManager] 🔧 计时器已启动: fetch_water_action, 实际时长: 2.0秒
[BaseInteractionManager] 交互执行结果: true
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.841，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 2.9秒
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 2.9秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 2.9秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 2.9秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.605，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1304.716, 381.2651)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.163，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (423.7591, 187.0464)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[BaseTaskManager] 生成随机值: 0.341，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (912.8462, 93.89505)
[BaseTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 5)
[WorkerCharacter] 动画切换: collecting -> collecting (状态: COLLECTING)
[FarmerTaskManager] 统一状态更新: MOVING -> FETCHING_WATER
[FarmerInteractionManager:@Node2D@621] 统一状态更新: MOVING -> FETCHING_WATER
[FarmerTimerManager] 统一状态更新: MOVING -> FETCHING_WATER
[Farmer] 统一状态变更: MOVING -> FETCHING_WATER
[WorkerCharacter] 侧面评分 - 左侧: 52.45, 右侧: 92.45, 选择: right
[WorkerCharacter] 智能选择交互侧面: right (目标: Well)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1156.0, 224.0), 侧面: right
[BaseInteractionManager] 开始交互: fetch_water, 目标: Well, 距离: 151.0, 目标位置: (1156.0, 224.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1156.0, 224.0), 侧面: right
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: FETCHING_WATER -> MOVING
[FarmerInteractionManager:@Node2D@621] 统一状态更新: FETCHING_WATER -> MOVING
[FarmerTimerManager] 统一状态更新: FETCHING_WATER -> MOVING
[Farmer] 统一状态变更: FETCHING_WATER -> MOVING
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.007，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (321.6423, 115.4791)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerInteractionManager:Farmer] 使用装备水桶容量取水: 5.0 (基础: 5.0 + 装备: 0)
[水井] 农夫请求取水: 5.0 单位，直接提供
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[FarmerTaskManager] 统一状态更新: MOVING -> CARRYING
[FarmerInteractionManager:Farmer] 统一状态更新: MOVING -> CARRYING
[FarmerTimerManager] 统一状态更新: MOVING -> CARRYING
[Farmer] 统一状态变更: MOVING -> CARRYING
[Farmer] 尝试显示携带图标，资源类型: water
[Farmer] DataManager返回的图标数据: {  }
[Farmer] 警告：无法找到任何携带图标，资源类型: water
[FarmerInteractionManager:Farmer] 成功取水 5.0 单位 (请求: 5.0)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 6)
[FarmerTaskManager] 进入空闲状态 - 准备新任务决策
[FarmerTaskManager] 统一状态更新: CARRYING -> IDLE
[FarmerInteractionManager:Farmer] 统一状态更新: CARRYING -> IDLE
[FarmerTimerManager] 统一状态更新: CARRYING -> IDLE
[Farmer] 统一状态变更: CARRYING -> IDLE
[WorkerCharacter] 侧面评分 - 左侧: 95.58, 右侧: 53.59, 选择: left
[WorkerCharacter] 智能选择交互侧面: left (目标: Farmland)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1244.0, 216.0), 侧面: left
[BaseInteractionManager] 开始交互: water, 目标: Farmland, 距离: 88.4, 目标位置: (1244.0, 216.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1244.0, 216.0), 侧面: left
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: IDLE -> MOVING
[FarmerInteractionManager:Farmer] 统一状态更新: IDLE -> MOVING
[FarmerTimerManager] 统一状态更新: IDLE -> MOVING
[Farmer] 统一状态变更: IDLE -> MOVING
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.781，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 3.7秒
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 3.7秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 3.7秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 3.7秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[BaseTaskManager] 生成随机值: 0.148，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (982.0505, 131.309)
[BaseTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.583，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (493.9398, 173.2028)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[Crop-wheat] [DEBUG] 作物 '小麦' 进入新阶段: mature
[FarmerInteractionManager:@Node2D@597] 使用装备水桶容量取水: 5.0 (基础: 5.0 + 装备: 0)
[水井] 农夫请求取水: 5.0 单位，直接提供
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[FarmerTaskManager] 统一状态更新: MOVING -> CARRYING
[FarmerInteractionManager:@Node2D@597] 统一状态更新: MOVING -> CARRYING
[FarmerTimerManager] 统一状态更新: MOVING -> CARRYING
[Farmer] 统一状态变更: MOVING -> CARRYING
[Farmer] 尝试显示携带图标，资源类型: water
[Farmer] DataManager返回的图标数据: {  }
[Farmer] 警告：无法找到任何携带图标，资源类型: water
[FarmerInteractionManager:@Node2D@597] 成功取水 5.0 单位 (请求: 5.0)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 6)
[FarmerTaskManager] 进入空闲状态 - 准备新任务决策
[FarmerTaskManager] 统一状态更新: CARRYING -> IDLE
[FarmerInteractionManager:@Node2D@597] 统一状态更新: CARRYING -> IDLE
[FarmerTimerManager] 统一状态更新: CARRYING -> IDLE
[Farmer] 统一状态变更: CARRYING -> IDLE
[WorkerCharacter] 侧面评分 - 左侧: 95.17, 右侧: 53.29, 选择: left
[WorkerCharacter] 智能选择交互侧面: left (目标: Farmland)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1244.0, 184.0), 侧面: left
[BaseInteractionManager] 开始交互: water, 目标: Farmland, 距离: 96.7, 目标位置: (1244.0, 184.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1244.0, 184.0), 侧面: left
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: IDLE -> MOVING
[FarmerInteractionManager:@Node2D@597] 统一状态更新: IDLE -> MOVING
[FarmerTimerManager] 统一状态更新: IDLE -> MOVING
[Farmer] 统一状态变更: IDLE -> MOVING
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: water, 目标: Farmland
[WorkerCharacter] 动画切换: water -> water (状态: WORKING_3)
[FarmerInteractionManager] 启动动作计时器: water_action, 预期时长: 1.2秒
[FarmerTimerManager] 使用预设动作时间: water_action = 1.2秒
[FarmerTimerManager] 🔧 计时器已启动: water_action, 实际时长: 1.2秒
[BaseInteractionManager] 交互执行结果: true
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.432，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (899.8871, 67.40765)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[Crop-wheat] [DEBUG] 作物 '小麦' 进入新阶段: growing2
[Crop-wheat] [DEBUG] 作物 '小麦' 已成熟!
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.008，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (353.1436, 74.91177)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.389，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (558.3029, 187.1333)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: fetch_water, 目标: Well
[WorkerCharacter] 动画切换: collecting -> collecting (状态: COLLECTING)
[FarmerInteractionManager] 启动动作计时器: fetch_water_action, 预期时长: 2.0秒
[FarmerTimerManager] 使用预设动作时间: fetch_water_action = 2.0秒
[FarmerTimerManager] 🔧 计时器已启动: fetch_water_action, 实际时长: 2.0秒
[BaseInteractionManager] 交互执行结果: true
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[BaseTaskManager] 生成随机值: 0.696，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1174.354, 84.36292)
[BaseTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[Crop-wheat] [DEBUG] 作物 '小麦' 已成熟!
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: water, 目标: Farmland
[FarmerTaskManager] 任务失败 - 类型: 6, 原因: 农田不需要浇水
[BaseInteractionManager] 交互执行结果: false
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 警告：尝试完成空任务
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.064，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (378.1638, 154.4734)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 1)
[WorkerCharacter] 动画切换: harvest -> harvest (状态: WORKING_1)
[FarmerTaskManager] 进入收获状态 - 调整任务优先级
[FarmerTaskManager] 统一状态更新: MOVING -> HARVESTING
[FarmerInteractionManager] 准备harvesting交互模式
[FarmerInteractionManager:@Node2D@597] 统一状态更新: MOVING -> HARVESTING
[FarmerTimerManager] 统一状态更新: MOVING -> HARVESTING
[Farmer] 统一状态变更: MOVING -> HARVESTING
[WorkerCharacter] 侧面评分 - 左侧: 100.00, 右侧: 58.00, 选择: left
[WorkerCharacter] 智能选择交互侧面: left (目标: Farmland)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1244.0, 184.0), 侧面: left
[BaseInteractionManager] 开始交互: harvest, 目标: Farmland, 距离: 0.0, 目标位置: (1244.0, 184.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1244.0, 184.0), 侧面: left
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: HARVESTING -> MOVING
[FarmerInteractionManager:@Node2D@597] 统一状态更新: HARVESTING -> MOVING
[FarmerTimerManager] 统一状态更新: HARVESTING -> MOVING
[Farmer] 统一状态变更: HARVESTING -> MOVING
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: harvest, 目标: Farmland
[WorkerCharacter] 动画切换: harvest -> harvest (状态: WORKING_1)
[FarmerInteractionManager] 启动动作计时器: harvest_action, 预期时长: 1.8秒
[FarmerTimerManager] 使用预设动作时间: harvest_action = 1.8秒
[FarmerTimerManager] 🔧 计时器已启动: harvest_action, 实际时长: 1.8秒
[BaseInteractionManager] 交互执行结果: true
[BaseTaskManager] 生成随机值: 0.796，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 4.6秒
[BaseTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 4.6秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 4.6秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 4.6秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[BaseTaskManager] 生成随机值: 0.605，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1010.891, 144.6859)
[BaseTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 任务失败 - 类型: 6, 原因: 浇水失败: 农田状态不正确
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.807，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 2.7秒
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 2.7秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 2.7秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 2.7秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 1)
[WorkerCharacter] 动画切换: harvest -> harvest (状态: WORKING_1)
[FarmerTaskManager] 进入收获状态 - 调整任务优先级
[FarmerTaskManager] 统一状态更新: MOVING -> HARVESTING
[FarmerInteractionManager] 准备harvesting交互模式
[FarmerInteractionManager:Farmer] 统一状态更新: MOVING -> HARVESTING
[FarmerTimerManager] 统一状态更新: MOVING -> HARVESTING
[Farmer] 统一状态变更: MOVING -> HARVESTING
[WorkerCharacter] 侧面评分 - 左侧: 100.00, 右侧: 58.00, 选择: left
[WorkerCharacter] 智能选择交互侧面: left (目标: Farmland)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1244.0, 216.0), 侧面: left
[BaseInteractionManager] 开始交互: harvest, 目标: Farmland, 距离: 0.0, 目标位置: (1244.0, 216.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1244.0, 216.0), 侧面: left
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: HARVESTING -> MOVING
[FarmerInteractionManager:Farmer] 统一状态更新: HARVESTING -> MOVING
[FarmerTimerManager] 统一状态更新: HARVESTING -> MOVING
[Farmer] 统一状态变更: HARVESTING -> MOVING
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: harvest, 目标: Farmland
[WorkerCharacter] 动画切换: harvest -> harvest (状态: WORKING_1)
[FarmerInteractionManager] 启动动作计时器: harvest_action, 预期时长: 1.8秒
[FarmerTimerManager] 使用预设动作时间: harvest_action = 1.8秒
[FarmerTimerManager] 🔧 计时器已启动: harvest_action, 实际时长: 1.8秒
[BaseInteractionManager] 交互执行结果: true
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.545，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (849.4583, 171.5354)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.348，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (294.3104, 232.0372)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.716，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 2.7秒
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 2.7秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 2.7秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 2.7秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[FarmerInteractionManager:@Node2D@621] 使用装备水桶容量取水: 5.0 (基础: 5.0 + 装备: 0)
[水井] 农夫请求取水: 5.0 单位，直接提供
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[FarmerTaskManager] 统一状态更新: MOVING -> CARRYING
[FarmerInteractionManager:@Node2D@621] 统一状态更新: MOVING -> CARRYING
[FarmerTimerManager] 统一状态更新: MOVING -> CARRYING
[Farmer] 统一状态变更: MOVING -> CARRYING
[Farmer] 尝试显示携带图标，资源类型: water
[Farmer] DataManager返回的图标数据: {  }
[Farmer] 警告：无法找到任何携带图标，资源类型: water
[FarmerInteractionManager:@Node2D@621] 成功取水 5.0 单位 (请求: 5.0)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 6)
[FarmerTaskManager] 进入空闲状态 - 准备新任务决策
[FarmerTaskManager] 统一状态更新: CARRYING -> IDLE
[FarmerInteractionManager:@Node2D@621] 统一状态更新: CARRYING -> IDLE
[FarmerTimerManager] 统一状态更新: CARRYING -> IDLE
[Farmer] 统一状态变更: CARRYING -> IDLE
[WorkerCharacter] 侧面评分 - 左侧: 96.56, 右侧: 54.80, 选择: left
[WorkerCharacter] 智能选择交互侧面: left (目标: Farmland)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1212.0, 184.0), 侧面: left
[BaseInteractionManager] 开始交互: water, 目标: Farmland, 距离: 68.8, 目标位置: (1212.0, 184.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1212.0, 184.0), 侧面: left
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: IDLE -> MOVING
[FarmerInteractionManager:@Node2D@621] 统一状态更新: IDLE -> MOVING
[FarmerTimerManager] 统一状态更新: IDLE -> MOVING
[Farmer] 统一状态变更: IDLE -> MOVING
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[Crop-wheat] [DEBUG] 作物 '小麦' 被收获，产出: wheat_item x2
[Crop-wheat] [DEBUG] 作物 '小麦' 被移除。玩家操作: false
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
CoreTech: 动作连贯性 - 立即请求收集任务
CoreTech: 动作连贯性 - 立即请求存储任务
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 4)
[FarmerTaskManager] 任务失败 - 类型: 4, 原因: 携带的不是作物资源
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.694，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (929.3427, 64.05724)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.186，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (758.3026, 143.1566)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[BaseTaskManager] 生成随机值: 0.198，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (985.316, 81.14263)
[BaseTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 2)
[WorkerCharacter] 动画切换: plant -> plant (状态: WORKING_2)
[FarmerTaskManager] 进入种植状态 - 调整任务优先级
[FarmerTaskManager] 统一状态更新: MOVING -> PLANTING
[FarmerInteractionManager] 准备planting交互模式
[FarmerInteractionManager:@Node2D@597] 统一状态更新: MOVING -> PLANTING
[FarmerTimerManager] 统一状态更新: MOVING -> PLANTING
[Farmer] 统一状态变更: MOVING -> PLANTING
[WorkerCharacter] 侧面评分 - 左侧: 100.00, 右侧: 58.00, 选择: left
[WorkerCharacter] 智能选择交互侧面: left (目标: Farmland)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1244.0, 184.0), 侧面: left
[BaseInteractionManager] 开始交互: plant, 目标: Farmland, 距离: 0.0, 目标位置: (1244.0, 184.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1244.0, 184.0), 侧面: left
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: PLANTING -> MOVING
[FarmerInteractionManager:@Node2D@597] 统一状态更新: PLANTING -> MOVING
[FarmerTimerManager] 统一状态更新: PLANTING -> MOVING
[Farmer] 统一状态变更: PLANTING -> MOVING
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: plant, 目标: Farmland
[WorkerCharacter] 动画切换: plant -> plant (状态: WORKING_2)
[FarmerInteractionManager] 启动动作计时器: plant_action, 预期时长: 1.5秒
[FarmerTimerManager] 使用预设动作时间: plant_action = 1.5秒
[FarmerTimerManager] 🔧 计时器已启动: plant_action, 实际时长: 1.5秒
[BaseInteractionManager] 交互执行结果: true
[Crop-wheat] [DEBUG] 作物 '小麦' 被收获，产出: wheat_item x2
[Crop-wheat] [DEBUG] 作物 '小麦' 被移除。玩家操作: false
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
CoreTech: 动作连贯性 - 立即请求收集任务
CoreTech: 动作连贯性 - 立即请求存储任务
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 4)
[FarmerTaskManager] 任务失败 - 类型: 4, 原因: 携带的不是作物资源
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 2)
[WorkerCharacter] 动画切换: plant -> plant (状态: WORKING_2)
[FarmerTaskManager] 进入种植状态 - 调整任务优先级
[FarmerTaskManager] 统一状态更新: MOVING -> PLANTING
[FarmerInteractionManager] 准备planting交互模式
[FarmerInteractionManager:Farmer] 统一状态更新: MOVING -> PLANTING
[FarmerTimerManager] 统一状态更新: MOVING -> PLANTING
[Farmer] 统一状态变更: MOVING -> PLANTING
[WorkerCharacter] 侧面评分 - 左侧: 100.00, 右侧: 58.00, 选择: left
[WorkerCharacter] 智能选择交互侧面: left (目标: Farmland)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1244.0, 216.0), 侧面: left
[BaseInteractionManager] 开始交互: plant, 目标: Farmland, 距离: 0.0, 目标位置: (1244.0, 216.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1244.0, 216.0), 侧面: left
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: PLANTING -> MOVING
[FarmerInteractionManager:Farmer] 统一状态更新: PLANTING -> MOVING
[FarmerTimerManager] 统一状态更新: PLANTING -> MOVING
[Farmer] 统一状态变更: PLANTING -> MOVING
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: plant, 目标: Farmland
[WorkerCharacter] 动画切换: plant -> plant (状态: WORKING_2)
[FarmerInteractionManager] 启动动作计时器: plant_action, 预期时长: 1.5秒
[FarmerTimerManager] 使用预设动作时间: plant_action = 1.5秒
[FarmerTimerManager] 🔧 计时器已启动: plant_action, 实际时长: 1.5秒
[BaseInteractionManager] 交互执行结果: true
[Crop-wheat] [DEBUG] 作物 '小麦' 已成熟!
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.744，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 2.1秒
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 2.1秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 2.1秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 2.1秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: water, 目标: Farmland
[FarmerTaskManager] 任务失败 - 类型: 6, 原因: 农田不需要浇水
[BaseInteractionManager] 交互执行结果: false
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 警告：尝试完成空任务
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 1)
[WorkerCharacter] 动画切换: harvest -> harvest (状态: WORKING_1)
[FarmerTaskManager] 进入收获状态 - 调整任务优先级
[FarmerTaskManager] 统一状态更新: MOVING -> HARVESTING
[FarmerInteractionManager] 准备harvesting交互模式
[FarmerInteractionManager:@Node2D@621] 统一状态更新: MOVING -> HARVESTING
[FarmerTimerManager] 统一状态更新: MOVING -> HARVESTING
[Farmer] 统一状态变更: MOVING -> HARVESTING
[WorkerCharacter] 侧面评分 - 左侧: 100.00, 右侧: 58.00, 选择: left
[WorkerCharacter] 智能选择交互侧面: left (目标: Farmland)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1212.0, 184.0), 侧面: left
[BaseInteractionManager] 开始交互: harvest, 目标: Farmland, 距离: 0.0, 目标位置: (1212.0, 184.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1212.0, 184.0), 侧面: left
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: HARVESTING -> MOVING
[FarmerInteractionManager:@Node2D@621] 统一状态更新: HARVESTING -> MOVING
[FarmerTimerManager] 统一状态更新: HARVESTING -> MOVING
[Farmer] 统一状态变更: HARVESTING -> MOVING
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: harvest, 目标: Farmland
[WorkerCharacter] 动画切换: harvest -> harvest (状态: WORKING_1)
[FarmerInteractionManager] 启动动作计时器: harvest_action, 预期时长: 1.8秒
[FarmerTimerManager] 使用预设动作时间: harvest_action = 1.8秒
[FarmerTimerManager] 🔧 计时器已启动: harvest_action, 实际时长: 1.8秒
[BaseInteractionManager] 交互执行结果: true
[Crop-wheat] [DEBUG] 作物 '小麦' 进入新阶段: mature
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.101，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (965.8109, -52.41938)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[BaseTaskManager] 生成随机值: 0.508，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (618.6635, 280.7994)
[BaseTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.553，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (707.0773, 184.5865)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.480，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1030.451, 31.50801)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 5)
[WorkerCharacter] 动画切换: collecting -> collecting (状态: COLLECTING)
[FarmerTaskManager] 统一状态更新: MOVING -> FETCHING_WATER
[FarmerInteractionManager:@Node2D@667] 统一状态更新: MOVING -> FETCHING_WATER
[FarmerTimerManager] 统一状态更新: MOVING -> FETCHING_WATER
[Farmer] 统一状态变更: MOVING -> FETCHING_WATER
[WorkerCharacter] 侧面评分 - 左侧: 86.52, 右侧: 46.52, 选择: left
[WorkerCharacter] 智能选择交互侧面: left (目标: Well)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1156.0, 224.0), 侧面: left
[BaseInteractionManager] 开始交互: fetch_water, 目标: Well, 距离: 269.6, 目标位置: (1156.0, 224.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1156.0, 224.0), 侧面: left
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: FETCHING_WATER -> MOVING
[FarmerInteractionManager:@Node2D@667] 统一状态更新: FETCHING_WATER -> MOVING
[FarmerTimerManager] 统一状态更新: FETCHING_WATER -> MOVING
[Farmer] 统一状态变更: FETCHING_WATER -> MOVING
[Crop-wheat] [DEBUG] 作物初始化完成: 小麦
[FarmerInteractionManager:@Node2D@597] 成功种植作物: wheat
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 6)
[FarmerTaskManager] 进入空闲状态 - 准备新任务决策
[FarmerTaskManager] 统一状态更新: MOVING -> IDLE
[FarmerInteractionManager:@Node2D@597] 统一状态更新: MOVING -> IDLE
[FarmerTimerManager] 统一状态更新: MOVING -> IDLE
[Farmer] 统一状态变更: MOVING -> IDLE
[WorkerCharacter] 侧面评分 - 左侧: 100.00, 右侧: 58.00, 选择: left
[WorkerCharacter] 智能选择交互侧面: left (目标: Farmland)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1244.0, 184.0), 侧面: left
[BaseInteractionManager] 开始交互: water, 目标: Farmland, 距离: 0.0, 目标位置: (1244.0, 184.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1244.0, 184.0), 侧面: left
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: IDLE -> MOVING
[FarmerInteractionManager:@Node2D@597] 统一状态更新: IDLE -> MOVING
[FarmerTimerManager] 统一状态更新: IDLE -> MOVING
[Farmer] 统一状态变更: IDLE -> MOVING
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: water, 目标: Farmland
[WorkerCharacter] 动画切换: water -> water (状态: WORKING_3)
[FarmerInteractionManager] 启动动作计时器: water_action, 预期时长: 1.2秒
[FarmerTimerManager] 使用预设动作时间: water_action = 1.2秒
[FarmerTimerManager] 🔧 计时器已启动: water_action, 实际时长: 1.2秒
[BaseInteractionManager] 交互执行结果: true
[Crop-wheat] [DEBUG] 作物初始化完成: 小麦
[FarmerInteractionManager:Farmer] 成功种植作物: wheat
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 6)
[FarmerTaskManager] 进入空闲状态 - 准备新任务决策
[FarmerTaskManager] 统一状态更新: MOVING -> IDLE
[FarmerInteractionManager:Farmer] 统一状态更新: MOVING -> IDLE
[FarmerTimerManager] 统一状态更新: MOVING -> IDLE
[Farmer] 统一状态变更: MOVING -> IDLE
[WorkerCharacter] 侧面评分 - 左侧: 100.00, 右侧: 58.00, 选择: left
[WorkerCharacter] 智能选择交互侧面: left (目标: Farmland)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1244.0, 216.0), 侧面: left
[BaseInteractionManager] 开始交互: water, 目标: Farmland, 距离: 0.0, 目标位置: (1244.0, 216.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1244.0, 216.0), 侧面: left
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: IDLE -> MOVING
[FarmerInteractionManager:Farmer] 统一状态更新: IDLE -> MOVING
[FarmerTimerManager] 统一状态更新: IDLE -> MOVING
[Farmer] 统一状态变更: IDLE -> MOVING
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: water, 目标: Farmland
[WorkerCharacter] 动画切换: water -> water (状态: WORKING_3)
[FarmerInteractionManager] 启动动作计时器: water_action, 预期时长: 1.2秒
[FarmerTimerManager] 使用预设动作时间: water_action = 1.2秒
[FarmerTimerManager] 🔧 计时器已启动: water_action, 实际时长: 1.2秒
[BaseInteractionManager] 交互执行结果: true
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.993，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 3.0秒
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 3.0秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 3.0秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 3.0秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[BaseTaskManager] 生成随机值: 0.285，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1115.07, 128.0139)
[BaseTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.376，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (671.6495, 333.3884)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[Crop-wheat] [DEBUG] 作物 '小麦' 被收获，产出: wheat_item x3
[Crop-wheat] [DEBUG] 作物 '小麦' 被移除。玩家操作: false
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
CoreTech: 动作连贯性 - 立即请求收集任务
CoreTech: 动作连贯性 - 立即请求存储任务
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.411，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (835.8043, 176.1175)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 4)
[FarmerTaskManager] 任务失败 - 类型: 4, 原因: 携带的不是作物资源
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 2)
[WorkerCharacter] 动画切换: plant -> plant (状态: WORKING_2)
[FarmerTaskManager] 进入种植状态 - 调整任务优先级
[FarmerTaskManager] 统一状态更新: MOVING -> PLANTING
[FarmerInteractionManager] 准备planting交互模式
[FarmerInteractionManager:@Node2D@621] 统一状态更新: MOVING -> PLANTING
[FarmerTimerManager] 统一状态更新: MOVING -> PLANTING
[Farmer] 统一状态变更: MOVING -> PLANTING
[WorkerCharacter] 侧面评分 - 左侧: 100.00, 右侧: 58.00, 选择: left
[WorkerCharacter] 智能选择交互侧面: left (目标: Farmland)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1212.0, 184.0), 侧面: left
[BaseInteractionManager] 开始交互: plant, 目标: Farmland, 距离: 0.0, 目标位置: (1212.0, 184.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1212.0, 184.0), 侧面: left
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: PLANTING -> MOVING
[FarmerInteractionManager:@Node2D@621] 统一状态更新: PLANTING -> MOVING
[FarmerTimerManager] 统一状态更新: PLANTING -> MOVING
[Farmer] 统一状态变更: PLANTING -> MOVING
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: plant, 目标: Farmland
[WorkerCharacter] 动画切换: plant -> plant (状态: WORKING_2)
[FarmerInteractionManager] 启动动作计时器: plant_action, 预期时长: 1.5秒
[FarmerTimerManager] 使用预设动作时间: plant_action = 1.5秒
[FarmerTimerManager] 🔧 计时器已启动: plant_action, 实际时长: 1.5秒
[BaseInteractionManager] 交互执行结果: true
[BaseTaskManager] 生成随机值: 0.703，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 4.5秒
[BaseTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 4.5秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 4.5秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 4.5秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[BaseTaskManager] 生成随机值: 0.955，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 3.8秒
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.679，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (652.9321, 143.2827)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 3.8秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 3.8秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 3.8秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerInteractionManager:@Node2D@597] 成功浇水 5.0 单位
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.316，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1168.93, 156.6559)
[BaseTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerInteractionManager:Farmer] 成功浇水 5.0 单位
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.398，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1316.016, 256.6789)
[BaseTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.436，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1212.889, 102.57)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.618，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1262.505, 148.0954)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.207，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (722.0421, 186.5569)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[Crop-wheat] [DEBUG] 作物初始化完成: 小麦
[FarmerInteractionManager:@Node2D@621] 成功种植作物: wheat
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 6)
[FarmerTaskManager] 进入空闲状态 - 准备新任务决策
[FarmerTaskManager] 统一状态更新: MOVING -> IDLE
[FarmerInteractionManager:@Node2D@621] 统一状态更新: MOVING -> IDLE
[FarmerTimerManager] 统一状态更新: MOVING -> IDLE
[Farmer] 统一状态变更: MOVING -> IDLE
[WorkerCharacter] 侧面评分 - 左侧: 100.00, 右侧: 58.00, 选择: left
[WorkerCharacter] 智能选择交互侧面: left (目标: Farmland)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1212.0, 184.0), 侧面: left
[BaseInteractionManager] 开始交互: water, 目标: Farmland, 距离: 0.0, 目标位置: (1212.0, 184.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1212.0, 184.0), 侧面: left
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: IDLE -> MOVING
[FarmerInteractionManager:@Node2D@621] 统一状态更新: IDLE -> MOVING
[FarmerTimerManager] 统一状态更新: IDLE -> MOVING
[Farmer] 统一状态变更: IDLE -> MOVING
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.246，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1395.367, 211.3956)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: water, 目标: Farmland
[WorkerCharacter] 动画切换: water -> water (状态: WORKING_3)
[FarmerInteractionManager] 启动动作计时器: water_action, 预期时长: 1.2秒
[FarmerTimerManager] 使用预设动作时间: water_action = 1.2秒
[FarmerTimerManager] 🔧 计时器已启动: water_action, 实际时长: 1.2秒
[BaseInteractionManager] 交互执行结果: true
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.486，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1270.674, 52.72723)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.284，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (676.0274, 43.65085)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[Crop-wheat] [DEBUG] 作物 '小麦' 已成熟!
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.754，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 5.0秒
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 5.0秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 5.0秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 5.0秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[BaseTaskManager] 生成随机值: 0.142，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1087.467, 9.541206)
[BaseTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.664，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1204.976, 247.1028)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: MOVING
[BaseInteractionManager] 执行交互: fetch_water, 目标: Well
[WorkerCharacter] 动画切换: collecting -> collecting (状态: COLLECTING)
[FarmerInteractionManager] 启动动作计时器: fetch_water_action, 预期时长: 2.0秒
[FarmerTimerManager] 使用预设动作时间: fetch_water_action = 2.0秒
[FarmerTimerManager] 🔧 计时器已启动: fetch_water_action, 实际时长: 2.0秒
[BaseInteractionManager] 交互执行结果: true
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.452，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1157.207, 37.7327)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.881，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 2.3秒
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 2.3秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 2.3秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 2.3秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.164，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1464.913, 297.8911)
[BaseTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerInteractionManager:@Node2D@621] 成功浇水 5.0 单位
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[BaseTaskManager] 生成随机值: 0.636，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1315.568, 182.2035)
[BaseTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
(2) [Crop-wheat] [DEBUG] 作物 '小麦' 进入新阶段: growing1
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.483，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1331.85, 170.4907)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.278，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (744.3036, 150.0549)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.888，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 2.4秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 2.4秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 2.4秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 2.4秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.311，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1190.026, 350.4189)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.260，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1328.518, 286.5268)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.198，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1183.913, 330.008)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.878，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 3.8秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 3.8秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 3.8秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 3.8秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[FarmerInteractionManager:@Node2D@667] 使用装备水桶容量取水: 5.0 (基础: 5.0 + 装备: 0)
[水井] 农夫请求取水: 5.0 单位，直接提供
[WorkerCharacter] 动画切换: walk_carry -> walk_carry (状态: CARRYING)
[FarmerTaskManager] 统一状态更新: MOVING -> CARRYING
[FarmerInteractionManager:@Node2D@667] 统一状态更新: MOVING -> CARRYING
[FarmerTimerManager] 统一状态更新: MOVING -> CARRYING
[Farmer] 统一状态变更: MOVING -> CARRYING
[Farmer] 尝试显示携带图标，资源类型: water
[Farmer] DataManager返回的图标数据: {  }
[Farmer] 警告：无法找到任何携带图标，资源类型: water
[FarmerInteractionManager:@Node2D@667] 成功取水 5.0 单位 (请求: 5.0)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[BaseTaskManager] 设置任务超时时间: 15.0秒 (任务类型: 1)
[WorkerCharacter] 动画切换: harvest -> harvest (状态: WORKING_1)
[FarmerTaskManager] 进入收获状态 - 调整任务优先级
[FarmerTaskManager] 统一状态更新: MOVING -> HARVESTING
[FarmerInteractionManager] 准备harvesting交互模式
[FarmerInteractionManager:@Node2D@654] 统一状态更新: MOVING -> HARVESTING
[FarmerTimerManager] 统一状态更新: MOVING -> HARVESTING
[Farmer] 统一状态变更: MOVING -> HARVESTING
[WorkerCharacter] 侧面评分 - 左侧: 85.00, 右侧: 45.00, 选择: left
[WorkerCharacter] 智能选择交互侧面: left (目标: Farmland)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1212.0, 216.0), 侧面: left
[BaseInteractionManager] 开始交互: harvest, 目标: Farmland, 距离: 917.8, 目标位置: (1212.0, 216.0)
[WorkerCharacter] 使用get_interaction_data成功: 位置(1212.0, 216.0), 侧面: left
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: HARVESTING -> MOVING
[FarmerInteractionManager:@Node2D@654] 统一状态更新: HARVESTING -> MOVING
[FarmerTimerManager] 统一状态更新: HARVESTING -> MOVING
[Farmer] 统一状态变更: HARVESTING -> MOVING
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.593，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1453.314, 199.0508)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 进入空闲状态 - 准备新任务决策
[FarmerTaskManager] 统一状态更新: CARRYING -> IDLE
[FarmerInteractionManager:@Node2D@667] 统一状态更新: CARRYING -> IDLE
[FarmerTimerManager] 统一状态更新: CARRYING -> IDLE
[Farmer] 统一状态变更: CARRYING -> IDLE
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.996，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 2.8秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 生成随机值: 0.977，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 2
[BaseTaskManager] 成功添加原地idle任务，持续时间: 2.4秒
[BaseTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 2.8秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 2.8秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 2.8秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.671，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1199.234, 165.6153)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.298，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (720.0156, 82.72409)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.398，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1277.973, 265.2153)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[Crop-wheat] [DEBUG] 作物 '小麦' 进入新阶段: growing1
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.398，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1437.64, 266.2813)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.946，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 4.7秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 4.7秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 4.7秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 4.7秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.844，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 2.6秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 2.6秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 2.6秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 2.6秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.036，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1231.271, 205.1578)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.743，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 3.3秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 3.3秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 3.3秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 3.3秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.824，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 3.0秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 3.0秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 3.0秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 3.0秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
(2) [Crop-wheat] [DEBUG] 作物 '小麦' 进入新阶段: growing2
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.605，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1204.388, 85.10051)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.491，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (751.6755, 182.9422)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 2.4秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 2.4秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 2.4秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.304，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1284.482, 110.2911)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.088，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1124.968, 186.3363)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.693，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (705.2075, 189.38)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.715，随机移动概率: 0.7
[BaseTaskManager] 添加原地idle任务 (30%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加原地idle任务，持续时间: 2.2秒
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 8)
[FarmerTaskManager] 🕒 开始idle任务，持续时间: 2.2秒
[FarmerTimerManager] 使用自定义动作时间: idle_task = 2.2秒
[FarmerTimerManager] 🔧 计时器已启动: idle_task, 实际时长: 2.2秒
[FarmerTaskManager] 🕒 使用TimerManager启动idle计时器
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.342，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1328.747, 205.853)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.143，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1501.636, 300.8158)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.337，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1402.003, 177.4676)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[Crop-wheat] [DEBUG] 作物 '小麦' 进入新阶段: growing2
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.310，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1213.118, 122.1297)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.402，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (720.6169, 121.0539)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.261，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (593.14, 163.8087)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.216，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1216.713, 104.8261)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 🕒 收到idle完成信号: idle_task
[FarmerTaskManager] 🕒 农夫idle任务正常完成
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.056，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1193.629, 99.3733)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[FarmerTaskManager] 统一状态更新: IDLE -> MOVING
[FarmerInteractionManager:@Node2D@667] 统一状态更新: IDLE -> MOVING
[FarmerTimerManager] 统一状态更新: IDLE -> MOVING
[Farmer] 统一状态变更: IDLE -> MOVING
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 没有可用工作目标，添加随机空闲任务
[BaseTaskManager] 生成随机值: 0.247，随机移动概率: 0.7
[BaseTaskManager] 添加随机走动任务 (70%概率)
[BaseTaskManager] 任务已添加到队列，队列长度: 1
[BaseTaskManager] 成功添加随机走动任务，目标位置: (1324.723, 259.8128)
[BaseTaskManager] 立即处理新添加的空闲任务
[FarmerTaskManager] 立即处理新添加的空闲任务
[WorkerCharacter] 处理移动完成
[WorkerCharacter] 是否委托给交互管理器: true
[WorkerCharacter] 委托给交互管理器处理移动完成
[BaseInteractionManager] 移动完成，当前状态: IDLE
[BaseInteractionManager] 状态不是MOVING，忽略移动完成事件
[BaseTaskManager] 设置任务超时时间: 20.0秒 (任务类型: 7)
[WorkerCharacter] 动画切换: walk -> walk (状态: MOVING)
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 进入空闲状态 - 准备新任务决策
[FarmerTaskManager] 统一状态更新: MOVING -> IDLE
[FarmerInteractionManager:@Node2D@681] 统一状态更新: MOVING -> IDLE
[FarmerTimerManager] 统一状态更新: MOVING -> IDLE
[Farmer] 统一状态变更: MOVING -> IDLE
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 进入空闲状态 - 准备新任务决策
[FarmerTaskManager] 统一状态更新: MOVING -> IDLE
[FarmerInteractionManager:@Node2D@667] 统一状态更新: MOVING -> IDLE
[FarmerTimerManager] 统一状态更新: MOVING -> IDLE
[Farmer] 统一状态变更: MOVING -> IDLE
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 进入空闲状态 - 准备新任务决策
[FarmerTaskManager] 统一状态更新: MOVING -> IDLE
[FarmerInteractionManager:@Node2D@654] 统一状态更新: MOVING -> IDLE
[FarmerTimerManager] 统一状态更新: MOVING -> IDLE
[Farmer] 统一状态变更: MOVING -> IDLE
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 进入空闲状态 - 准备新任务决策
[FarmerTaskManager] 统一状态更新: MOVING -> IDLE
[FarmerInteractionManager:@Node2D@642] 统一状态更新: MOVING -> IDLE
[FarmerTimerManager] 统一状态更新: MOVING -> IDLE
[Farmer] 统一状态变更: MOVING -> IDLE
[WorkerCharacter] 动画切换: idle -> idle (状态: IDLE)
[FarmerTaskManager] 进入空闲状态 - 准备新任务决策
[FarmerTaskManager] 统一状态更新: MOVING -> IDLE
[FarmerInteractionManager:@Node2D@631] 统一状态更新: MOVING -> IDLE
[FarmerTimerManager] 统一状态更新: MOVING -> IDLE
[Farmer] 统一状态变更: MOVING -> IDLE
[FarmerTaskManager] 进入空闲状态 - 准备新任务决策
[FarmerTaskManager] 统一状态更新: MOVING -> IDLE
[FarmerInteractionManager:@Node2D@621] 统一状态更新: MOVING -> IDLE
[FarmerTimerManager] 统一状态更新: MOVING -> IDLE
[Farmer] 统一状态变更: MOVING -> IDLE
